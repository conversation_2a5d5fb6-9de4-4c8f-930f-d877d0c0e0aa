import { type NextFunction, type Request, type Response } from 'express'

import { decorateLcidLogger } from '@emma/logger/create-server-logger'

import { createLogger } from '../../modules/app'

const logger = createLogger('logger-middleware')

export const loggerMiddleware = (
  _req: Request,
  _res: Response,
  next: NextFunction,
) => {
  return decorateLcidLogger(() => {
    logger.updateContext({
      t: Date.now(),
    })
    return next()
  })()
}
