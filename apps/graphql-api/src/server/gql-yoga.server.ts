import { useGraphQLSSE } from '@graphql-yoga/plugin-graphql-sse'
import { createYoga } from 'graphql-yoga'

import { createContext } from '../modules/server/interface/create-context'

import { websocketShutdown } from './infra/websocket-shutdown'

import type { JobManager } from '@emma/jobs'
import type { Request } from 'express'
import type { GraphQLSchema } from 'graphql'
import type http from 'http'
import type { PubSubInstance } from '../modules/pubsub/core/pubsub.instance'

export async function gqlYogaServer({
  pubSub,
  schema,
  jobManager,
  graphqlEndpoint,
  streamEndpoint,
  httpServer,
}: {
  pubSub: PubSubInstance
  schema: GraphQLSchema
  jobManager: JobManager
  graphqlEndpoint: string
  streamEndpoint: string
  httpServer: http.Server
}) {
  // Create GraphQL Yoga server with SSE plugin
  const yoga = createYoga({
    schema,
    graphqlEndpoint,
    landingPage: false,
    context: async ({ req }: { req: Request }) => {
      const context = await createContext({
        pubSub,
        headers: req.headers,
        url: req.url,
        method: req.method,
        jobManager,
      })
      return context
    },
    plugins: [
      useGraphQLSSE({
        endpoint: streamEndpoint,
      }),
      // TODO: remove after migrate will be done
      websocketShutdown({
        schema,
        httpServer,
      }),
    ],
    graphiql: {
      subscriptionsProtocol: 'SSE',
    },
  })

  return yoga
}
