import { useServer } from 'graphql-ws/lib/use/ws'
import { WebSocketServer } from 'ws'

import { getAuth } from '../../modules/server/interface/get-auth'

import type { GraphQLSchema } from 'graphql'
import type http from 'http'
import type { GQLServerContext } from '../../modules/server/core/gql-server-context'

export function websocketShutdown({
  schema,
  httpServer,
}: {
  schema: GraphQLSchema
  httpServer: http.Server
}) {
  // Set up WebSocket server
  const wsServer = new WebSocketServer({
    server: httpServer,
    path: '/subscriptions',
  })

  const serverCleanup = useServer(
    {
      schema,
      context: (ctx: { connectionParams?: { Authorization?: string } }) => {
        const { auth, isExpired } = getAuth('GET', ctx.connectionParams || {})

        return {
          auth,
          mode: 'subscription',
          isExpired,
        } as {
          auth: GQLServerContext['auth']
        }
      },
    },
    wsServer,
  )

  return {
    async serverWillStart() {
      return {
        async drainServer() {
          await serverCleanup.dispose()
        },
      }
    },
  }
}
