import { hasAccess } from '../../../modules/acl/interface/has-access'

import type { ResolverData } from 'type-graphql'
import type { AclUserRoleValueObject } from '../../../modules/acl/core/acl-user-role.value-object'
import type { GQLServerContext } from '../../../modules/server/core/gql-server-context'

export const gqlAuthChecker = async (
  { context }: ResolverData<GQLServerContext>,
  roles: AclUserRoleValueObject[],
): Promise<boolean> => {
  return hasAccess({
    roles,
    context,
  })
}
