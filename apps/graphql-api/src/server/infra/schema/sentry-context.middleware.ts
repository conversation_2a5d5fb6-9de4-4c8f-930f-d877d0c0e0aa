import * as Sentry from '@sentry/node'

import { createLogger } from '../../../modules/app'

import type { MiddlewareFn } from 'type-graphql'
import type { GQLServerContext } from '../../../modules/server/core/gql-server-context'

const logger = createLogger('sentry-middleware')

export const SentryContextMiddleware: MiddlewareFn<GQLServerContext> = async (
  { context, info },
  next,
) => {
  const { auth } = context

  if (auth?.userUuid) {
    Sentry.setUser({
      companyUserUuid: auth?.companyUserUuid || undefined,
    })
  } else {
    Sentry.setUser(null)
  }

  logger.updateContext({
    operation: info?.operation?.operation,
    fieldName: info?.fieldName,
  })

  Sentry.setContext('request', {
    operation: info?.operation?.operation,
    fieldName: info?.fieldName,
    lcid: logger.rawLcid,
  })

  if (info?.operation) {
    Sentry.setContext('graphql', {
      operation: info.operation.operation,
      fieldName: info.fieldName,
      operationName: info.operation.name?.value,
    })
  }

  return next()
}
