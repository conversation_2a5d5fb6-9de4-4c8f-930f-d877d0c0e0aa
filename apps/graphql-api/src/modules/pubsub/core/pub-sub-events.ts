import type { Result } from '@repo/result'
import type { AiAssistantChunkDto } from '../../ai-assistant/dto/ai-assistant-chunk.dto'
import type { OpenAITokenDto } from '../../ai-message/dtos/open-ai-token.dto'
import type { OpenAITokenParsingErrorDto } from '../../ai-message/dtos/open-ai-token-parsing-error.dto'
import type { PubSubTokenDto } from '../../ai-message/dtos/pub-sub-token.dto'
import type { PubSubTokenParsingErrorDto } from '../../ai-message/dtos/pub-sub-token-parsing-error.dto'
import type { AiMessageDto } from '../../anthropic/dtos/ai-message.dto'
import type { AiEvents } from './ai-events'

// Define the events for PubSub
export type PubSubEvents = {
  [key: `AI_MESSAGE_STREAM_${string}`]: [
    {
      aiMessageStream: AiMessageDto
    },
  ]
  // @deprecated, will be removed later
  [key: `${AiEvents.AiMessageGeneration}:${string}`]: [
    {
      result: Result<OpenAITokenDto, OpenAITokenParsingErrorDto>
    },
  ]
  [key: `${AiEvents.CustomerMessage}:${string}`]: [
    {
      result: Result<PubSubTokenDto, PubSubTokenParsingErrorDto>
    },
  ]
  [key: `${AiEvents.AiAssistant}:${string}`]: [AiAssistantChunkDto]
}
