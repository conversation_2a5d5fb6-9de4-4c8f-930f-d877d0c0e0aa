import { createRedisEventTarget } from '@graphql-yoga/redis-event-target'
import { createPubSub } from '@graphql-yoga/subscription'
import { Redis } from 'ioredis'

import { monitorRedis } from '../infra/monitor-redis'

import type { PubSubEvents } from '../core/pub-sub-events'

export function createPubSubInstance() {
  const redisUrl = process.env.REDIS_URL || ''
  if (!redisUrl) {
    console.error(
      '[pubSub] !!!! REDIS_URL env variable is not defined. using default memory pubsub',
    )
    return createPubSub<PubSubEvents>()
  }

  if (process.env.NODE_ENV !== 'production') {
    monitorRedis(redisUrl)
  }

  const pubSub = createPubSub<PubSubEvents>({
    eventTarget: createRedisEventTarget({
      publishClient: new Redis(redisUrl, {
        retryStrategy: (times) => Math.max(times * 100, 3000),
      }),
      subscribeClient: new Redis(redisUrl, {
        retryStrategy: (times) => Math.max(times * 100, 3000),
      }),
    }),
  })
  console.log('[pubSub] created using Redis connection')

  return pubSub
}
