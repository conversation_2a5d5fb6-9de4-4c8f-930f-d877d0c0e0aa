import { createLogger } from '../../../app'

const logger = createLogger('mcp.client: parseToolParams')

export function parseToolParams(params: unknown): Record<string, unknown> {
  if (typeof params === 'string') {
    try {
      return JSON.parse(params)
    } catch (e) {
      logger.warn('Failed to parse string params:', params)
      return {}
    }
  } else if (params && typeof params === 'object') {
    return { ...params }
  }
  return {}
}
