export function attemptToFixIncompleteJson(
  partialJson: string,
): Record<string, unknown> | null {
  try {
    // Handle case where JSON has a query parameter but might be malformed
    const queryMatch = partialJson.match(/"query"\s*:\s*"([^"]+)"/)
    if (queryMatch?.[1]) {
      return { query: queryMatch[1] }
    }

    // Handle other parameters - can be expanded for other common fields
    const contactIdMatch = partialJson.match(/"id"\s*:\s*"([^"]+)"/)
    if (contactIdMatch?.[1]) {
      return { id: contactIdMatch[1] }
    }

    return null
  } catch (error) {
    console.warn('[Anthropic] Failed to fix incomplete JSON:', error)
    return null
  }
}
