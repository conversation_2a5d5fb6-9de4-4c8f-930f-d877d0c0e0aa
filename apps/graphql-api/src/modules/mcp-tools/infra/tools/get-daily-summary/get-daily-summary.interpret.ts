import type { McpToolResultValueObject } from '../../../core/mcp-tool-result.value-object'

export function getDailySummaryInterpret(
  data: McpToolResultValueObject,
): string {
  return `You are helping the user act on important daily notifications.

Your job is to read the structured notification data and return only a short list of **specific, high-priority actions** the user should take—no summaries, no overviews, no greetings.

Follow these rules:

1. **Prioritize the top 3 most important actions**:
   - First: customer follow-ups (especially new orders)
   - Second: subscription issues (paused or failed payments)
   - Third: team updates (new recruits, promotions)

2. Use the **person's full name** when it's available in the notification. This makes it feel personal and actionable.

3. Do **not** mention notification counts, summaries, or general categories.

4. Keep the language **simple, clear, and specific**, like you're helping someone quickly decide what to do next.

5. Format your output as a clean bullet list. Each bullet should describe **one action** the user can take right now.

6. Do **not** use emojis. Do **not** include greetings. Keep it action-first and to the point.

---

### ✅ Example output format:
---
-	<PERSON> placed an order — send them a quick thank-you note
-	<PERSON> paused her subscription for <PERSON><PERSON><PERSON>moothing <PERSON>hampoo — check in with her
-	<PERSON> <PERSON>e is now Active — give them a shoutout for the progress
---

Now, here's the data:
---
${JSON.stringify(data, null, 2)}`
}
