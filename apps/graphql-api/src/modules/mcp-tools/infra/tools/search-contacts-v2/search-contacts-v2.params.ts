import type { Mcp<PERSON><PERSON><PERSON>allValueObject } from '../../../core/mcp-tool-call.value-object'

export function searchContactsV2Params(
  toolInput: McpToolCallValueObject['input'],
) {
  let searchParams: Record<string, unknown> = {}

  // Extract and validate the input
  if (typeof toolInput === 'object' && toolInput !== null) {
    searchParams = toolInput as Record<string, unknown>
  } else if (typeof toolInput === 'string') {
    try {
      searchParams = JSON.parse(toolInput)
    } catch (error) {
      searchParams = {}
      console.warn('Failed to parse string input:', toolInput)
    }
  }

  searchParams.limit = 21

  // Verify if we have a query
  if (searchParams.query) {
    console.log('[processToolCall] Found query parameter:', searchParams.query)
  } else {
    console.warn('[processToolCall] No query parameter found in input')
  }

  console.log('[processToolCall] start search contacts' + '!!!')
  // Pass the token and correlation ID directly to the MCP client method

  return searchParams
}
