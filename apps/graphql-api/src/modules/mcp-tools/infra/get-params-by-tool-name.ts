import { getDailySummaryParams } from './tools/get-daily-summary/get-daily-summary.params'
import { searchContactsV2Params } from './tools/search-contacts-v2/search-contacts-v2.params'

import type { McpToolCallValueObject } from '../core/mcp-tool-call.value-object'

export function getParamsByToolName(toolCall: McpToolCallValueObject) {
  if (toolCall.name === 'search_contacts_v2') {
    return searchContactsV2Params(toolCall.input)
  }
  if (toolCall.name === 'get_daily_summary') {
    return getDailySummaryParams(toolCall.input)
  }
}
