import { v4 as uuidv4 } from 'uuid'

import { createLogger } from '../../app'
import { AnthropicClient } from '../infra/anthropic-client'
import { getParamsByToolName } from '../infra/get-params-by-tool-name'
import { McpClientService } from '../infra/mcp-client.service/mcp-client.service'
import { parseStream } from '../infra/parse-stream/parse-stream'
import { ProcessContent } from '../infra/process-content'

import type { MessageParam } from '@anthropic-ai/sdk/resources/index.mjs'
import type { McpToolResultValueObject } from '../core/mcp-tool-result.value-object'
import type { StreamToolUseBlockValueObject } from '../core/stream-tool-use-block-value.object'

type StreamMessageArgs = {
  prompt: string
  conversationId: string
  systemPrompt?: string
  userToken: string
  onPublish: (data: {
    id: string
    role: 'assistant' | 'user'
    content: string
    createdAt: Date
    isComplete: boolean
  }) => void
}

const logger = createLogger('anthropic-client.service')

export class AnthropicClientService {
  private readonly client: AnthropicClient
  private readonly processContent: ProcessContent
  private readonly mcpClientService: McpClientService
  // Track accumulated JSON for each index globally
  private activeToolInputs: Map<number, string> = new Map()
  // Store conversation history by conversationId
  private conversationHistory: Map<string, MessageParam[]> = new Map()

  constructor() {
    this.client = new AnthropicClient()
    this.processContent = new ProcessContent(this.client)
    this.mcpClientService = new McpClientService()
  }

  async streamMessage({
    prompt,
    conversationId,
    systemPrompt,
    userToken,
    onPublish,
  }: StreamMessageArgs): Promise<string> {
    try {
      const messageId = uuidv4()
      const timestamp = new Date()
      let accumulatedContent = ''

      console.log('conversationId', conversationId)

      // Publish initial empty message
      onPublish({
        id: messageId,
        role: 'assistant',
        content: '',
        createdAt: timestamp,
        isComplete: false,
      })

      // TODO: activeToolInputs is global object, for more than two streams, it will mixed. we cannot use more than one user.
      // Reset active tool inputs for this stream
      this.activeToolInputs = new Map()

      // Get existing conversation history or initialize a new one
      const messages = this.conversationHistory.get(conversationId) || []

      // Add the new user message
      const userMessage: MessageParam = { role: 'user', content: prompt }
      messages.push(userMessage)

      // Update our stored history with the new user message
      this.conversationHistory.set(conversationId, messages)

      const stream = await this.client.createStream({
        messages,
        systemPrompt,
      })

      // Track content blocks for tool use
      const contentBlocks: StreamToolUseBlockValueObject[] = []
      // Track which blocks have been completed/stopped
      const completedBlockIndices = new Set<number>()
      // Track accumulated input JSON for each content block
      const parsedInputs: Record<number, Record<string, unknown>> = {}

      for await (const chunk of stream) {
        const streamResult = parseStream({
          chunk,
          activeToolInputs: this.activeToolInputs,
          contentBlocks,
          parsedInputs,
          completedBlockIndices,
        })
        if (!streamResult) continue

        if (streamResult.type === 'msg') {
          accumulatedContent += streamResult.delta
          onPublish({
            id: messageId,
            role: 'assistant',
            content: accumulatedContent,
            createdAt: timestamp,
            isComplete: false,
          })
        }

        if (streamResult.type === 'tool') {
          const toolCall = streamResult.tool
          console.log(
            'Tool call request:',
            toolCall.name,
            JSON.stringify(toolCall.input),
          )

          const toolParams = getParamsByToolName(toolCall)

          // Log the tool call input for debugging
          logger.log('[process-tool-call] input-params', {
            name: toolCall.name,
            inputParams: JSON.stringify(toolCall.input),
            toolParams: toolParams,
          })
          let toolResult: McpToolResultValueObject | undefined
          if (toolParams) {
            toolResult = await this.mcpClientService.callTool({
              name: toolCall.name,
              params: toolParams,
              token: userToken,
            })
          }

          // Use the provided userToken from the GraphQL context
          // Process the tool call and update accumulated content
          const result = await this.processContent.processToolCall({
            toolResult,
            toolCall,
            initialContent: accumulatedContent,
          })

          accumulatedContent = result.accumulatedContent

          // Publish updated content
          onPublish({
            id: messageId,
            role: 'assistant',
            content: accumulatedContent,
            createdAt: timestamp,
            isComplete: false,
          })
        }
      }

      // After completing the stream, save the assistant's response to the conversation history
      if (accumulatedContent) {
        const assistantMessage: MessageParam = {
          role: 'assistant',
          content: accumulatedContent,
        }

        // Get the updated history and add the assistant's response
        const updatedHistory =
          this.conversationHistory.get(conversationId) || []
        updatedHistory.push(assistantMessage)

        // Save the updated history
        this.conversationHistory.set(conversationId, updatedHistory)

        // Trim history if it gets too long (optional, to prevent token limits)
        if (updatedHistory.length > 20) {
          this.conversationHistory.set(
            conversationId,
            updatedHistory.slice(-20), // Keep the most recent 20 messages
          )
        }
      }

      // Publish final message with complete flag
      onPublish({
        id: messageId,
        role: 'assistant',
        content: accumulatedContent,
        createdAt: timestamp,
        isComplete: true,
      })

      return messageId
    } catch (error) {
      console.error('Error streaming from Anthropic:', error)
      throw error
    }
  }
}
