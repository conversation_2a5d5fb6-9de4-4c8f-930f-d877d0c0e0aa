import { v4 as uuidv4 } from 'uuid'

import { createLogger } from '../../app'
import { AnthropicClient } from '../infra/anthropic-client'
import { getParamsByToolName } from '../infra/get-params-by-tool-name'
import { parseStream } from '../infra/parse-stream/parse-stream'
import { ProcessContent } from '../infra/process-content'

import type { MessageParam } from '@anthropic-ai/sdk/resources/index.mjs'
import type { McpToolResultValueObject } from '../core/mcp-tool-result.value-object'
import type { StreamCollectArgs } from '../core/stream-collect.args'
import type { StreamToolUseBlockValueObject } from '../core/stream-tool-use-block-value.object'

type Args = {
  message: string
  conversationUuid: string
  systemPrompt: string
  userToken: string
  onPublish: (data: {
    conversationUuid: string
    chunkUuid: string
    type: 'start' | 'process' | 'end' | 'error'
    role: 'assistant' | 'user'
    content?: {
      type: 'text'
      data: string
    }
    createdAt: Date
  }) => void
}

const logger = createLogger('ai-assistant-stream')

export class AiAssistantStreamService {
  private readonly processContent: ProcessContent
  constructor(private readonly anthropicClient = new AnthropicClient()) {
    this.processContent = new ProcessContent(this.anthropicClient)
  }

  async createStream({
    message,
    systemPrompt,
    onPublish,
    conversationUuid,
    userToken,
  }: Args) {
    const timestamp = new Date()
    const chunkUuid = uuidv4()
    // Track content blocks for tool use
    const contentBlocks: StreamToolUseBlockValueObject[] = []
    // Track which blocks have been completed/stopped
    const completedBlockIndices = new Set<number>()
    // Track accumulated input JSON for each content block
    const parsedInputs: Record<number, Record<string, unknown>> = {}
    const activeToolInputs = new Map()
    let accumulatedContent = ''

    onPublish({
      conversationUuid,
      chunkUuid,
      type: 'start',
      role: 'assistant',
      createdAt: timestamp,
    })

    const messages: MessageParam[] = [] // load prev messages by conversationUuid from redis
    messages.push({ role: 'user', content: message })

    logger.log('messages', messages)

    const stream = await this.anthropicClient.createStream({
      messages,
      systemPrompt,
    })

    const onCollect = async (data: StreamCollectArgs) => {
      if (data.type === 'msg') {
        accumulatedContent += data.delta
        onPublish({
          role: 'assistant',
          chunkUuid,
          conversationUuid,
          type: 'process',
          content: {
            type: 'text',
            data: data.delta,
          },
          createdAt: timestamp,
        })
      }
      if (data.type === 'tool') {
        const toolCall = data.tool

        const toolParams = getParamsByToolName(toolCall)

        // Log the tool call input for debugging
        logger.log('[process-tool-call] input-params', {
          name: toolCall.name,
          inputParams: JSON.stringify(toolCall.input),
          toolParams: toolParams,
        })
        let toolResult: McpToolResultValueObject | undefined
        if (toolParams) {
          // create client and call tool
          // toolResult = await this.mcpClientService.callTool({
          //   name: toolCall.name,
          //   params: toolParams,
          //   token: userToken
          // })
        }

        // Use the provided userToken from the GraphQL context
        // Process the tool call and update accumulated content
        const result = await this.processContent.processToolCall({
          toolResult,
          toolCall,
          initialContent: accumulatedContent,
        })

        accumulatedContent = result.accumulatedContent

        // Publish updated content
        onPublish({
          conversationUuid,
          chunkUuid,
          type: 'process',
          content: {
            type: 'text',
            data: result.accumulatedContent,
          },
          role: 'assistant',
          createdAt: timestamp,
        })
      }
    }

    for await (const chunk of stream) {
      const streamResult = parseStream({
        chunk,
        activeToolInputs,
        contentBlocks,
        parsedInputs,
        completedBlockIndices,
      })
      if (!streamResult) continue

      if (streamResult.type === 'msg') {
        accumulatedContent += streamResult.delta
        onPublish({
          conversationUuid,
          chunkUuid,
          type: 'process',
          role: 'assistant',
          content: {
            type: 'text',
            data: streamResult.delta,
          },
          createdAt: timestamp,
        })
      }

      if (streamResult.type === 'tool') {
        const toolCall = streamResult.tool
        console.log(
          'Tool call request:',
          toolCall.name,
          JSON.stringify(toolCall.input),
        )

        const toolParams = getParamsByToolName(toolCall)

        // Log the tool call input for debugging
        logger.log('[process-tool-call] input-params', {
          name: toolCall.name,
          inputParams: JSON.stringify(toolCall.input),
          toolParams: toolParams,
        })
        let toolResult: McpToolResultValueObject | undefined
        if (toolParams) {
          toolResult = await this.mcpClientService.callTool({
            name: toolCall.name,
            params: toolParams,
            token: userToken,
          })
        }

        // Use the provided userToken from the GraphQL context
        // Process the tool call and update accumulated content
        const result = await this.processContent.processToolCall({
          toolResult,
          toolCall,
          initialContent: accumulatedContent,
        })

        accumulatedContent = result.accumulatedContent

        // Publish updated content
        onPublish({
          conversationUuid,
          chunkUuid,
          type: 'process',
          role: 'assistant',
          content: {
            type: 'text',
            data: result.accumulatedContent,
          },
          createdAt: timestamp,
        })
      }
    }

    // end stream

    if (accumulatedContent) {
      const assistantMessage: MessageParam = {
        role: 'assistant',
        content: accumulatedContent,
      }

      // Get the updated history and add the assistant's response
      // const updatedHistory =
      //   this.conversationHistory.get(conversationId) || []
      // updatedHistory.push(assistantMessage)
      //
      // // Save the updated history
      // this.conversationHistory.set(conversationId, updatedHistory)
      //
      // // Trim history if it gets too long (optional, to prevent token limits)
      // if (updatedHistory.length > 20) {
      //   this.conversationHistory.set(
      //     conversationId,
      //     updatedHistory.slice(-20), // Keep the most recent 20 messages
      //   )
      // }
    }

    // Publish final message with complete flag
    onPublish({
      conversationUuid,
      chunkUuid,
      type: 'end',
      role: 'assistant',
      createdAt: timestamp,
    })
  }
}
