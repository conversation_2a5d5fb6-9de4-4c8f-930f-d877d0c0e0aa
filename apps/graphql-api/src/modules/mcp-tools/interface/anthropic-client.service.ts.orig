import { v4 as uuidv4 } from 'uuid'

import { createLogger } from '../../app'
import { AnthropicClient } from '../infra/anthropic-client'
import { getParamsByToolName } from '../infra/get-params-by-tool-name'
import { McpClientService } from '../infra/mcp-client.service/mcp-client.service'
import { parseStream } from '../infra/parse-stream/parse-stream'
import { ProcessContent } from '../infra/process-content'

import type { MessageParam } from '@anthropic-ai/sdk/resources/index.mjs'
import type { McpToolResultValueObject } from '../core/mcp-tool-result.value-object'
import type { StreamToolUseBlockValueObject } from '../core/stream-tool-use-block-value.object'

type StreamMessageArgs = {
  prompt: string
  conversationId: string
  systemPrompt?: string
  userToken: string
  onPublish: (data: {
    id: string
    role: 'assistant' | 'user'
    content: string
    createdAt: Date
    isComplete: boolean
  }) => void
}

const logger = createLogger('anthropic-client.service')

export class AnthropicClientService {
  private readonly client: AnthropicClient
  private readonly processContent: ProcessContent
  private readonly mcpClientService: McpClientService
  // Track accumulated JSON for each index globally
  private activeToolInputs: Map<number, string> = new Map()
  // Store conversation history by conversationId
  private conversationHistory: Map<string, MessageParam[]> = new Map()

  constructor() {
    this.client = new AnthropicClient()
    this.processContent = new ProcessContent(this.client)
    this.mcpClientService = new McpClientService()
  }

  async streamMessage({
    prompt,
    conversationId,
    systemPrompt,
    userToken,
    onPublish,
  }: StreamMessageArgs): Promise<string> {
    try {
      const messageId = uuidv4()
      const timestamp = new Date()
      let accumulatedContent = ''

      console.log('conversationId', conversationId)

      // Publish initial empty message
      onPublish({
        id: messageId,
        role: 'assistant',
        content: '',
        createdAt: timestamp,
        isComplete: false,
      })

      // TODO: activeToolInputs is global object, for more than two streams, it will mixed. we cannot use more than one user.
      // Reset active tool inputs for this stream
      this.activeToolInputs = new Map()

      // Get existing conversation history or initialize a new one
      const messages = this.conversationHistory.get(conversationId) || []

      // Add the new user message
      const userMessage: MessageParam = { role: 'user', content: prompt }
      messages.push(userMessage)

      // Update our stored history with the new user message
      this.conversationHistory.set(conversationId, messages)

      const stream = await this.client.createStream({
        messages,
        systemPrompt,
      })

      // Track content blocks for tool use
      const contentBlocks: StreamToolUseBlockValueObject[] = []
      // Track which blocks have been completed/stopped
      const completedBlockIndices = new Set<number>()
      // Track accumulated input JSON for each content block
      const parsedInputs: Record<number, Record<string, unknown>> = {}

      for await (const chunk of stream) {
        const streamResult = parseStream({
          chunk,
          activeToolInputs: this.activeToolInputs,
          contentBlocks,
          parsedInputs,
          completedBlockIndices,
        })
        if (!streamResult) continue

        if (streamResult.type === 'msg') {
          accumulatedContent += streamResult.delta
          onPublish({
            id: messageId,
            role: 'assistant',
            content: accumulatedContent,
            createdAt: timestamp,
            isComplete: false,
          })
        }

        if (streamResult.type === 'tool') {
          const toolCall = streamResult.tool
          console.log(
            'Tool call request:',
            toolCall.name,
            JSON.stringify(toolCall.input),
          )

          const toolParams = getParamsByToolName(toolCall)

<<<<<<< HEAD
          // Log the tool call input for debugging
          logger.log('[process-tool-call] input-params', {
            name: toolCall.name,
            inputParams: JSON.stringify(toolCall.input),
            toolParams: toolParams,
          })
          let toolResult: McpToolResultValueObject | undefined
          if (toolParams) {
            toolResult = await this.mcpClientService.callTool({
              name: toolCall.name,
              params: toolParams,
              token: userToken,
=======
          try {
            // Use the most recent tool_use block
            if (contentBlocks.length > 0) {
              const lastToolBlock = contentBlocks[contentBlocks.length - 1]
              console.log(
                '[Anthropic] Last tool block:',
                JSON.stringify(lastToolBlock),
              )

              // First try to use the parsed input from the content_block_stop event
              let toolName = lastToolBlock.name
              let toolArgs: Record<string, unknown> = {}

              // Priority 1: Find the last completed block with parsed input
              const lastCompletedIndices = Array.from(completedBlockIndices)
              if (lastCompletedIndices.length > 0) {
                const lastCompletedIndex = Math.max(...lastCompletedIndices)
                if (
                  lastCompletedIndex >= 0 &&
                  parsedInputs[lastCompletedIndex]
                ) {
                  toolArgs = parsedInputs[lastCompletedIndex]
                  toolName = contentBlocks[lastCompletedIndex].name
                  console.log(
                    `[Anthropic] Using parsed input from block ${lastCompletedIndex}:`,
                    toolArgs,
                  )
                } else if (
                  contentBlocks[lastCompletedIndex]?.input &&
                  Object.keys(contentBlocks[lastCompletedIndex].input).length >
                    0
                ) {
                  // If we have input directly on the content block, use that
                  toolArgs = contentBlocks[lastCompletedIndex].input
                  toolName = contentBlocks[lastCompletedIndex].name
                  console.log(
                    `[Anthropic] Using input directly from block ${lastCompletedIndex}:`,
                    toolArgs,
                  )
                }
              }

              // Priority 2: Use the accumulated JSON from our active tool inputs map
              if (Object.keys(toolArgs).length === 0) {
                // Find the last block with accumulated JSON
                let lastInputIndex = -1
                let fullestInput = ''

                for (const [
                  indexStr,
                  json,
                ] of this.activeToolInputs.entries()) {
                  const index = Number(indexStr)
                  // Find the most complete accumulated JSON
                  if (json && json.length > fullestInput.length) {
                    fullestInput = json
                    lastInputIndex = index
                  }
                }

                if (lastInputIndex >= 0 && fullestInput) {
                  console.log(
                    `[Anthropic] Found accumulated JSON from activeToolInputs for index ${
                      lastInputIndex
                    }:`,
                    fullestInput,
                  )

                  try {
                    // Try to parse the accumulated JSON
                    const cleanInput = fullestInput.trim()
                    if (
                      cleanInput.startsWith('{') &&
                      cleanInput.endsWith('}')
                    ) {
                      toolArgs = JSON.parse(cleanInput)
                      console.log(
                        '[Anthropic] Parsed accumulated JSON for tool call:',
                        toolArgs,
                      )

                      // If we have a valid index in contentBlocks, update toolName
                      if (contentBlocks.length > lastInputIndex) {
                        toolName = contentBlocks[lastInputIndex].name
                      }
                    } else {
                      // Try to extract specific fields if JSON is incomplete
                      const extractedArgs =
                        attemptToFixIncompleteJson(cleanInput)
                      if (extractedArgs) {
                        toolArgs = extractedArgs
                        console.log(
                          '[Anthropic] Extracted parameters from accumulated JSON:',
                          toolArgs,
                        )
                      }
                    }
                  } catch (e) {
                    console.log(
                      '[Anthropic] Error parsing accumulated JSON:',
                      e,
                    )
                  }
                }
              }

              // Priority 3: Fallback to previous methods if neither of the above worked
              if (Object.keys(toolArgs).length === 0) {
                // Find the block with the most complete partial JSON
                let mostCompleteJsonBlock = lastToolBlock
                let mostCompleteJson = lastToolBlock.partialInputJson || ''

                for (const block of contentBlocks) {
                  if (
                    block.partialInputJson &&
                    block.partialInputJson.length > mostCompleteJson.length
                  ) {
                    mostCompleteJson = block.partialInputJson
                    mostCompleteJsonBlock = block
                  }
                }

                console.log(
                  '[Anthropic] Most complete JSON found:',
                  mostCompleteJson,
                )

                // Access tool information
                toolName = mostCompleteJsonBlock.name

                // Parse the JSON from the partial input
                if (mostCompleteJson) {
                  try {
                    // Per Anthropic docs, input_json_delta gives partial JSON strings
                    // that should be accumulated and then parsed when complete
                    const cleanJson = mostCompleteJson.trim()

                    // Check if the JSON is complete (has opening and closing braces)
                    const isCompleteJson =
                      cleanJson.startsWith('{') && cleanJson.endsWith('}')

                    if (isCompleteJson) {
                      try {
                        // Parse the complete JSON
                        toolArgs = JSON.parse(cleanJson)
                        console.log(
                          '[Anthropic] Parsed complete JSON object:',
                          toolArgs,
                        )
                      } catch (e) {
                        console.log(
                          '[Anthropic] JSON parsing failed, will try to extract fields:',
                          e,
                        )

                        // Extract query parameter if it exists
                        const queryMatch = cleanJson.match(
                          /"query"\s*:\s*"([^"]+)"/,
                        )
                        if (queryMatch?.[1]) {
                          toolArgs.query = queryMatch[1]
                          console.log(
                            '[Anthropic] Extracted query parameter:',
                            toolArgs.query,
                          )
                        }
                      }
                    } else {
                      // Handle incomplete JSON by looking for specific patterns
                      const queryMatch = cleanJson.match(
                        /"query"\s*:\s*"([^"]+)"/,
                      )
                      if (queryMatch?.[1]) {
                        toolArgs.query = queryMatch[1]
                        console.log(
                          '[Anthropic] Extracted query parameter from incomplete JSON:',
                          toolArgs.query,
                        )
                      }
                    }
                  } catch (error) {
                    console.warn(
                      'Error processing tool input JSON:',
                      error,
                      'Raw JSON:',
                      mostCompleteJson,
                    )
                  }
                } else if (
                  mostCompleteJsonBlock.input &&
                  Object.keys(mostCompleteJsonBlock.input).length > 0
                ) {
                  // If partialInputJson is empty but the input field has content, use that
                  toolArgs = mostCompleteJsonBlock.input
                  console.log(
                    '[Anthropic] Using predefined input from tool block:',
                    toolArgs,
                  )
                }
              }

              const toolCall = { name: toolName, input: toolArgs }

              console.log(
                'Tool call request:',
                toolCall.name,
                JSON.stringify(toolCall.input),
              )

              const toolParams = getParamsByToolName(toolCall)

              // Log the tool call input for debugging
              logger.log('[process-tool-call] input-params', {
                name: toolCall.name,
                inputParams: JSON.stringify(toolCall.input),
                toolParams: toolParams,
              })
              let toolResult: McpToolResultValueObject | undefined
              if (toolParams) {
                toolResult = await this.mcpClientService.callTool({
                  name: toolCall.name,
                  params: toolParams,
                  token: userToken,
                })
              }

              // Use the provided userToken from the GraphQL context
              // Process the tool call and update accumulated content
              const result = await this.processContent.processToolCall({
                toolResult,
                toolCall,
                toolParams,
                initialContent: accumulatedContent,
              })

              accumulatedContent = result.accumulatedContent

              // Publish updated content
              onPublish({
                id: messageId,
                role: 'assistant',
                content: accumulatedContent,
                createdAt: timestamp,
                isComplete: false,
              })
            }
          } catch (error) {
            console.error('Error handling tool call:', error)

            accumulatedContent += `\n\nI'm sorry I can't access your contact list. Can you try again later or check your connection.\n\n`

            onPublish({
              id: messageId,
              role: 'assistant',
              content: accumulatedContent,
              createdAt: timestamp,
              isComplete: false,
>>>>>>> main
            })
          }

          // Use the provided userToken from the GraphQL context
          // Process the tool call and update accumulated content
          const result = await this.processContent.processToolCall({
            toolResult,
            toolCall,
            initialContent: accumulatedContent,
          })

          accumulatedContent = result.accumulatedContent

          // Publish updated content
          onPublish({
            id: messageId,
            role: 'assistant',
            content: accumulatedContent,
            createdAt: timestamp,
            isComplete: false,
          })
        }
      }

      // After completing the stream, save the assistant's response to the conversation history
      if (accumulatedContent) {
        const assistantMessage: MessageParam = {
          role: 'assistant',
          content: accumulatedContent,
        }

        // Get the updated history and add the assistant's response
        const updatedHistory =
          this.conversationHistory.get(conversationId) || []
        updatedHistory.push(assistantMessage)

        // Save the updated history
        this.conversationHistory.set(conversationId, updatedHistory)

        // Trim history if it gets too long (optional, to prevent token limits)
        if (updatedHistory.length > 20) {
          this.conversationHistory.set(
            conversationId,
            updatedHistory.slice(-20), // Keep the most recent 20 messages
          )
        }
      }

      // Publish final message with complete flag
      onPublish({
        id: messageId,
        role: 'assistant',
        content: accumulatedContent,
        createdAt: timestamp,
        isComplete: true,
      })

      return messageId
    } catch (error) {
      console.error('Error streaming from Anthropic:', error)
      throw error
    }
  }
}
