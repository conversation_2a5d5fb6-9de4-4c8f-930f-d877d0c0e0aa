import { assertTypes, type TypeEqualityGuard } from '@repo/result'
import { registerEnumType } from 'type-graphql'

import type { DBCompanyUserRankValueObject } from '@emma/storage'

export enum CompanyUserRankEnum {
  SalesConsultant = 'SalesConsultant',
  TeamCoordinator = 'TeamCoordinator',
  SalesLeader = 'SalesLEader',
  ExecutiveSalesLeader = 'ExecutiveSalesLeader',
}

type Keys = keyof typeof CompanyUserRankEnum

assertTypes<TypeEqualityGuard<Keys, DBCompanyUserRankValueObject>>()

registerEnumType(CompanyUserRankEnum, {
  name: 'CompanyUserRankEnum',
})
