import { assertTypes, type TypeEqualityGuard } from '@repo/result'
import { registerEnumType } from 'type-graphql'

import type { DBCompanyUserActivityStatusValueObject } from '@emma/storage'

export enum CompanyUserActivityStatusEnum {
  Active = 'Active',
  Inactive = 'Inactive',
}

type Keys = keyof typeof CompanyUserActivityStatusEnum

assertTypes<TypeEqualityGuard<Keys, DBCompanyUserActivityStatusValueObject>>()

registerEnumType(CompanyUserActivityStatusEnum, {
  name: 'CompanyUserActivityStatusEnum',
})
