import { Field, ObjectType } from 'type-graphql'

import { PaginationCursorDto } from '../../pagination/dtos/pagination-cursor.dto'

import { CompanyDto } from './company.dto'

@ObjectType('CompanyList', { description: '' })
export class CompanyListDto {
  @Field((_type) => [CompanyDto])
  companyList: CompanyDto[]

  @Field((_type) => CompanyDto, { nullable: true })
  company?: CompanyDto

  @Field((_type) => PaginationCursorDto)
  pagination?: PaginationCursorDto

  constructor(value: CompanyListDto) {
    this.companyList = value.companyList
    this.pagination = value.pagination
    this.company = value.company
  }
}
