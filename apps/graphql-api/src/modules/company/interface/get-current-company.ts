import { isErr } from '@repo/result'

import { GetCompanyUseCase } from '@emma/storage'

import type { CompanyListRelationsValueObject } from '@emma/storage'

export async function getCurrentCompany({
  companyList,
  selectedCompanyUuid,
  isRoot,
}: {
  isRoot: boolean
  companyList: CompanyListRelationsValueObject[]
  selectedCompanyUuid?: string
}) {
  const findCompany = companyList?.find(
    (item) => item.company.public_uuid === selectedCompanyUuid,
  )?.company
  if (!isRoot) {
    return findCompany
  }

  if (findCompany) return findCompany

  const useCase = new GetCompanyUseCase()

  if (!selectedCompanyUuid) return

  const res = await useCase.getCompany({
    uuid: selectedCompanyUuid,
  })

  if (isErr(res)) {
    return
  }

  return res.data.company
}
