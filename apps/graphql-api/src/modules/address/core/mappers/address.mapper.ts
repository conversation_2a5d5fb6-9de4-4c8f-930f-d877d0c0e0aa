import { AddressDto } from '../../dto/address.dto'

import type { Address } from '@prisma/client'

export class AddressMapper {
  static toAddressDto(address: Address): AddressDto {
    return new AddressDto({
      uuid: address.public_uuid,
      city: address.city,
      country: address.country,
      countryCode: address.country_code,
      mailStop: address.mail_stop,
      postalCode: address.zip_code,
      stateCode: address.state_code,
      street1: address.street_1,
      street2: address.street_2,
    })
  }
}
