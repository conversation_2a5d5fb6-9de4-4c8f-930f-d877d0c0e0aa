import { Field, ID, InputType } from 'type-graphql'

import { Valid } from '../../validator'

@InputType()
export class TeammateNoteUpsertInput {
  @Field((_type) => ID, { nullable: true })
  @Valid(Valid.scheme.string().uuid().optional())
  uuid?: string

  @Field((_type) => ID)
  @Valid(Valid.scheme.string().uuid())
  teammateUuid: string

  @Field()
  @Valid(Valid.scheme.string())
  content: string
}
