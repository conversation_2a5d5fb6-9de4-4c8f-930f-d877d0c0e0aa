import { isErr } from '@repo/result'
import { Args, Authorized, Ctx, Query, Resolver } from 'type-graphql'

import { GetTeammateNotesUseCase, GetTeammateUseCase } from '@emma/storage'

import { createLogger } from '../../app'
import { DefaultErrorDto } from '../../app/dtos/default-error.dto'
import { NoteMapper } from '../../note/core/mappers/note.mapper'
import { NoteListDto } from '../../note/dto/note-list.dto'
import { NoteListResultUnion } from '../../note/dto/note-list.union'
import { PaginationCursorMapper } from '../../pagination/core/pagination-cursor.mapper'
import { ValidateArgs } from '../../validator'
import { TeammateMapper } from '../core/mappers/teammate.mapper'
import { TeammateListDto } from '../dtos/team-list.dto'
import { TeammateDetailsDto } from '../dtos/teammate-details.dto'
import { TeammateDetailsResultUnion } from '../dtos/teammate-details.union'
import { TeammateListResultUnion } from '../dtos/teammate-list.union'
import { GetTeammateArgsDto } from '../inputs/get-teammate-args.dto'
import { GetTeammateNotesListArgsDto } from '../inputs/get-teammate-notes-list-args.dto'
import { GetTeammatesListArgsDto } from '../inputs/get-teammates-list-args.dto'

import type { GQLServerContext } from '../../server/core/gql-server-context'

const logger = createLogger('teams.queries.resolver')

@Authorized()
@Resolver()
export class TeamQueriesResolver {
  @ValidateArgs(GetTeammatesListArgsDto)
  @Query((_returns) => TeammateListResultUnion)
  async getTeammateList(
    @Args()
    { searchValue, filters, pagination, sortFields }: GetTeammatesListArgsDto,
    @Ctx() ctx: GQLServerContext,
  ): Promise<typeof TeammateListResultUnion> {
    const useCase = new GetTeammateUseCase()
    const teammatesResult = await useCase.getTeammateListByCompanyUserUuid({
      companyUserUuid: ctx.auth?.companyUserUuid || '',
      filters,
      searchValue,
      sortFields,
      pagination,
    })

    if (isErr(teammatesResult)) {
      const { error } = teammatesResult

      logger.error(error.key, error)

      if (error.key === 'TeammateErrors.GetTeammateListByCompanyUserUuid') {
        return new DefaultErrorDto({
          code: 'Empty',
          message: error.message,
        })
      }

      return new DefaultErrorDto({
        code: 'Bad',
        message: error.message,
      })
    }

    return new TeammateListDto({
      list: TeammateMapper.toTeammatesDtoList(teammatesResult.data.list),
      totalCount: teammatesResult.data.totalCount,
      pagination: PaginationCursorMapper.toDto(teammatesResult.data),
    })
  }

  @ValidateArgs(GetTeammateArgsDto)
  @Query((_returns) => TeammateDetailsResultUnion)
  async getTeammateDetails(
    @Args()
    { uuid }: GetTeammateArgsDto,
  ): Promise<typeof TeammateDetailsResultUnion> {
    const useCase = new GetTeammateUseCase()
    const teammatesResult = await useCase.getTeammateDetailsByUuid({
      uuid,
    })

    if (isErr(teammatesResult)) {
      const { error } = teammatesResult

      logger.error(error.key, error)

      if (error.key === 'TeammateErrors.GetTeammateDetailsByUuid') {
        return new DefaultErrorDto({
          code: 'Empty',
          message: error.message,
        })
      }

      return new DefaultErrorDto({
        code: 'Bad',
        message: error.message,
      })
    }

    return new TeammateDetailsDto(
      TeammateMapper.toTeammateDetailsDto(teammatesResult.data),
    )
  }

  @ValidateArgs(GetTeammateNotesListArgsDto)
  @Query((_returns) => NoteListResultUnion)
  async getTeammateNotesList(
    @Args() { teammateUuid }: GetTeammateNotesListArgsDto,
    @Ctx() ctx: GQLServerContext,
  ): Promise<typeof NoteListResultUnion> {
    const useCase = new GetTeammateNotesUseCase()
    const notesResult = await useCase.getTeammateNotesByUuid({
      teammateUuid,
      parentUserUuid: ctx.auth?.companyUserUuid || '',
    })

    if (isErr(notesResult)) {
      const { error } = notesResult

      logger.error(error.key, error)

      if (error.key === 'TeammateNoteErrors.GetTeammateNotesByUuidNotExist') {
        return new DefaultErrorDto({
          code: 'Empty',
          message: error.message,
        })
      }

      return new DefaultErrorDto({
        code: 'Bad',
        message: error.message,
      })
    }

    return new NoteListDto({
      list: NoteMapper.toNotesDtoList(notesResult.data),
    })
  }
}
