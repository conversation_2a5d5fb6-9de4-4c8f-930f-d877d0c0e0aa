import { Field, Int, ObjectType } from 'type-graphql'

import { PaginationCursorDto } from '../../pagination/dtos/pagination-cursor.dto'

import { TeammateDto } from './teammate.dto'

@ObjectType('TeammateList')
export class TeammateListDto {
  @Field((_type) => [TeammateDto])
  list: TeammateDto[]

  @Field((_type) => PaginationCursorDto)
  pagination: PaginationCursorDto

  @Field((_type) => Int)
  totalCount: number

  constructor(value: TeammateListDto) {
    this.list = value.list
    this.pagination = value.pagination
    this.totalCount = value.totalCount
  }
}
