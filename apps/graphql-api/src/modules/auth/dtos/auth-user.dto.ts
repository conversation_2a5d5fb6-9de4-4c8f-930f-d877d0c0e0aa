import { Field, ObjectType } from 'type-graphql'

import { CompanyDto } from '../../company/dtos/company.dto'
import { CompanyUserDto } from '../../company/dtos/company-user.dto'
import { CompanyUserSettingsDto } from '../../settings'
import { UserDto } from '../../user/dtos/user.dto'

import { TokensDto } from './tokens.dto'

@ObjectType('AuthUser', { description: '' })
export class AuthUserDto {
  @Field((_type) => UserDto)
  user: UserDto

  @Field((_type) => CompanyUserSettingsDto)
  settings: CompanyUserSettingsDto

  @Field((_type) => TokensDto)
  tokens: TokensDto

  @Field((_type) => [CompanyUserDto], {
    description:
      'Available companyUser for login. Used for switch company, if more than ones',
  })
  companyUsers: CompanyUserDto[]

  @Field((_type) => CompanyUserDto, { nullable: true })
  companyUser?: CompanyUserDto

  @Field((_type) => Boolean)
  hasTeam: boolean

  @Field((_type) => String)
  sessionUuid: string

  @Field((_type) => CompanyDto, { nullable: true })
  company?: CompanyDto

  constructor(value: AuthUserDto) {
    this.tokens = value.tokens
    this.user = value.user
    this.sessionUuid = value.sessionUuid
    this.companyUsers = value.companyUsers
    this.companyUser = value.companyUser
    this.company = value.company
    this.settings = value.settings
    this.hasTeam = value.hasTeam
  }
}
