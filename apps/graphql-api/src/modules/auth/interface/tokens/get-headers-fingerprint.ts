import { createHash } from 'node:crypto'

import type { IncomingHttpHeaders } from 'http'

const isSkipFingerprint = !!process.env.SKIP_HEADER_FINGERPRINT

const USE_HEADERS = [
  'accept',
  'accept-encoding',
  'accept-language',
  'host',
  'sec-browsing-topics',
  'sec-ch-prefers-color-scheme',
  'sec-ch-prefers-reduced-motion',
  'sec-ch-prefers-reduced-transparency',
  'sec-ch-ua',
  'sec-ch-ua-arch',
  'sec-ch-ua-bitness',
  'sec-ch-ua-full-version',
  'sec-ch-ua-full-version-list',
  'sec-ch-ua-mobile',
  'sec-ch-ua-model',
  'sec-ch-ua-platform',
  'sec-ch-ua-platform-version',
  'sec-fetch-dest',
  'sec-fetch-mode',
  'sec-fetch-site',
  'sec-fetch-user',
  'sec-gpc',
  'sec-purpose',
  'sec-websocket-accept',
  'user-agent',
]

export function getHeadersFingerprint({
  headers,
}: {
  headers: IncomingHttpHeaders
}): string {
  if (isSkipFingerprint) return '-'

  const hash = createHash('sha256')
  const headersToHash = USE_HEADERS.reduce<string[]>((result, header) => {
    const value = headers[header]
    if (Array.isArray(value)) return result
    if (!value) return result
    return [...result, value]
  }, [])

  hash.update(headersToHash.join('|'))

  const hex = hash.digest('hex')

  return hex
}
