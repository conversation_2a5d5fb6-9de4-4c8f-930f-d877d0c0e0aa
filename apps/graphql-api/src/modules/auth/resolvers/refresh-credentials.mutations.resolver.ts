import { isErr } from '@repo/result'
import { Arg, Ctx, Mutation, Resolver } from 'type-graphql'
import { v4 as uuidv4 } from 'uuid'

import { GetUserUseCase } from '@emma/storage'

import { createLogger } from '../../app'
import { generateAuthTokens } from '../../token/interface/generate-auth-tokens'
import { RefreshCredentialsMapper } from '../core/mappers/refresh-credentials.mapper'
import { RefreshCredentialsResultUnion } from '../dtos/refresh-credentials-result.union'
import { RefreshCredentialsErrorDto } from '../dtos/refresh-errors.dto'
import { RefreshCredentialsInput } from '../inputs/refresh-credentials-input.dto'
import { validateTokens } from '../interface/tokens/validate-tokens'

import { getAclRoles } from './user-login/get-acl-roles'

import type { GQLServerContext } from '../../server/core/gql-server-context'

const logger = createLogger('refresh-credentials.mutations')

@Resolver()
export class RefreshCredentialsMutationsResolver {
  @Mutation((_returns) => RefreshCredentialsResultUnion)
  async refreshCredentials(
    @Arg('RefreshCredentialsInput') input: RefreshCredentialsInput,
    @Ctx() ctx: GQLServerContext,
  ): Promise<typeof RefreshCredentialsResultUnion> {
    const accessRes = validateTokens({
      refreshToken: input.refreshToken,
      headers: ctx.headers,
    })

    if (isErr(accessRes)) {
      logger.error('validate-access-token', accessRes.error)
      return new RefreshCredentialsErrorDto({
        code: 'Invalid',
      })
    }

    if (
      !accessRes.data.parsedData.roles.includes('root') &&
      !accessRes.data.parsedData.companyUserUuid
    ) {
      logger.error('company-not-selected-for-user')
      return new RefreshCredentialsErrorDto({
        code: 'CompanyNotSelected',
      })
    }

    if (!accessRes.data.parsedData.p) {
      logger.error('fingerprint-not-found')
      return new RefreshCredentialsErrorDto({
        code: 'HeadersBroken',
      })
    }

    const useCase = new GetUserUseCase()
    const userResult = await useCase.getCompanyUserByUuid({
      companyUserUuid: accessRes.data.parsedData.companyUserUuid || '',
      userUuid: accessRes.data.parsedData.userUuid || '',
    })

    if (isErr(userResult)) {
      const { error } = userResult

      logger.error(error.key, error)

      switch (error.key) {
        case 'UserErrors.GetUserNotExist':
          return new RefreshCredentialsErrorDto({
            code: 'UserNotExist',
          })
        default:
          return new RefreshCredentialsErrorDto({
            code: 'UnknownError',
          })
      }
    }

    const companyUserUuid = accessRes.data.parsedData.companyUserUuid
    const companyUser = userResult.data.companyUserRelations.find(
      (node) => node.companyUser.public_uuid === companyUserUuid,
    )?.companyUser

    const tokensDto = generateAuthTokens({
      userUuid: accessRes.data.parsedData.userUuid,
      pushUuid: accessRes.data.parsedData.pushUuid,
      companyUuid: accessRes.data.parsedData.companyUuid,
      sessionUuid: accessRes.data.parsedData.sessionUuid || uuidv4(),
      companyUserUuid,
      roles: getAclRoles({
        user: userResult.data.user,
        companyUser,
      }),
      p: accessRes.data.parsedData.p,
    })

    return RefreshCredentialsMapper.toRefreshCredentialsDto({
      tokens: tokensDto,
    })
  }
}
