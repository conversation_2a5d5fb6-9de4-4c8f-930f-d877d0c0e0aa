import { AuthUserDto } from '../../dtos/auth-user.dto'

import type { CompanyDto } from '../../../company/dtos/company.dto'
import type { CompanyUserDto } from '../../../company/dtos/company-user.dto'
import type { CompanyUserSettingsDto } from '../../../settings'
import type { UserDto } from '../../../user'
import type { TokensDto } from '../../dtos/tokens.dto'

export class AuthUserMapper {
  static toAuthUserDto({
    user,
    tokens,
    companyUsers,
    companyUser,
    company,
    settings,
    hasTeam,
    sessionUuid,
  }: {
    user: UserDto
    tokens: TokensDto
    companyUsers: CompanyUserDto[]
    settings: CompanyUserSettingsDto
    companyUser?: CompanyUserDto
    company?: CompanyDto
    hasTeam: boolean
    sessionUuid: string
  }): AuthUserDto {
    return new AuthUserDto({
      user,
      tokens,
      companyUser,
      companyUsers,
      company,
      settings,
      hasTeam,
      sessionUuid,
    })
  }
}
