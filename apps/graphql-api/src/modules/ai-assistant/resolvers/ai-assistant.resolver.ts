import {
  Arg,
  Authorized,
  Ctx,
  Mutation,
  Resolver,
  Root,
  Subscription,
} from 'type-graphql'
import { v4 as uuidv4 } from 'uuid'

import { createLogger } from '../../app'
import { DefaultErrorDto } from '../../app/dtos/default-error.dto'
import { ResponseOkErrorUnion } from '../../app/dtos/response-ok-error.union'
import { StatusOkDto } from '../../app/dtos/status-ok.dto'
import { AiAssistantStreamService } from '../../mcp-tools/interface/ai-assistant-stream.service'
import { getSystemPrompt } from '../../mcp-tools/interface/get-system-prompt'
import { generateAccessToken } from '../../token/interface/generate-auth-tokens'
import { ValidateArgs } from '../../validator'
import { AiAssistantChunkDto } from '../dto/ai-assistant-chunk.dto'
import { AiAssistantMessageInput } from '../inputs/ai-assistant-message-input.dto'
import { getAiAssistantChannel } from '../interface/get-ai-assistant-channel'

import type { GQLServerContext } from '../../server/core/gql-server-context'

const logger = createLogger('ai-assistant.resolver')

@Authorized()
@Resolver()
export class AiAssistantResolver {
  constructor(
    private readonly aiAssistantStreamService = new AiAssistantStreamService(),
  ) {}

  @Subscription({
    topics: ({ context }) => getAiAssistantChannel(context.auth.sessionUuid),
  })
  subscribeAiAssistant(
    @Root()
    payload: AiAssistantChunkDto,
  ): AiAssistantChunkDto {
    return payload
  }

  @Mutation(() => ResponseOkErrorUnion)
  @ValidateArgs(AiAssistantMessageInput)
  async sendAiAssistantMessage(
    @Arg('input') input: AiAssistantMessageInput,
    @Ctx() ctx: GQLServerContext,
  ): Promise<typeof ResponseOkErrorUnion> {
    const sessionUuid = ctx.auth?.sessionUuid

    if (!sessionUuid) {
      logger.error('session-uuid-not-defined', {
        companyUserUuid: ctx.auth?.companyUserUuid,
        roles: ctx.auth?.roles,
      })
      return new DefaultErrorDto({ code: 'Bad' })
    }

    if (!input.conversationUuid) {
      // start new conversation, init in database
    }

    const conversationUuid = input.conversationUuid || uuidv4()

    // user token can have expiration in few seconds, let's try to generate new without link to old valid token
    const { exp, iat, ...auth } = ctx.auth!
    const mcpToken = generateAccessToken(auth)

    this.aiAssistantStreamService.createStream({
      conversationUuid,
      message: input.message,
      systemPrompt: getSystemPrompt(),
      userToken: mcpToken,
      onPublish: (chunk) => {
        ctx.pubSub.publish(getAiAssistantChannel(sessionUuid), chunk)
      },
    })

    return new StatusOkDto()
  }
}
