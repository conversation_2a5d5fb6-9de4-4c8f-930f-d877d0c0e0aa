import { Field, ID, ObjectType } from 'type-graphql'

import { AiAssistantContentDto } from './ai-assistant-content.dto'

import type { AiRoleValueObject } from '../core/ai-role.value-object'

@ObjectType('AiAssistantChunk')
export class AiAssistantChunkDto {
  @Field(() => ID)
  conversationUuid: string

  @Field(() => ID)
  chunkUuid: string

  @Field()
  type: 'start' | 'process' | 'end' | 'error'

  @Field()
  role: AiRoleValueObject

  @Field(() => AiAssistantContentDto, { nullable: true })
  content?: AiAssistantContentDto

  @Field()
  createdAt: Date
}
