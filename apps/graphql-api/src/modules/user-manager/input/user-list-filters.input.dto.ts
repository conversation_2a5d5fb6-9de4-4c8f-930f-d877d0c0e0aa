import { Field, InputType } from 'type-graphql'

import { Valid } from '../../validator'

import type {
  DBInviteStatusValueObject,
  DBUserRoleValueObject,
} from '@emma/storage'

const ROLES_MAP: Record<DBUserRoleValueObject, boolean> = {
  admin: true,
  member: true,
}

const INVITE_MAP: Record<DBInviteStatusValueObject, boolean> = {
  applied: true,
  revoke: true,
  send: true,
}

@InputType('UserListFiltersInput')
export class UserListFiltersInputDto {
  @Valid(Valid.scheme.string().max(1024).optional())
  @Field((_type) => String, { nullable: true })
  name?: string

  @Valid(Valid.scheme.string().uuid().optional())
  @Field((_type) => String, { nullable: true })
  companyUuid?: string

  @Valid(
    Valid.scheme
      .custom((val: DBUserRoleValueObject) => ROLES_MAP[val])
      .optional(),
  )
  @Field((_type) => String, { nullable: true })
  role?: DBUserRoleValueObject

  @Valid(
    Valid.scheme
      .custom((val: DBInviteStatusValueObject) => INVITE_MAP[val])
      .optional(),
  )
  @Field((_type) => String, { nullable: true })
  invite?: DBInviteStatusValueObject
}
