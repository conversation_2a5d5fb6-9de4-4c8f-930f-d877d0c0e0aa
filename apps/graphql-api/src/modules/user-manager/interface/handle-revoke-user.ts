import { isErr } from '@repo/result'

import { RevokeCompanyUserUseCase } from '@emma/storage'

import { createLogger } from '../../app'
import { DefaultErrorDto } from '../../app/dtos/default-error.dto'
import { StatusOkDto } from '../../app/dtos/status-ok.dto'

const logger = createLogger('handle-revoke-user')

export async function handleRevokeUser(companyUserUuid: string) {
  const useCase = new RevokeCompanyUserUseCase()
  const userResult = await useCase.revoke({
    companyUserUuid,
  })

  if (isErr(userResult)) {
    logger.error('revoke-user', userResult.error)
    return new DefaultErrorDto({
      code: 'Bad',
    })
  }
  return new StatusOkDto()
}
