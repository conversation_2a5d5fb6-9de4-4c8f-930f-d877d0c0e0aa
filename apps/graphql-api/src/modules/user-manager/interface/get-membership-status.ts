import { MembershipStatusEnum } from '../core/membership-status.enum'

import type {
  DBAccountTypeValueObject,
  DBCompanyUserInvite,
} from '@emma/storage'

type Args = {
  accountType?: DBAccountTypeValueObject
  invite?: DBCompanyUserInvite
}

export function getMembershipStatus({
  accountType,
  invite,
}: Args): MembershipStatusEnum {
  if (accountType === 'block') return MembershipStatusEnum.blocked
  if (!invite?.invite_status) return MembershipStatusEnum.active
  if (invite.expired_at && Date.now() > invite.expired_at.getTime())
    return MembershipStatusEnum.expired
  if (invite.invite_status === 'applied') return MembershipStatusEnum.active
  if (invite.invite_status === 'send') return MembershipStatusEnum.invited
  return MembershipStatusEnum.removed
}
