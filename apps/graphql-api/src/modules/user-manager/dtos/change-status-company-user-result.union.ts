import { createUnionType } from 'type-graphql'

import { DefaultErrorDto } from '../../app/dtos/default-error.dto'
import { StatusOkDto } from '../../app/dtos/status-ok.dto'
import { ValidationErrorDto } from '../../validator'

export const ChangeStatusCompanyUserResultUnion = createUnionType({
  name: 'ChangeStatusCompanyUserResult',
  types: () => [StatusOkDto, DefaultErrorDto, ValidationErrorDto] as const,
})
