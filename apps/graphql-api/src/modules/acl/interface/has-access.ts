import { AuthenticationError, AuthorizationError } from 'type-graphql'

import { checkRolesExist } from './check-roles-exist'

import type { GQLServerContext } from '../../server/core/gql-server-context'
import type { AclUserRoleValueObject } from '../core/acl-user-role.value-object'

export async function hasAccess({
  context,
  roles,
}: {
  context: GQLServerContext
  roles?: AclUserRoleValueObject[]
}): Promise<boolean> {
  if (!context.auth?.userUuid) return false

  const userUuid = context.auth?.userUuid

  if (!userUuid) return false

  if (context.isExpired) {
    throw new AuthenticationError()
  }

  if (context.mode === 'subscription') {
    if (
      !checkRolesExist({
        incomingRoles: roles,
        userRoles: context.auth.roles.filter((role) => role !== 'root') || [],
        isRootAdmin: false,
      })
    ) {
      throw new AuthorizationError()
    }

    return true
  }

  if (!context.isHeadersCorrect) {
    throw new AuthorizationError()
  }

  if (!context.userRelations) return false

  if (
    !checkRolesExist({
      incomingRoles: roles,
      userRoles: context.userRelations.companyUser?.roles,
      isRootAdmin: context.userRelations.user.is_root_admin,
    })
  ) {
    throw new AuthorizationError()
  }

  return true
}
