import { Field, Float, ObjectType } from 'type-graphql'

import { NotificationSubtypeEnum } from '../../notification/dtos/meta-subtypes/notification-subtype.enum'
import { ScheduleDto } from '../../schedule/dto/schedule.dto'

import { ContentFileDto } from './content-file.dto'
import { ContentLangEnum } from './content-lang-enum'
import { ContentStatusEnum } from './content-status-enum'

import type { DBContentTypeValueObject } from '@emma/storage'

@ObjectType('Content')
export class ContentDto {
  @Field((_type) => String)
  uuid: string

  @Field((_type) => String)
  domain: string

  @Field((_type) => String)
  contentType: DBContentTypeValueObject

  @Field()
  title: string

  @Field()
  text: string

  @Field((_type) => [String], { nullable: true })
  links?: string[]

  @Field((_type) => [String], { nullable: true })
  keywords?: string[]

  @Field((_type) => [ContentLangEnum], { nullable: true })
  lang?: ContentLangEnum[]

  @Field((_type) => ContentStatusEnum, { nullable: true })
  status?: keyof typeof ContentStatusEnum

  @Field((_type) => NotificationSubtypeEnum, { nullable: true })
  notificationSubtype?: NotificationSubtypeEnum

  @Field((_type) => ScheduleDto, { nullable: true })
  schedule?: ScheduleDto

  @Field((_type) => ScheduleDto, { nullable: true })
  expiration?: ScheduleDto

  @Field((_type) => Float, { nullable: true, defaultValue: 0 })
  usagesCount: number

  @Field((_type) => Float, { nullable: true, defaultValue: 0 })
  viewCount: number

  @Field((_type) => Boolean, { nullable: true })
  isDefault?: boolean

  @Field()
  createdAt: Date

  @Field()
  updatedAt: Date

  @Field((_) => ContentFileDto, { nullable: true })
  file?: ContentFileDto

  constructor(content: ContentDto) {
    this.uuid = content.uuid
    this.domain = content.domain
    this.contentType = content.contentType
    this.title = content.title
    this.text = content.text
    this.links = content.links
    this.usagesCount = content.usagesCount
    this.viewCount = content.viewCount
    this.schedule = content.schedule
    this.expiration = content.expiration
    this.notificationSubtype = content.notificationSubtype
    this.status = content.status
    this.lang = content.lang
    this.keywords = content.keywords
    this.file = content.file
    this.isDefault = content.isDefault

    this.createdAt = content.createdAt
    this.updatedAt = content.updatedAt
  }
}
