import { Field, ObjectType } from 'type-graphql'

import { PaginationCursorDto } from '../../pagination/dtos/pagination-cursor.dto'

import { ContentDto } from './content.dto'

@ObjectType('ContentList', { description: '' })
export class ContentListDto {
  @Field((_type) => [ContentDto])
  contentList: ContentDto[]

  @Field((_type) => PaginationCursorDto)
  pagination?: PaginationCursorDto

  constructor(value: ContentListDto) {
    this.contentList = value.contentList
    this.pagination = value.pagination
  }
}
