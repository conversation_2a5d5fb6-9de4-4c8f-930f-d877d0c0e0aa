import { assertTypes, type TypeEqualityGuard } from '@repo/result'
import { registerEnumType } from 'type-graphql'

import type { DBContentLanguageValueObject } from '@emma/storage'

export enum ContentLangEnum {
  enUS = 'enUS',
  enCA = 'enCA',
  esUS = 'esUS',
  frCA = 'frCA',
}

type Keys = keyof typeof ContentLangEnum

assertTypes<TypeEqualityGuard<Keys, DBContentLanguageValueObject>>()

registerEnumType(ContentLangEnum, {
  name: 'ContentLangEnum',
})
