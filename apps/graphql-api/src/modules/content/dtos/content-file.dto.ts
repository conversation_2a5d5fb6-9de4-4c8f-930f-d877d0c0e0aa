import { Field, Float, ObjectType } from 'type-graphql'

@ObjectType('ContentFile')
export class ContentFileDto {
  @Field()
  bucketName: string

  @Field()
  fileName: string

  @Field()
  mimeType: string

  @Field()
  publicUrl: string

  @Field({ nullable: true })
  thumbnailUrl?: string

  @Field((_type) => Float, { nullable: true })
  fileSize?: number

  constructor(content: ContentFileDto) {
    this.bucketName = content.bucketName
    this.fileName = content.fileName
    this.mimeType = content.mimeType
    this.publicUrl = content.publicUrl
    this.thumbnailUrl = content.thumbnailUrl ?? undefined
    this.fileSize = content.fileSize ?? undefined
  }
}
