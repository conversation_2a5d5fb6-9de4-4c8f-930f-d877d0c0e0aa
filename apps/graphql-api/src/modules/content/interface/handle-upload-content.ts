import { isErr, resultErr, resultOk } from '@repo/result'

import { handleUploadFile } from '@emma/s3'

import { createLogger } from '../../app'

import { getContentFolderName } from './get-content-folder-name'
import { getContentTypeByMimeType } from './get-content-type-by-mime-type'

import type { DBContentTypeValueObject, DBFileEntity } from '@emma/storage'
import type { Result } from '@repo/result'
import type { FileUpload } from 'graphql-upload-minimal'

type Args = {
  file: Promise<FileUpload>
  companyUuid?: string
}

type Errors = {
  status: 'invalid-content-type' | 'upload-error'
  message?: string
}

type Success = {
  contentType: DBContentTypeValueObject
  file: DBFileEntity
}

const logger = createLogger('handle-upload-content')

export async function handleUploadContent({
  file,
  companyUuid,
}: Args): Promise<Result<Success, Errors>> {
  const { createReadStream, filename, mimetype } = await file
  const contentType = getContentTypeByMimeType(mimetype)
  if (!contentType) {
    return resultErr({
      status: 'invalid-content-type',
    })
  }

  const fileStream = createReadStream()
  const folderName = getContentFolderName({
    companyUuid,
    assetType: 'marketing',
  })

  logger.log('start upload', {
    filename,
  })

  const uploadRes = await handleUploadFile({
    file: fileStream,
    fileName: filename,
    folderName,
    mimeType: mimetype,
  })

  if (isErr(uploadRes)) {
    logger.error(uploadRes.error.key, uploadRes.error)
    return resultErr({
      status: 'upload-error',
      message: uploadRes.error.message,
    })
  }

  return resultOk({
    contentType,
    file: {
      fileName: filename,
      mimeType: mimetype,
      fileSize: uploadRes.data.fileSize,
      bucketName: uploadRes.data.bucketName,
      publicUrl: uploadRes.data.url,
      thumbnailUrl: uploadRes.data.thumbnailUrl,
    },
  })
}
