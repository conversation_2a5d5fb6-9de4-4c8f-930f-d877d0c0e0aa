import type { DBContentStatusValueObject } from '@emma/storage'
import type { ContentStatusEnum } from '../dtos/content-status-enum'

export function getStatus({
  isAdmin,
  status,
}: {
  isAdmin: boolean
  status?: ContentStatusEnum
}): Extract<DBContentStatusValueObject, 'publish' | 'draft'> {
  if (!isAdmin) return 'publish'
  if (status === 'publish') return 'publish'
  if (status === 'draft') return 'draft'
  return 'draft'
}
