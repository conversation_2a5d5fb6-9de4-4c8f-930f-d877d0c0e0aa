import { ArgsType, Field } from 'type-graphql'

import { NotificationSubtypeEnum } from '../../notification/dtos/meta-subtypes/notification-subtype.enum'
import { PaginationCursorArgsDto } from '../../pagination/dtos/pagination-cursor-args.dto'
import { Valid } from '../../validator'
import { ContentDomainEnum } from '../dtos/content-domain-enum'
import { ContentFilterEnum } from '../dtos/content-filter-enum'
import { ContentLangEnum } from '../dtos/content-lang-enum'
import { ContentStatusEnum } from '../dtos/content-status-enum'
import { ContentTypeEnum } from '../dtos/content-type-enum'

import { ContentSortFieldsInputDto } from './content-sort-fields-input.dto'

@ArgsType()
export class GetContentListArgsDto {
  @Field(() => String, { nullable: true })
  @Valid(Valid.scheme.string().max(2048).optional())
  searchValue?: string

  @Field(() => [ContentTypeEnum], { nullable: true })
  @Valid(
    Valid.scheme.array(Valid.scheme.nativeEnum(ContentTypeEnum)).optional(),
  )
  contentType?: ContentTypeEnum[]

  @Field((_type) => PaginationCursorArgsDto, { nullable: true })
  @Valid(PaginationCursorArgsDto, { isOptional: true })
  pagination?: PaginationCursorArgsDto

  @Field(() => ContentStatusEnum, {
    nullable: true,
    deprecationReason: 'use filter instead',
  })
  @Valid(Valid.scheme.nativeEnum(ContentStatusEnum).optional())
  status?: ContentStatusEnum

  @Field((_type) => ContentSortFieldsInputDto, { nullable: true })
  @Valid(ContentSortFieldsInputDto, { isOptional: true })
  sortFields?: ContentSortFieldsInputDto

  @Field(() => ContentFilterEnum, { nullable: true })
  @Valid(
    Valid.scheme
      .nativeEnum(ContentFilterEnum)
      .optional()
      .default(ContentFilterEnum.mixed),
  )
  filter?: ContentFilterEnum

  @Field(() => [ContentLangEnum], { nullable: true })
  @Valid(
    Valid.scheme.array(Valid.scheme.nativeEnum(ContentLangEnum)).optional(),
  )
  lang?: ContentLangEnum[]

  @Field(() => ContentDomainEnum, {
    nullable: true,
    deprecationReason: 'use filter instead',
  })
  @Valid(Valid.scheme.nativeEnum(ContentDomainEnum).optional())
  domain?: ContentDomainEnum

  @Field(() => NotificationSubtypeEnum, { nullable: true })
  @Valid(Valid.scheme.nativeEnum(NotificationSubtypeEnum).optional())
  notificationSubtype?: NotificationSubtypeEnum

  @Field(() => Boolean, { nullable: true })
  @Valid(Valid.scheme.boolean().optional())
  isUsedContentOnly?: boolean
}
