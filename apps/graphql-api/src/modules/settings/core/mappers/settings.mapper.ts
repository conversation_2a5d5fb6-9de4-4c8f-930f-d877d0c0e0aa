import { AiSettingsDto } from '../../dtos/ai-settings.dto'
import { CompanyUserSettingsDto } from '../../dtos/company-user-settings.dto'
import { SettingsCommonDto } from '../../dtos/settings-common.dto'
import { SettingsNotificationsDto } from '../../dtos/settings-notifications.dto'

import type { DBCompanyUserSettingsValueObject } from '@emma/storage'

export class UserSettingsMapper {
  static toUserSettingsDto({
    settings,
  }: {
    settings?: DBCompanyUserSettingsValueObject
  }): CompanyUserSettingsDto {
    return new CompanyUserSettingsDto({
      common: new SettingsCommonDto({
        language: settings?.common?.language || 'en-US',
        aiSettings: settings?.common?.ai_settings
          ? new AiSettingsDto({
              styleSample: settings.common.ai_settings.style_sample,
              voiceAndTone: settings.common.ai_settings.voice_and_tone,
              voiceAndToneCustom:
                settings.common.ai_settings.voice_and_tone_custom,
              messageLength: settings.common.ai_settings.message_length,
              keySellingPoints: settings.common.ai_settings.key_selling_points,
              personalizationPrompt:
                settings.common.ai_settings.personalization_prompt,
              personalizationFeedback:
                settings.common.ai_settings.personalization_feedback,
              personalizationFeedbackCustom:
                settings.common.ai_settings.personalization_feedback_custom,
            })
          : undefined,
      }),
      notifications:
        settings?.notifications.map((notification) => {
          return new SettingsNotificationsDto({
            uuid: notification.public_uuid,
            name: notification.name,
            subtype: notification.subtype,
            subject: notification.subject,
            isEnabled: notification.is_enabled,
          })
        }) ?? [],
    })
  }
}
