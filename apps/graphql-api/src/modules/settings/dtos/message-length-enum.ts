import { assertTypes, type TypeEqualityGuard } from '@repo/result'
import { registerEnumType } from 'type-graphql'

import type { DBMessageLengthValueObject } from '@emma/storage'

export enum MessageLengthEnum {
  short = 'short',
  medium = 'medium',
  long = 'long',
}

type Keys = keyof typeof MessageLengthEnum

assertTypes<TypeEqualityGuard<Keys, DBMessageLengthValueObject>>()

registerEnumType(MessageLengthEnum, {
  name: 'MessageLengthEnum',
  description: 'Available message length options for AI message generation',
})
