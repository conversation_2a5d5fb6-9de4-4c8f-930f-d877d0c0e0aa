import { Field, ObjectType } from 'type-graphql'

import { MessageLengthEnum } from './message-length-enum'
import { PersonalizationFeedbackEnum } from './personalization-feedback-enum'
import { SellingPointEnum } from './selling-point-enum'
import { VoiceAndToneEnum } from './voice-and-tone-enum'

import type {
  DBMessageLengthValueObject,
  DBPersonalizationFeedbackValueObject,
  DBSellingPointValueObject,
  DBVoiceAndToneValueObject,
} from '@emma/storage'

@ObjectType('AiSettings')
export class AiSettingsDto {
  @Field({ nullable: true })
  styleSample?: string

  @Field(() => VoiceAndToneEnum, { nullable: true })
  voiceAndTone?: DBVoiceAndToneValueObject

  @Field({ nullable: true })
  voiceAndToneCustom?: string

  @Field(() => MessageLengthEnum, { nullable: true })
  messageLength?: DBMessageLengthValueObject

  @Field(() => [SellingPointEnum], { nullable: true })
  keySellingPoints?: DBSellingPointValueObject[]

  @Field({ nullable: true })
  personalizationPrompt?: string

  @Field(() => PersonalizationFeedbackEnum, { nullable: true })
  personalizationFeedback?: DBPersonalizationFeedbackValueObject

  @Field({ nullable: true })
  personalizationFeedbackCustom?: string

  constructor(value: AiSettingsDto) {
    this.styleSample = value.styleSample
    this.voiceAndTone = value.voiceAndTone
    this.voiceAndToneCustom = value.voiceAndToneCustom
    this.messageLength = value.messageLength
    this.keySellingPoints = value.keySellingPoints
    this.personalizationPrompt = value.personalizationPrompt
    this.personalizationFeedback = value.personalizationFeedback
    this.personalizationFeedbackCustom = value.personalizationFeedbackCustom
  }
}
