import { assertTypes, type TypeEqualityGuard } from '@repo/result'
import { registerEnumType } from 'type-graphql'

import type { DBVoiceAndToneValueObject } from '@emma/storage'

export enum VoiceAndToneEnum {
  friendly_conv = 'friendly_conv',
  prof_direct = 'prof_direct',
  edu_inform = 'edu_inform',
  enthusiastic = 'enthusiastic',
}

type Keys = keyof typeof VoiceAndToneEnum

assertTypes<TypeEqualityGuard<Keys, DBVoiceAndToneValueObject>>()

registerEnumType(VoiceAndToneEnum, {
  name: 'VoiceAndToneEnum',
  description: 'Available voice and tone options for AI message generation',
})
