import { Field, InputType } from 'type-graphql'

import { Valid } from '../../../validator'

import { CompanyNotificationInitialContentInputDto } from './company-notification-initial-content-input.dto'
import { CompanyNotificationReceiverInputDto } from './company-notification-receiver-input.dto'
import { CompanyNotificationTimingInputDto } from './company-notification-timing-input.dto'

@InputType('PartialCompanyNotificationInput')
export class PartialCompanyNotificationInputDto {
  @Field((_type) => String, { nullable: true })
  @Valid(Valid.scheme.string().max(2058).optional(), { isOptional: true })
  name?: string

  @Field((_value) => CompanyNotificationInitialContentInputDto, {
    nullable: true,
  })
  @Valid(CompanyNotificationInitialContentInputDto, { isOptional: true })
  initialContent?: CompanyNotificationInitialContentInputDto

  @Field((_type) => [CompanyNotificationTimingInputDto], { nullable: true })
  @Valid([CompanyNotificationTimingInputDto], { isOptional: true })
  timing?: CompanyNotificationTimingInputDto[]

  @Field((_type) => Boolean, { nullable: true })
  @Valid(Valid.scheme.boolean().optional(), { isOptional: true })
  isEnabled?: boolean

  @Field(() => CompanyNotificationReceiverInputDto, { nullable: true })
  @Valid(CompanyNotificationReceiverInputDto, { isOptional: true })
  receiver?: CompanyNotificationReceiverInputDto
}
