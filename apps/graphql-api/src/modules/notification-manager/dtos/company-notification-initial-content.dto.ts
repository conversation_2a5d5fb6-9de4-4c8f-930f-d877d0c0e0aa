import { Field, ObjectType } from 'type-graphql'

@ObjectType('CompanyNotificationInitialContent')
export class CompanyNotificationInitialContentDto {
  @Field((_type) => String)
  title: string

  @Field((_type) => String)
  html: string

  @Field((_type) => String)
  pushTitle: string

  @Field((_type) => String)
  pushDesc: string

  constructor(notificationDto: CompanyNotificationInitialContentDto) {
    this.pushTitle = notificationDto.pushTitle
    this.pushDesc = notificationDto.pushDesc
    this.title = notificationDto.title
    this.html = notificationDto.html
  }
}
