import { assertTypes, type TypeEqualityGuard } from '@repo/result'
import { registerEnumType } from 'type-graphql'

import type { DBReceiverTypeValueObject } from '@emma/storage'

export enum NotificationReceiverTypeEnum {
  All = 'All',
  Picked = 'Picked',
  File = 'File',
}

type Keys = keyof typeof NotificationReceiverTypeEnum

assertTypes<TypeEqualityGuard<Keys, DBReceiverTypeValueObject>>()

registerEnumType(NotificationReceiverTypeEnum, {
  name: 'NotificationReceiverTypeEnum',
})
