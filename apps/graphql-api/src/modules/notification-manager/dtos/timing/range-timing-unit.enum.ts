import { assertTypes, type TypeEqualityGuard } from '@repo/result'
import { registerEnumType } from 'type-graphql'

import type { DBRangeTiming } from '@emma/storage'

export enum RangeTimingUnitEnum {
  d = 'd',
  w = 'w',
  m = 'm',
}

type Keys = keyof typeof RangeTimingUnitEnum

assertTypes<TypeEqualityGuard<Keys, DBRangeTiming['unit']>>()

registerEnumType(RangeTimingUnitEnum, {
  name: 'RangeTimingUnitEnum',
})
