import { Field, Float, InputType } from 'type-graphql'

import { Valid } from '../../../validator'

import { RangeTimingUnitEnum } from './range-timing-unit.enum'

@InputType('RangeTimingInput')
export class RangeTimingInputDto {
  @Field((_type) => RangeTimingUnitEnum)
  @Valid(Valid.scheme.nativeEnum(RangeTimingUnitEnum))
  unit: keyof typeof RangeTimingUnitEnum

  @Field((_type) => Float)
  @Valid(Valid.scheme.number().min(0).max(360))
  val: number
}
