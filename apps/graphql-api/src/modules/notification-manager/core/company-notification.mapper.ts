import { CompanyNotificationDto } from '../dtos/company-notification.dto'
import { CompanyNotificationInitialContentDto } from '../dtos/company-notification-initial-content.dto'
import { CompanyNotificationReceiverDto } from '../dtos/company-notification-receiver.dto'
import { CompanyNotificationStatusEnum } from '../dtos/company-notification-status-enum'

import { CompanyNotificationTimingMapper } from './company-notification-timing.mapper'

import type { DBCompanyNotificationEntity } from '@emma/storage'

function getCreatedBy(
  createdBy: DBCompanyNotificationEntity['created_by'],
): string | undefined {
  if (!createdBy) return undefined
  const name = `${createdBy.first_name} ${createdBy.last_name}`
  return name
}

function getStatus(
  notification: DBCompanyNotificationEntity,
): CompanyNotificationStatusEnum {
  if (notification.sent_at) {
    return CompanyNotificationStatusEnum.Sent
  }
  const isScheduled = notification.timing.filter(
    (timing) => timing.type === 'schedule' && timing.prop.isEnabled,
  )

  if (isScheduled) {
    return CompanyNotificationStatusEnum.Scheduled
  }

  return CompanyNotificationStatusEnum.Draft
}

export class CompanyNotificationMapper {
  static toDto(
    notification: DBCompanyNotificationEntity,
  ): CompanyNotificationDto {
    return new CompanyNotificationDto({
      name: notification.name,
      uuid: notification.public_uuid,
      sentAt: notification.sent_at,
      createdAt: notification.created_at,
      createdBy: getCreatedBy(notification.created_by),
      receiver: new CompanyNotificationReceiverDto({
        type: notification.receiver_type,
        total: notification.receiver_file?.emails?.length,
      }),
      status: getStatus(notification),
      subject: notification.subject,
      subtype: notification.subtype,
      isEnabled: notification.is_enabled,
      initialContent: new CompanyNotificationInitialContentDto(
        notification.initial_content,
      ),
      timing: CompanyNotificationTimingMapper.toDtoList(notification.timing),
    })
  }
}
