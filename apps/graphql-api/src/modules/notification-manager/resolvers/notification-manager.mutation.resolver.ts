import { isErr } from '@repo/result'
import { Arg, Ctx, Mutation, Resolver } from 'type-graphql'

import {
  AddCompanyNotificationUseCase,
  EditCompanyNotificationUseCase,
  RemoveCompanyNotificationUseCase,
} from '@emma/storage'

import { AuthRoles } from '../../acl/interface/auth-roles'
import { createLogger } from '../../app'
import { DefaultErrorDto } from '../../app/dtos/default-error.dto'
import { StatusOkDto } from '../../app/dtos/status-ok.dto'
import { ValidateArgs } from '../../validator'
import { AddCompanyNotificationResultUnion } from '../dtos/add-company-notification-result-union'
import { EditCompanyNotificationResultUnion } from '../dtos/edit-company-notification-result.union'
import { RemoveCompanyNotificationResultUnion } from '../dtos/remove-company-notification-result.union'
import { AddCompanyNotificationInputDto } from '../inputs/add-company-notification-input.dto'
import { EditCompanyNotificationInputDto } from '../inputs/edit-company-notification-input.dto'
import { RemoveCompanyNotificationInputDto } from '../inputs/remove-company-notification-input.dto'
import { getReceiverFile } from '../interface/get-receiver-file'
import { getTimingList } from '../interface/get-timing'
import { scheduleHomeOfficeNotify } from '../interface/schedule-home-office-notify'

import type { GQLServerContext } from '../../server/core/gql-server-context'

const logger = createLogger('notification-manager.resolver')

@AuthRoles(['root', 'admin'])
@Resolver()
export class NotificationManagerMutationResolver {
  @ValidateArgs(AddCompanyNotificationInputDto)
  @Mutation((_returns) => AddCompanyNotificationResultUnion)
  async addCompanyNotification(
    @Arg('AddInput') input: AddCompanyNotificationInputDto,
    @Ctx() ctx: GQLServerContext,
  ): Promise<typeof AddCompanyNotificationResultUnion> {
    const useCase = new AddCompanyNotificationUseCase()
    const companyUuid = ctx.auth?.companyUuid
    if (!companyUuid) {
      logger.error('add-company-notification.company-required')
      return new DefaultErrorDto({
        code: 'Empty',
        message: 'Company required',
      })
    }

    const timing = getTimingList(input.notification.timing)
    const res = await useCase.addNotification({
      notification: {
        name: input.notification.name,
        receiverType: input.notification.receiver?.type || 'File',
        receiverFile: getReceiverFile(input.notification.receiver),
        timing,
        initialContent: input.notification.initialContent,
        isEnabled: input.notification.isEnabled,
        subject: 'HomeOffice',
        subtype: 'TBC',
      },
      companyUuid,
    })

    if (isErr(res)) {
      const { error } = res
      logger.error('add-company-notification.add-notification', error)

      return new DefaultErrorDto({
        code: 'Bad',
        message: 'cannot add notification',
      })
    }

    await scheduleHomeOfficeNotify({
      companyNotificationUuid: res.data.uuid,
      ctx,
      timing,
    })

    return new StatusOkDto()
  }

  @ValidateArgs(EditCompanyNotificationInputDto)
  @Mutation((_returns) => EditCompanyNotificationResultUnion)
  async editCompanyNotification(
    @Arg('EditInput') input: EditCompanyNotificationInputDto,
    @Ctx() ctx: GQLServerContext,
  ): Promise<typeof EditCompanyNotificationResultUnion> {
    const companyUuid = ctx.auth?.companyUuid
    if (!companyUuid) {
      logger.error('edit-company-notification.company-required')
      return new DefaultErrorDto({
        code: 'Empty',
        message: 'Company required',
      })
    }

    const useCase = new EditCompanyNotificationUseCase()
    const res = await useCase.editNotification({
      notification: {
        name: input.notification.name,
        receiverType: input.notification.receiver?.type || 'File',
        receiverFile: getReceiverFile(input.notification.receiver),
        timing: input.notification.timing
          ? getTimingList(input.notification.timing)
          : undefined,
        initialContent: input.notification.initialContent,
        isEnabled: input.notification.isEnabled,
      },
      notificationUuid: input.notificationUuid,
      companyUuid,
    })

    // TODO: add reschedule here for editing flow
    // if timing changed - cancel old, start new
    // if timing disabled - cancel jobs
    // cancel old schedule, if timing changed, and timing not passed, start new

    if (isErr(res)) {
      const { error } = res
      logger.error('edit-company-notification.edit-notification', error)

      return new DefaultErrorDto({
        code: 'Bad',
        message: 'Cannot edit notification',
      })
    }

    return new StatusOkDto()
  }

  @ValidateArgs(RemoveCompanyNotificationInputDto)
  @Mutation((_returns) => RemoveCompanyNotificationResultUnion)
  async removeCompanyNotification(
    @Arg('RemoveInput') input: RemoveCompanyNotificationInputDto,
    @Ctx() ctx: GQLServerContext,
  ): Promise<typeof RemoveCompanyNotificationResultUnion> {
    const companyUuid = ctx.auth?.companyUuid
    if (!companyUuid) {
      logger.error('remove-company-notification.company-required')
      return new DefaultErrorDto({
        code: 'Empty',
        message: 'Company required',
      })
    }

    const useCase = new RemoveCompanyNotificationUseCase()
    const res = await useCase.removeNotification({
      notificationUuid: input.uuid,
      companyUuid,
      subject: 'HomeOffice', // we can remove only HomeOffice notifications
    })

    // TODO: drop scheduled job

    if (isErr(res)) {
      const { error } = res
      logger.error('remove-company-notification.remove-notification', error)

      return new DefaultErrorDto({
        code: 'Bad',
        message: 'cannot remove notification',
      })
    }

    return new StatusOkDto()
  }
}
