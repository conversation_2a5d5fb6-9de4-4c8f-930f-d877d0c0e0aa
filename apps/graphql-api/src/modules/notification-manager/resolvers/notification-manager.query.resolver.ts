import { isErr } from '@repo/result'
import { Args, Ctx, Query, Resolver } from 'type-graphql'

import {
  GetCompanyNotificationItemUseCase,
  GetCompanyNotificationListUseCase,
} from '@emma/storage'

import { AuthRoles } from '../../acl/interface/auth-roles'
import { hasRoot } from '../../acl/interface/has-root'
import { createLogger } from '../../app'
import { DefaultErrorDto } from '../../app/dtos/default-error.dto'
import { PaginationCursorMapper } from '../../pagination/core/pagination-cursor.mapper'
import { ValidateArgs } from '../../validator'
import { CompanyNotificationMapper } from '../core/company-notification.mapper'
import { CompanyNotificationDto } from '../dtos/company-notification.dto'
import { CompanyNotificationItemResultUnion } from '../dtos/company-notification-item-result-union'
import { CompanyNotificationListDto } from '../dtos/company-notification-list.dto'
import { CompanyNotificationListResultUnion } from '../dtos/company-notification-list-result-union'
import { GetCompanyNotificationItemInputDto } from '../inputs/get-company-notification-item.input.dto'
import { GetCompanyNotificationListArgsDto } from '../inputs/get-company-notification-list-filters.input.dto'

import type { GQLServerContext } from '../../server/core/gql-server-context'

const logger = createLogger('notification-manager.query.resolver')

@AuthRoles(['root', 'admin'])
@Resolver()
export class NotificationManagerQueryResolver {
  @ValidateArgs(GetCompanyNotificationListArgsDto)
  @Query((_returns) => CompanyNotificationListResultUnion)
  async getCompanyNotifications(
    @Args()
    { filters, pagination, isSystem }: GetCompanyNotificationListArgsDto,
    @Ctx() ctx: GQLServerContext,
  ): Promise<typeof CompanyNotificationListResultUnion> {
    const companyUuid = ctx.auth?.companyUuid
    if (!companyUuid) {
      logger.error('get-company-notifications.company-required')
      return new DefaultErrorDto({
        code: 'Empty',
        message: 'Company required',
      })
    }

    const isRoot = hasRoot(ctx)
    const useCase = new GetCompanyNotificationListUseCase()

    const res = await useCase.getCompanyNotificationList({
      companyUuid,
      filters: {
        name: filters?.name,
      },
      isSystem: isRoot && isSystem,
      pagination,
    })

    if (isErr(res)) {
      const { error } = res
      logger.error('get-company-notifications', error)

      return new DefaultErrorDto({
        code: 'Bad',
        message: 'cannot get list of notifications',
      })
    }

    return new CompanyNotificationListDto({
      notificationList: res.data.notificationList.map(
        CompanyNotificationMapper.toDto,
      ),
      pagination: PaginationCursorMapper.toDto(res.data),
    })
  }

  @ValidateArgs(GetCompanyNotificationItemInputDto)
  @Query((_returns) => CompanyNotificationItemResultUnion)
  async getCompanyNotificationItem(
    @Args()
    { uuid }: GetCompanyNotificationItemInputDto,
    @Ctx() ctx: GQLServerContext,
  ): Promise<typeof CompanyNotificationItemResultUnion> {
    const companyUuid = ctx.auth?.companyUuid
    if (!companyUuid) {
      logger.error('get-company-notification-item.company-required')
      return new DefaultErrorDto({
        code: 'Empty',
        message: 'Company required',
      })
    }

    const useCase = new GetCompanyNotificationItemUseCase()
    const res = await useCase.getNotify({
      uuid,
      companyUuid,
    })

    if (isErr(res)) {
      logger.error('get-company-notification-item', res.error)

      return new DefaultErrorDto({
        code: 'Bad',
        message: 'cannot get notification',
      })
    }

    return new CompanyNotificationDto(
      CompanyNotificationMapper.toDto(res.data.item),
    )
  }
}
