import { ArgsType, Field } from 'type-graphql'

import { PaginationCursorArgsDto } from '../../pagination/dtos/pagination-cursor-args.dto'
import { Valid } from '../../validator'

@ArgsType()
export class GetTemplatesListArgsDto {
  @Valid(Valid.scheme.string().max(2048).optional())
  @Field({ nullable: true })
  templateTitleOrContent?: string

  @Field((_type) => PaginationCursorArgsDto, { nullable: true })
  @Valid(PaginationCursorArgsDto, { isOptional: true })
  pagination?: PaginationCursorArgsDto
}
