import { ObjectType, registerEnumType } from 'type-graphql'

import { ErrorFabric } from '../../app/dtos/error-fabric.dto'

export enum OpenAITokenParsingErrorsEnum {
  UnknownError = 'UnknownError',
  InvalidResponseStructure = 'InvalidResponseStructure',
  NoTokenFound = 'NoTokenFound',
  ErrorParsingToken = 'ErrorParsingToken',
}

registerEnumType(OpenAITokenParsingErrorsEnum, {
  name: 'AIMessageErrorsEnum',
})
@ObjectType('OpenAITokenParsingError')
export class OpenAITokenParsingErrorDto extends ErrorFabric<
  keyof typeof OpenAITokenParsingErrorsEnum
>(OpenAITokenParsingErrorsEnum) {}
