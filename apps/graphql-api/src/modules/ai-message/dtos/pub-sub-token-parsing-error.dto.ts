import { ObjectType, registerEnumType } from 'type-graphql'

import { ErrorFabric } from '../../app/dtos/error-fabric.dto'

export enum PubSubTokenParsingErrorsEnum {
  UnknownError = 'UnknownError',
  InvalidResponseStructure = 'InvalidResponseStructure',
  NoTokenFound = 'NoTokenFound',
  ErrorParsingToken = 'ErrorParsingToken',
}

registerEnumType(PubSubTokenParsingErrorsEnum, {
  name: 'PubSubTokenParsingErrorsEnum',
})
@ObjectType('PubSubTokenParsingError')
export class PubSubTokenParsingErrorDto extends ErrorFabric<
  keyof typeof PubSubTokenParsingErrorsEnum
>(PubSubTokenParsingErrorsEnum) {}
