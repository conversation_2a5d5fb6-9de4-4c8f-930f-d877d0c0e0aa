import { isErr, type Result } from '@repo/result'
import {
  Arg,
  Authorized,
  Ctx,
  Mutation,
  Resolver,
  Root,
  Subscription,
} from 'type-graphql'

import { createLogger } from '../../app'
import { DefaultErrorDto } from '../../app/dtos/default-error.dto'
import { StatusOkDto } from '../../app/dtos/status-ok.dto'
import { ValidateArgs } from '../../validator'
import { AIMessageSubscriptionResponseDto } from '../dtos/ai-message-subscription-response.dto'
import { OpenAITokenDto } from '../dtos/open-ai-token.dto'
import { OpenAITokenParsingErrorDto } from '../dtos/open-ai-token-parsing-error.dto'
import { StreamAIMessageResponseUnion } from '../dtos/stream-ai-response.union'
import { CustomerMessageGenerationInput } from '../inputs/customer-message-generation-input.js'
import { createSystemPrompt } from '../interface/create-system-prompt'
import { createCustomerPrompt } from '../interface/customer-message/create-customer-prompt'
import { generateAIMessageStream } from '../interface/customer-message/generate-ai-message-stream'
import { getAiMessageChannel } from '../interface/customer-message/get-ai-message-channel'

import type { GQLServerContext } from '../../server/core/gql-server-context'

const logger = createLogger('ai-message.resolver')

// @deprecated, will be removed later
@Resolver()
export class AIMessageMutationResolver {
  @Authorized()
  @Subscription({
    topics: ({ context }) => getAiMessageChannel(context.auth?.sessionUuid),
  })
  aiMessageSubscription(
    @Arg('generationId') generationId: string,
    @Root()
    payload: {
      id: string
      result: Result<OpenAITokenDto, OpenAITokenParsingErrorDto>
    },
  ): AIMessageSubscriptionResponseDto {
    return {
      ...payload.result,
      id: generationId,
    }
  }

  @Authorized()
  @ValidateArgs(CustomerMessageGenerationInput)
  @Mutation((_returns) => StreamAIMessageResponseUnion)
  async startCustomerMessageGeneration(
    @Arg('input') input: CustomerMessageGenerationInput,
    @Ctx() ctx: GQLServerContext,
  ): Promise<typeof StreamAIMessageResponseUnion> {
    const sessionUuid = ctx.auth?.sessionUuid

    if (!sessionUuid) {
      logger.error('session-uuid-not-defined', {
        companyUserUuid: ctx.auth?.companyUserUuid,
        roles: ctx.auth?.roles,
      })
      return new DefaultErrorDto({ code: 'Bad' })
    }

    const prompt = createCustomerPrompt(input)
    const systemPrompt = createSystemPrompt(
      ctx.userRelations?.settings?.common.ai_settings.personalization_prompt,
    )

    const res = await generateAIMessageStream({
      prompt,
      systemPrompt,
      onPublish(result) {
        ctx.pubSub.publish(getAiMessageChannel(sessionUuid), {
          result,
        })
      },
    })

    if (isErr(res)) {
      logger.error('CustomerMessageGeneration', res.error)
      return new DefaultErrorDto({ code: 'Unknown' })
    }

    return new StatusOkDto()
  }
}
