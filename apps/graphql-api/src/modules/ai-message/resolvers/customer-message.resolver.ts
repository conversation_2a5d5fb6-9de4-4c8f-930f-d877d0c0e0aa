import { isErr, type Result } from '@repo/result'
import {
  Arg,
  Authorized,
  Ctx,
  Mutation,
  Resolver,
  Root,
  Subscription,
} from 'type-graphql'

import { createLogger } from '../../app'
import { DefaultErrorDto } from '../../app/dtos/default-error.dto'
import { StatusOkDto } from '../../app/dtos/status-ok.dto'
import { ValidateArgs } from '../../validator'
import { CustomerMessageSubscriptionResponsesDto } from '../dtos/customer-message-subscription-responses.dto'
import { PubSubResponseUnion } from '../dtos/pub-sub-response.union'
import { PubSubTokenDto } from '../dtos/pub-sub-token.dto'
import { PubSubTokenParsingErrorDto } from '../dtos/pub-sub-token-parsing-error.dto'
import { StartGenerateCustomerMessageInput } from '../inputs/start-generate-customer-message-input'
import { createSystemPrompt } from '../interface/create-system-prompt'
import { createCustomerPrompt } from '../interface/customer-message/create-customer-prompt'
import { generateAIMessageStream } from '../interface/customer-message/generate-ai-message-stream'
import { getCustomerMessageChannel } from '../interface/customer-message/get-customer-message-channel'

import type { GQLServerContext } from '../../server/core/gql-server-context'

const logger = createLogger('customer-message.resolver')

@Resolver()
export class CustomerMessageResolver {
  @Authorized()
  @Subscription({
    topics: ({ context }) =>
      getCustomerMessageChannel(context.auth.sessionUuid),
  })
  generateCustomerMessageSubscription(
    @Root()
    payload: {
      result: Result<PubSubTokenDto, PubSubTokenParsingErrorDto>
    },
  ): CustomerMessageSubscriptionResponsesDto {
    return payload.result
  }

  @Authorized()
  @ValidateArgs(StartGenerateCustomerMessageInput)
  @Mutation((_returns) => PubSubResponseUnion)
  async startGenerateCustomerMessage(
    @Arg('input') input: StartGenerateCustomerMessageInput,
    @Ctx() ctx: GQLServerContext,
  ): Promise<typeof PubSubResponseUnion> {
    const sessionUuid = ctx.auth?.sessionUuid

    if (!sessionUuid) {
      logger.error('session-uuid-not-defined', {
        companyUserUuid: ctx.auth?.companyUserUuid,
        roles: ctx.auth?.roles,
      })
      return new DefaultErrorDto({ code: 'Bad' })
    }

    const prompt = createCustomerPrompt(input)
    const systemPrompt = createSystemPrompt(
      ctx.userRelations?.settings?.common.ai_settings.personalization_prompt,
    )

    const res = await generateAIMessageStream({
      prompt,
      systemPrompt,
      onPublish(result) {
        ctx.pubSub.publish(getCustomerMessageChannel(sessionUuid), {
          result,
        })
      },
    })

    if (isErr(res)) {
      logger.error('generate-fail', res.error)
      return new DefaultErrorDto({ code: 'Unknown' })
    }

    return new StatusOkDto()
  }
}
