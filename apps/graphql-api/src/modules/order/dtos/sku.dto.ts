import { Field, ID, Int, ObjectType } from 'type-graphql'

@ObjectType('Sku')
export class SkuDto {
  @Field((_type) => ID)
  uuid: string

  @Field()
  name: string

  @Field()
  code: string

  @Field({ nullable: true })
  imageUrl?: string

  constructor(skuDto: {
    uuid: string
    name: string
    code: string
    imageUrl?: string
  }) {
    this.code = skuDto.code
    this.uuid = skuDto.uuid
    this.imageUrl = skuDto.imageUrl ?? undefined
    this.name = skuDto.name
  }
}
