import { assertTypes } from '@repo/result'
import { registerEnumType } from 'type-graphql'

import type { DBOrderStatusValueObject } from '@emma/storage'
import type { TypeEqualityGuard } from '@repo/result'

export enum OrderStatus {
  Created = 'Created',
  Processed = 'Processed',
  Shipped = 'Shipped',
  Delivered = 'Delivered',
  Canceled = 'Canceled',
}

type keys = keyof typeof OrderStatus

assertTypes<TypeEqualityGuard<keys, DBOrderStatusValueObject>>()

registerEnumType(OrderStatus, {
  name: 'OrderStatus',
})
