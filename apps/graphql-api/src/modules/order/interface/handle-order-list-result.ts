import { isErr, type Result } from '@repo/result'

import { createLogger } from '../../app'
import { DefaultErrorDto } from '../../app/dtos/default-error.dto'
import { PaginationCursorMapper } from '../../pagination/core/pagination-cursor.mapper'
import { OrderMapper } from '../core/mappers/order.mapper'
import { OrderListDto } from '../dtos/order-list.dto'

import type {
  OrderErrorsValueObject,
  OrdersListValueObject,
} from '@emma/storage'

const logger = createLogger('handle-order-list-result')

export async function handleOrderListResult(
  res: Result<OrdersListValueObject, OrderErrorsValueObject>,
) {
  if (isErr(res)) {
    const { error } = res

    logger.error(error.key, error)

    if (error.key === 'OrderErrors.GetOrdersByUserPublicUuidNotExist') {
      return new DefaultErrorDto({
        code: 'Empty',
        message: error.message,
      })
    }

    return new DefaultErrorDto({
      code: 'Bad',
      message: error.message,
    })
  }

  return new OrderListDto({
    list: OrderMapper.toOrdersDtoList(res.data.list),
    pagination: PaginationCursorMapper.toDto(res.data),
  })
}
