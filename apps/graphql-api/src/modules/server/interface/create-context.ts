import { createLogger } from '../../app'
import { getHeadersFingerprint } from '../../auth/interface/tokens/get-headers-fingerprint'
import { getUser } from '../infra/get-user'

import { getAuth } from './get-auth'

import type { JobManager } from '@emma/jobs'
import type { IncomingHttpHeaders } from 'http'
import type { PubSubInstance } from '../../pubsub/core/pubsub.instance'
import type { GQLServerContext } from '../core/gql-server-context'

const isDev = process.env.NODE_ENV !== 'production'

const DEV_REQUEST_TIMEOUT = 10

const logger = createLogger('create-context')

async function waitDev(ms: number) {
  return new Promise((resolve) => {
    setTimeout(resolve, ms)
  })
}

export async function createContext({
  pubSub,
  method,
  headers,
  url,
  jobManager,
}: {
  pubSub: PubSubInstance
  method: string
  headers: IncomingHttpHeaders
  url: string
  jobManager: JobManager
}) {
  if (isDev) {
    // sometimes local dev is too fast, need a small delay to see loadings in UI
    await waitDev(DEV_REQUEST_TIMEOUT)
  }

  const { auth, isExpired } = getAuth(method, headers)

  const checkP = getHeadersFingerprint({
    headers,
  })

  const isHeadersCorrect = checkP === auth?.p

  const canGetUser = isHeadersCorrect && !isExpired

  // TODO: move outside from this. call this only when it needed
  const userRelations = canGetUser
    ? await getUser({
        userUuid: auth?.userUuid,
        companyUserUuid: auth?.companyUserUuid,
      })
    : undefined

  const isOtpEnabled = userRelations?.user.otp?.isEnabled

  const sessionUuid = auth?.sessionUuid

  if (sessionUuid) {
    logger.updateContext({
      sessionUuid,
    })
  }

  if (userRelations?.companyUser?.public_uuid) {
    logger.updateContext({
      companyUserUuid: userRelations.companyUser.public_uuid,
    })
  }

  const ctx: GQLServerContext = {
    mode: 'request',
    jobManager,
    auth,
    userRelations,
    isExpired,
    isOtpEnabled,
    isHeadersCorrect,
    headers,
    url,
    pubSub,
  }

  return ctx
}
