import { Field, ObjectType } from 'type-graphql'

import { PaginationCursorDto } from '../../pagination/dtos/pagination-cursor.dto'

import { ContactDto } from './contact.dto'

@ObjectType('ContactList', { description: '' })
export class ContactListDto {
  @Field((_type) => [ContactDto])
  list: ContactDto[]

  @Field((_type) => PaginationCursorDto)
  pagination: PaginationCursorDto

  constructor(value: ContactListDto) {
    this.list = value.list
    this.pagination = value.pagination
  }
}
