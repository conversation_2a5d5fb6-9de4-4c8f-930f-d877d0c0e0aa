import { Field, ID, ObjectType } from 'type-graphql'

import { AddressDto } from '../../address/dto/address.dto'
import { NoteDto } from '../../note/dto/note.dto'

import { ContactOrderDto } from './contact-order.dto'
import { ContactType } from './contact-type.enum'
import { PhoneDto } from './phone.dto'

@ObjectType('Contact')
export class ContactDto {
  @Field((_type) => ID)
  uuid: string

  @Field({ nullable: true })
  email?: string

  @Field({ nullable: true })
  firstName?: string

  @Field({ nullable: true })
  lastName?: string

  @Field((_type) => Date, { nullable: true })
  birthday?: Date | null

  @Field((_type) => PhoneDto, { nullable: true })
  phone?: PhoneDto

  @Field((_type) => ContactType, { nullable: true })
  type?: ContactType

  @Field((_type) => String, { nullable: true })
  referralLink?: string | null

  @Field((_type) => [NoteDto], { nullable: true })
  notes?: NoteDto[]

  @Field((_type) => [ContactOrderDto], { nullable: true })
  orders?: ContactOrderDto[]

  @Field((_type) => [ContactOrderDto], { nullable: true })
  upcomingOrders?: ContactOrderDto[]

  @Field((_type) => [AddressDto], { nullable: true })
  addresses?: AddressDto[]

  constructor(contactDto: ContactDto) {
    this.addresses = contactDto.addresses
    this.birthday = contactDto.birthday
    this.email = contactDto.email
    this.firstName = contactDto.firstName
    this.uuid = contactDto.uuid
    this.lastName = contactDto.lastName
    this.notes = contactDto.notes
    this.orders = contactDto.orders
    this.upcomingOrders = contactDto.upcomingOrders
    this.phone = contactDto.phone
    this.referralLink = contactDto.referralLink
    this.type = contactDto.type
  }
}
