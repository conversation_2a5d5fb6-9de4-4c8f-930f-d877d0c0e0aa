import { Field, Float, ID, ObjectType } from 'type-graphql'

import { AddressDto } from '../../address/dto/address.dto'
import { ItemDto } from '../../order/dtos/item.dto'
import { OrderStatus } from '../../order/dtos/order-status.enum'

@ObjectType('ContactOrder')
export class ContactOrderDto {
  @Field((_type) => ID)
  uuid: string

  @Field((_type) => OrderStatus, { nullable: true })
  status?: OrderStatus

  @Field({ nullable: true })
  type?: string

  @Field({ nullable: true })
  priceCurrency?: string

  @Field((_type) => Float, { nullable: true })
  totalCostCents?: number

  @Field((_type) => Float, { nullable: true })
  shippingCostCents?: number

  @Field({ nullable: true })
  trackingLink?: string

  @Field((_type) => AddressDto, { nullable: true })
  shippingAddress?: AddressDto

  @Field((_type) => AddressDto, { nullable: true })
  billingAddress?: AddressDto

  @Field((_type) => [ItemDto], { nullable: true })
  items?: ItemDto[]

  @Field((_type) => Date, { nullable: true })
  upcomingAt?: Date | null

  @Field({ nullable: true })
  createdAt?: Date

  constructor(contactOrderDto: ContactOrderDto) {
    this.billingAddress = contactOrderDto.billingAddress
    this.createdAt = contactOrderDto.createdAt
    this.uuid = contactOrderDto.uuid
    this.items = contactOrderDto.items
    this.priceCurrency = contactOrderDto.priceCurrency
    this.shippingAddress = contactOrderDto.shippingAddress
    this.shippingCostCents = contactOrderDto.shippingCostCents
    this.status = contactOrderDto.status
    this.totalCostCents = contactOrderDto.totalCostCents
    this.trackingLink = contactOrderDto.trackingLink
    this.type = contactOrderDto.type
    this.upcomingAt = contactOrderDto.upcomingAt
  }
}
