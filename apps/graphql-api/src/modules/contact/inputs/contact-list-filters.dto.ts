import { Field, InputType } from 'type-graphql'

import { Valid } from '../../validator'

@InputType('ContactListFilters')
export class ContactListFiltersDto {
  @Field({ nullable: true })
  @Valid(Valid.scheme.boolean().optional())
  customerType: boolean

  @Field({ nullable: true })
  @Valid(Valid.scheme.boolean().optional())
  contactType: boolean

  @Field({ nullable: true })
  @Valid(Valid.scheme.boolean().optional())
  birthdayThisMonth: boolean

  @Field({ nullable: true })
  @Valid(Valid.scheme.boolean().optional())
  birthdayNextMonth: boolean

  @Field({ nullable: true })
  @Valid(Valid.scheme.boolean().optional())
  orderedThisMonth: boolean

  @Field({ nullable: true })
  @Valid(Valid.scheme.boolean().optional())
  orderedLastMonth: boolean

  @Field({ nullable: true })
  @Valid(Valid.scheme.boolean().optional())
  ordered3_6MonthsAgo: boolean

  @Field({ nullable: true })
  @Valid(Valid.scheme.boolean().optional())
  ordered6plusMonthsAgo: boolean

  @Field({ nullable: true })
  @Valid(Valid.scheme.boolean().optional())
  subscriptionActive: boolean
}
