import { ArgsType, Field } from 'type-graphql'

import { PaginationCursorArgsDto } from '../../pagination/dtos/pagination-cursor-args.dto'
import { Valid } from '../../validator'
import { ContactListSortValueObject } from '../value-objects/contact-list-sort.value-object'

import { ContactListFiltersDto } from './contact-list-filters.dto'

@ArgsType()
export class GetContactsListArgsDto {
  @Field({ nullable: true })
  @Valid(Valid.scheme.string().max(2048).optional())
  contactEmailOrNameOrCity?: string

  @Field((_type) => ContactListSortValueObject, { nullable: true })
  @Valid(Valid.scheme.nativeEnum(ContactListSortValueObject).optional())
  sort?: ContactListSortValueObject

  @Field((_type) => ContactListFiltersDto, { nullable: true })
  @Valid(ContactListFiltersDto, { isOptional: true })
  filters?: ContactListFiltersDto

  @Field((_type) => PaginationCursorArgsDto, { nullable: true })
  @Valid(PaginationCursorArgsDto, { isOptional: true })
  pagination?: PaginationCursorArgsDto
}
