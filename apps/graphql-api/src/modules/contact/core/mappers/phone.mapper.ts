import { PhoneDto } from '../../dtos/phone.dto'

import type { ContactWithRelationsValueObject } from '@emma/storage'

export class PhoneMapper {
  static toPhoneDto(
    phone: Pick<
      ContactWithRelationsValueObject['contact'],
      'phone_country_code' | 'phone_number'
    >,
  ): PhoneDto {
    return new PhoneDto({
      countryCode: phone.phone_country_code,
      number: phone.phone_number,
    })
  }
}
