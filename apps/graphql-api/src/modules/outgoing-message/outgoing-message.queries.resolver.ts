import { isErr } from '@repo/result'
import { Args, Authorized, Ctx, Query, Resolver } from 'type-graphql'

import {
  GetOutgoingMessageListUseCase,
  GetOutgoingMessageUseCase,
} from '@emma/storage'

import { DefaultErrorDto } from '../app/dtos/default-error.dto'
import { createLogger } from '../app/interface/logger'
import { PaginationCursorMapper } from '../pagination/core/pagination-cursor.mapper'
import { ValidateArgs } from '../validator/interface/validate-args'

import {
  outgoingMessageListMapper,
  outgoingMessageMapper,
} from './core/mappers/outgoing-message.mapper'
import { OutgoingMessageUnion } from './dtos/outgoing-message.union'
import { OutgoingMessageListDto } from './dtos/outgoing-message-list.dto'
import { OutgoingMessageListUnion } from './dtos/outgoing-message-list.union'
import { GetOutgoingMessageArgs } from './inputs/get-outgoing-message-args.dto'
import { GetOutgoingMessageListArgs } from './inputs/get-outgoing-message-list-args.dto'

import type { GQLServerContext } from '../server/core/gql-server-context'

const logger = createLogger('outgoing-message.queries')

@Resolver()
@Authorized()
export class OutgoingMessageQueriesResolver {
  @Query(() => OutgoingMessageUnion)
  @ValidateArgs(GetOutgoingMessageArgs)
  async getOutgoingMessage(
    @Args() { uuid }: GetOutgoingMessageArgs,
    @Ctx() context: GQLServerContext,
  ): Promise<typeof OutgoingMessageUnion> {
    const useCase = new GetOutgoingMessageUseCase()
    const result = await useCase.getOutgoingMessage({ uuid })

    if (isErr(result)) {
      logger.error('get-outgoing-message', {
        uuid,
        error: result.error,
      })
      return new DefaultErrorDto({
        code: 'Bad',
        message: result.error.message,
      })
    }

    return outgoingMessageMapper(result.data)
  }

  @Query(() => OutgoingMessageListUnion)
  @ValidateArgs(GetOutgoingMessageListArgs)
  async getOutgoingMessageList(
    @Args() { contactUuid, pagination, searchTerm }: GetOutgoingMessageListArgs,
    @Ctx() context: GQLServerContext,
  ): Promise<typeof OutgoingMessageListUnion> {
    const useCase = new GetOutgoingMessageListUseCase()

    const result = await useCase.getOutgoingMessageList({
      companyUserUuid: context.auth?.companyUserUuid || '',
      contactUuid,
      pagination,
      searchTerm,
    })

    if (isErr(result)) {
      logger.error('get-outgoing-message-list', {
        contactUuid,
        error: result.error,
      })
      return new DefaultErrorDto({
        code: 'Bad',
        message: result.error.message,
      })
    }

    return new OutgoingMessageListDto({
      list: outgoingMessageListMapper(result.data.list),
      pagination: PaginationCursorMapper.toDto(result.data),
    })
  }
}
