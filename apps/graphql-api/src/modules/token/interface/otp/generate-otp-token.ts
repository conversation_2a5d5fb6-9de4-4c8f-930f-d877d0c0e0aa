import jwt from 'jsonwebtoken'

import { TOKEN_ALGO } from '../token-algo'

import type { OptTokenDataValueObject } from '../../core/opt-token-data.value-object'

const EXPIRES_IN = '10m'

export function generateOtpToken(payload: OptTokenDataValueObject): {
  token: string
} {
  const token = jwt.sign(payload, process.env.JWT_SECRET_KEY, {
    expiresIn: EXPIRES_IN,
    algorithm: TOKEN_ALGO,
  })

  return {
    token,
  }
}
