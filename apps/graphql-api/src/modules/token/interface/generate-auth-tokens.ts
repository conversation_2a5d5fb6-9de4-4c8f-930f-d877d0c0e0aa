import jwt from 'jsonwebtoken'

import { TokensDto } from '../../auth/dtos/tokens.dto'

import { TOKEN_ALGO } from './token-algo'

import type { StoredTokenDataValueObject } from '../../auth/core/stored-token-data.value-object'

const ACCESS_TOKEN_EXPIRE = '20m'
const REFRESH_TOKEN_EXPIRE = '30d'

export function generateAccessToken(
  payload: StoredTokenDataValueObject,
): string {
  return jwt.sign(payload, process.env.AUTH_ACCESS_SECRET_KEY, {
    expiresIn: ACCESS_TOKEN_EXPIRE,
    algorithm: TOKEN_ALGO,
  })
}

export function generateAuthTokens(
  payload: StoredTokenDataValueObject,
): TokensDto {
  const accessToken = generateAccessToken(payload)

  const refreshToken = jwt.sign(payload, process.env.AUTH_REFRESH_SECRET_KEY, {
    expiresIn: REFRESH_TOKEN_EXPIRE,
    algorithm: TOKEN_ALGO,
  })

  return new TokensDto({
    accessToken,
    refreshToken,
  })
}
