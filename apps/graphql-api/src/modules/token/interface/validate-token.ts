import { resultErr, resultOk } from '@repo/result'
import jwt from 'jsonwebtoken'

import { TOKEN_ALGO } from './token-algo'

import type { Result } from '@repo/result'

export function validateToken<T>(
  value: string | undefined,
  secret: string,
): Result<T, { status: 'fail' | 'no-value' | 'expired' }> {
  try {
    if (!value) return resultErr({ status: 'no-value' })
    const result = jwt.verify(value, secret, {
      algorithms: [TOKEN_ALGO],
    }) as T
    return resultOk(result)
  } catch (e: unknown) {
    if (e instanceof jwt.TokenExpiredError) {
      return resultErr({ status: 'expired' })
    }

    // here we can split verify by types, if we need it
    return resultErr({ status: 'fail', error: e })
  }
}
