import { Field, ID, InputType } from 'type-graphql'

import { Valid } from '../../validator'
import { AssistantMessageTypeEnum } from '../core/enums/assistant-message-type.enum'

import type { DBAssistantMessageHistoryItemTypeValueObject } from '@emma/storage'

@InputType()
export class SendMessageInput {
  @Field(() => ID, { nullable: true })
  @Valid(Valid.scheme.string().uuid().optional(), { isOptional: true })
  conversationId?: string

  @Field()
  @Valid(Valid.scheme.string().max(32000))
  message: string

  @Field(() => String, { nullable: true })
  @Valid(Valid.scheme.string().optional(), { isOptional: true })
  systemPrompt?: string

  @Field(() => AssistantMessageTypeEnum)
  @Valid(Valid.scheme.nativeEnum(AssistantMessageTypeEnum))
  type: DBAssistantMessageHistoryItemTypeValueObject
}
