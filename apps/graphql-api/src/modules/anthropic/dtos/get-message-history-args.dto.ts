import { ArgsType, Field } from 'type-graphql'

import { PaginationCursorArgsDto } from '../../pagination/dtos/pagination-cursor-args.dto'
import { Valid } from '../../validator'
import { AssistantMessageTypeEnum } from '../core/enums/assistant-message-type.enum'

import type { DBAssistantMessageHistoryItemTypeValueObject } from '@emma/storage'

@ArgsType()
export class GetMessageHistoryArgsDto {
  @Valid(Valid.scheme.nativeEnum(AssistantMessageTypeEnum).optional())
  @Field((_type) => AssistantMessageTypeEnum)
  type: DBAssistantMessageHistoryItemTypeValueObject

  @Valid(PaginationCursorArgsDto, { isOptional: true })
  @Field((_type) => PaginationCursorArgsDto, { nullable: true })
  pagination?: PaginationCursorArgsDto
}
