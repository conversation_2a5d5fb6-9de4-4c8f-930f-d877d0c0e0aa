import { isErr } from '@repo/result'
import { Arg, Authorized, Ctx, Mutation, Resolver } from 'type-graphql'
import { v4 as uuidv4 } from 'uuid'

import { AddAssistantMessageHistoryItemUseCase } from '@emma/storage'

import { createLogger } from '../../app'
import { AnthropicClientService } from '../../mcp-tools/interface/anthropic-client.service'
import { getSystemPrompt } from '../../mcp-tools/interface/get-system-prompt'
import { generateAccessToken } from '../../token/interface/generate-auth-tokens'
import { ValidateArgs } from '../../validator'
import { AiMessageDto } from '../dtos/ai-message.dto'
import { SendMessageInput } from '../inputs/send-message-input.dto'

import type { GQLServerContext } from '../../server/core/gql-server-context'

const logger = createLogger('ai-message.mutations')

@Resolver()
export class AiMessageMutationsResolver {
  constructor(
    private readonly anthropicClient = new AnthropicClientService(),
  ) {}

  @Authorized()
  @Mutation(() => AiMessageDto)
  @ValidateArgs(SendMessageInput)
  async sendAiMessage(
    @Arg('input', () => SendMessageInput) input: SendMessageInput,
    @Ctx() context: GQLServerContext,
  ): Promise<AiMessageDto> {
    try {
      const addAssistantMessageHistoryItemUseCase =
        new AddAssistantMessageHistoryItemUseCase()
      const { pubSub } = context
      const conversationId = input.conversationId || uuidv4()

      const result = await addAssistantMessageHistoryItemUseCase.handle({
        companyUserUuid: context.auth?.companyUserUuid || '',
        conversationUuid: conversationId,
        message: input.message,
        type: input.type,
      })

      if (isErr(result)) {
        logger.error(result.error.key, result.error)
      }

      // user token can have expiration in few seconds, let's try to generate new without link to old valid token
      const { exp, iat, ...auth } = context.auth!
      const mcpToken = generateAccessToken(auth)

      const messageId = await this.anthropicClient.streamMessage({
        prompt: input.message,
        conversationId,
        systemPrompt: getSystemPrompt(),
        userToken: mcpToken,
        onPublish(aiMessageStream) {
          pubSub.publish(`AI_MESSAGE_STREAM_${conversationId}`, {
            aiMessageStream,
          })
        },
      })

      return {
        id: messageId,
        role: 'assistant',
        content: '',
        createdAt: new Date(),
        isComplete: false,
      }
    } catch (error) {
      console.error('Error in sendAiMessage resolver:', error)
      throw error
    }
  }
}
