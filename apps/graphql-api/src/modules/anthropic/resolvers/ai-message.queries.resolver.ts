import { isErr } from '@repo/result'
import { Args, Authorized, Ctx, Query, Resolver } from 'type-graphql'

import { GetAssistantMessageHistoryUseCase } from '@emma/storage'

import { createLogger } from '../../app'
import { DefaultErrorDto } from '../../app/dtos/default-error.dto'
import { PaginationCursorMapper } from '../../pagination/core/pagination-cursor.mapper'
import { ValidateArgs } from '../../validator'
import { AssistantMessageHistoryItemMapper } from '../core/mappers/assistant-message-history.mapper'
import { AssistantMessageHistoryDto } from '../dtos/assistant-message-history.dto'
import { AssistantMessageHistoryUnion } from '../dtos/assistant-message-history.union'
import { GetMessageHistoryArgsDto } from '../dtos/get-message-history-args.dto'

import type { GQLServerContext } from '../../server/core/gql-server-context'

const logger = createLogger('assistant.queries.resolver')

@Authorized()
@Resolver()
export class AiMesssageQueriesResolver {
  @Query((_returns) => AssistantMessageHistoryUnion)
  @ValidateArgs(GetMessageHistoryArgsDto)
  async getAssistantMessageHistory(
    @Args()
    { type, pagination }: GetMessageHistoryArgsDto,
    @Ctx() ctx: GQLServerContext,
  ): Promise<typeof AssistantMessageHistoryUnion> {
    const companyUserUuid = ctx.auth?.companyUserUuid

    const useCase = new GetAssistantMessageHistoryUseCase()
    const res = await useCase.getAssistantMessageHistory({
      companyUserUuid: companyUserUuid || '',
      type,
      pagination,
    })

    if (isErr(res)) {
      const { error } = res
      logger.error(error.key, error)

      return new DefaultErrorDto({
        code: 'Bad',
        message: 'Cannot get assistant message history',
      })
    }

    return new AssistantMessageHistoryDto({
      list: res.data.list.map(AssistantMessageHistoryItemMapper.toDto),
      pagination: PaginationCursorMapper.toDto(res.data),
    })
  }
}
