import { AssistantMessageHistoryItemDto } from '../../dtos/assistant-message-history-item.dto'
import { AssistantMessageTypeEnum } from '../enums/assistant-message-type.enum'

import type { DBAssistantMessageHistoryValueObject } from '@emma/storage'

export class AssistantMessageHistoryItemMapper {
  static toDto(
    prompt: DBAssistantMessageHistoryValueObject,
  ): AssistantMessageHistoryItemDto {
    return new AssistantMessageHistoryItemDto({
      uuid: prompt.public_uuid,
      message: prompt.message,
      type:
        prompt.type === 'Predefined'
          ? AssistantMessageTypeEnum.Predefined
          : AssistantMessageTypeEnum.UserTyped,
    })
  }
}
