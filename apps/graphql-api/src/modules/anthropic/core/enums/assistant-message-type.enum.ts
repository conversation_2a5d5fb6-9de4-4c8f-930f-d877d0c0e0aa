import { assertTypes, type TypeEqualityGuard } from '@repo/result'
import { registerEnumType } from 'type-graphql'

import type { DBAssistantMessageHistoryItemTypeValueObject } from '@emma/storage'

export enum AssistantMessageTypeEnum {
  Predefined = 'Predefined',
  UserTyped = 'UserTyped',
}

type Keys = keyof typeof AssistantMessageTypeEnum

assertTypes<
  TypeEqualityGuard<Keys, DBAssistantMessageHistoryItemTypeValueObject>
>()

registerEnumType(AssistantMessageTypeEnum, {
  name: 'AssistantMessageTypeEnum',
})
