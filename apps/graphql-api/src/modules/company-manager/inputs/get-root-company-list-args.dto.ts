import { ArgsType, Field } from 'type-graphql'

import { PaginationCursorArgsDto } from '../../pagination/dtos/pagination-cursor-args.dto'
import { Valid } from '../../validator'

@ArgsType()
export class GetRootCompanyListArgsDto {
  @Field((_type) => String, { nullable: true })
  name?: string

  @Field((_type) => PaginationCursorArgsDto, { nullable: true })
  @Valid(PaginationCursorArgsDto, { isOptional: true })
  pagination?: PaginationCursorArgsDto
}
