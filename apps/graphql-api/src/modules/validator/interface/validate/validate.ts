import { handleValidate } from '@repo/validator'

import { getSchema } from './get-schema'

type SuccessValidation = {
  isValid: true
}

type FailedValidation<T> = {
  isValid: false
  errors: ValidationRecord<T>
}

export type ValidationRecord<T = unknown> = {
  [P in keyof T]: string
}

export function validate<T extends { [x: string]: any }>(
  values: T,
): SuccessValidation | FailedValidation<T> {
  const scheme = getSchema(values)
  const result = handleValidate(scheme, values)

  if (result.isValid) {
    return { isValid: true }
  }

  return {
    isValid: false,
    errors: result.errors as ValidationRecord<T>,
  }
}
