import { Field, ObjectType } from 'type-graphql'

import { ValidationRecord } from '../interface/validate/validate'

import { ValidationItemDto } from './validation-item.dto'

type ValidKey<T extends string> = T | 'invalid'

export function ValidationFabric<
  Key extends string = string,
  TObject extends object = object,
>(code?: TObject) {
  @ObjectType()
  class ValidationClass {
    @Field((type) => String)
    icode: ValidKey<Key>

    @Field((_type) => [ValidationItemDto], { nullable: true })
    fields: ValidationItemDto[]

    constructor({
      fields,
      code,
    }: {
      code?: ValidKey<Key>
      message?: string
      fields: ValidationRecord
    }) {
      this.icode = code || 'invalid'
      this.fields = Object.entries(fields).map(([name, message]) => {
        return new ValidationItemDto({
          name,
          message: (message as string) || '',
        })
      })
    }
  }

  return ValidationClass
}

@ObjectType('ValidationError')
export class ValidationErrorDto extends ValidationFabric() {}
