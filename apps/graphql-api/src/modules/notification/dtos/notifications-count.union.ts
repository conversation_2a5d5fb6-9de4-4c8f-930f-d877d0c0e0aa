import { createUnionType } from 'type-graphql'

import { DefaultErrorDto } from '../../app/dtos/default-error.dto'
import { ValidationErrorDto } from '../../validator'

import { NotificationsCountDto } from './notifications-count.dto'

export const NotificationsCountResultUnion = createUnionType({
  name: 'NotificationsCountResult',
  types: () =>
    [NotificationsCountDto, DefaultErrorDto, ValidationErrorDto] as const,
})
