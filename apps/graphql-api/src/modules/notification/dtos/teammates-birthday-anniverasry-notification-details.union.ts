import { createUnionType } from 'type-graphql'

import { DefaultErrorDto } from '../../app/dtos/default-error.dto'
import { ValidationErrorDto } from '../../validator'

import { TeammatesBirthdayNotificationDetailsDto } from './teammates-birthday-notification-details.dto'

export const TeammatesBirthdayNotificationDetailsResultUnion = createUnionType({
  name: 'TeammatesBirthdayNotificationDetailsResult',
  types: () =>
    [
      TeammatesBirthdayNotificationDetailsDto,
      DefaultErrorDto,
      ValidationErrorDto,
    ] as const,
})
