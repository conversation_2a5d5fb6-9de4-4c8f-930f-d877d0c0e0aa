import {
  NotificationMetaCustomerBirthdayDto,
  NotificationMetaGroupBirthdayDto,
} from './birtdays.dto'
import { NotificationTBCHomeOfficeDto } from './home-office.dto'
import {
  NotificationMetaCorpOrderDto,
  NotificationMetaOrderFollowup2monDto,
  NotificationMetaOrderFollowup2wDto,
  NotificationMetaOrderFollowup6monDto,
  NotificationMetaOrderNewDto,
} from './orders.dto'
import {
  NotificationMetaCustomerDiscountExpirationDto,
  NotificationMetaSubscriptionCustomerCancelsDto,
  NotificationMetaSubscriptionCustomerPausedDto,
  NotificationMetaSubscriptionCustomerPaymentFailedDto,
  NotificationMetaSubscriptionCustomerSkippedDto,
  NotificationMetaSubscriptionReminderDto,
} from './subscriptions.dto'
import {
  NotificationMetaTeamAnniversariesDto,
  NotificationMetaTeamBirthdaysDto,
  NotificationMetaTeamNewRecruitDownlineDto,
  NotificationMetaTeamNewRecruitDto,
  NotificationMetaTeamStatusChangeDto,
} from './team.dto'

import type {
  ContactBaseNotificationMetaDto,
  TeamBaseNotificationMetaDto,
} from './base-meta.dto'
import type { NotificationSubtypeEnum } from './notification-subtype.enum'

type AllClasses =
  | typeof NotificationTBCHomeOfficeDto
  | typeof TeamBaseNotificationMetaDto
  | typeof ContactBaseNotificationMetaDto

const notifyList = [
  NotificationMetaTeamNewRecruitDto,
  NotificationMetaTeamBirthdaysDto,
  NotificationMetaTeamNewRecruitDownlineDto,
  NotificationMetaTeamStatusChangeDto,
  NotificationMetaTeamAnniversariesDto,
  NotificationMetaCustomerDiscountExpirationDto,
  NotificationMetaCustomerBirthdayDto,
  NotificationMetaGroupBirthdayDto,
  NotificationTBCHomeOfficeDto,
  NotificationMetaOrderNewDto,
  NotificationMetaOrderFollowup6monDto,
  NotificationMetaCorpOrderDto,
  NotificationMetaOrderFollowup2monDto,
  NotificationMetaOrderFollowup2wDto,
  NotificationMetaSubscriptionReminderDto,
  NotificationMetaSubscriptionCustomerSkippedDto,
  NotificationMetaSubscriptionCustomerPausedDto,
  NotificationMetaSubscriptionCustomerPaymentFailedDto,
  NotificationMetaSubscriptionCustomerCancelsDto,
]

function getNotifyMap(): Record<NotificationSubtypeEnum, AllClasses> {
  const res = notifyList.reduce(
    (acc, ClassNode) => {
      acc[ClassNode.subtype] = ClassNode
      return acc
    },
    {} as Record<NotificationSubtypeEnum, AllClasses>,
  )
  return res
}

export const notificationMap = getNotifyMap()

export const notificationTypes = Object.values(notificationMap)
