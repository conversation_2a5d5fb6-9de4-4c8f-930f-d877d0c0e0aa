import { assertTypes, type TypeEqualityGuard } from '@repo/result'
import { registerEnumType } from 'type-graphql'

import type { DBNotificationAllSubtypeValueObject } from '@emma/storage'

export enum NotificationSubtypeEnum {
  CustomerBirthday = 'CustomerBirthday',
  GroupBirthday = 'GroupBirthday',

  NewOrder = 'NewOrder',
  OrderFollowup2w = 'OrderFollowup2w',
  OrderFollowup2mon = 'OrderFollowup2mon',
  OrderFollowup6mon = 'OrderFollowup6mon',
  CorpOrderAssigned = 'CorpOrderAssigned',

  SubscriptionReminder = 'SubscriptionReminder',
  SubscriptionCustomerCancels = 'SubscriptionCustomerCancels',
  SubscriptionCustomerSkipped = 'SubscriptionCustomerSkipped',
  SubscriptionCustomerPaused = 'SubscriptionCustomerPaused',
  SubscriptionCustomerPaymentFailed = 'SubscriptionCustomerPaymentFailed',
  TeamNewRecruit = 'TeamNewRecruit',
  TeamNewRecruitDownline = 'TeamNewRecruitDownline',
  TeamStatusChange = 'TeamStatusChange',
  TeamAnniversaries = 'TeamAnniversaries',
  TeamBirthdays = 'TeamBirthdays',
  CustomerDiscountExpiration = 'CustomerDiscountExpiration',

  TBC = 'TBC',
}

type Keys = keyof typeof NotificationSubtypeEnum

assertTypes<TypeEqualityGuard<Keys, DBNotificationAllSubtypeValueObject>>()

registerEnumType(NotificationSubtypeEnum, {
  name: 'NotificationSubtypeEnum',
})
