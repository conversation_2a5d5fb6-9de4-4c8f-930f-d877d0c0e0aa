import { assertTypes, type TypeEqualityGuard } from '@repo/result'
import { registerEnumType } from 'type-graphql'

import type { DBNotificationSubjectValueObject } from '@emma/storage'

export enum NotificationSubjectEnum {
  Birthday = 'Birthday',
  Order = 'Order',
  Subscription = 'Subscription',
  Team = 'Team',
  HomeOffice = 'HomeOffice',
  Customer = 'Customer',
}

type Keys = keyof typeof NotificationSubjectEnum

assertTypes<TypeEqualityGuard<Keys, DBNotificationSubjectValueObject>>()

registerEnumType(NotificationSubjectEnum, {
  name: 'NotificationSubjectEnum',
})
