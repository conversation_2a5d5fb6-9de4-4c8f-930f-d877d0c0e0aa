import { Field, ObjectType } from 'type-graphql'

@ObjectType('NotificationContent')
export class NotificationContentDto {
  @Field((_type) => String)
  title: string

  @Field((_type) => String)
  html: string

  @Field((_type) => String)
  pushTitle: string

  @Field((_type) => String)
  pushDesc: string

  constructor(notificationDto: NotificationContentDto) {
    this.title = notificationDto.title || ''
    this.html = notificationDto.html || ''
    this.pushTitle = notificationDto.pushTitle || ''
    this.pushDesc = notificationDto.pushDesc || ''
  }
}
