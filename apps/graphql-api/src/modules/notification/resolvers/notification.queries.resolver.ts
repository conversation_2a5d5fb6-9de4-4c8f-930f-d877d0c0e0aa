import { isErr } from '@repo/result'
import { Args, Authorized, Ctx, Query, Resolver } from 'type-graphql'

import {
  GetAnniversaryNotificationDetailsUseCase,
  GetDiscountsExpirationDetailsUseCase,
  GetNotificationListUseCase,
  GetNotificationUseCase,
  GetTeammatesBirthdayNotificationDetailsUseCase,
} from '@emma/storage'

import { createLogger } from '../../app'
import { DefaultErrorDto } from '../../app/dtos/default-error.dto'
import { PaginationCursorMapper } from '../../pagination/core/pagination-cursor.mapper'
import { ValidateArgs } from '../../validator'
import { AnniversaryNotificationDetailsMapper } from '../core/mappers/anniversary-notification-details.mapper'
import { CustomerDiscountsExpirationNotificationDetailsMapper } from '../core/mappers/customer-discounts-expiration-notification-details.mapper'
import { NotificationMapper } from '../core/mappers/notification.mapper'
import { NotificationCountMapper } from '../core/mappers/notification-count.mapper'
import { TeammatesBirthdayNotificationDetailsMapper } from '../core/mappers/teammates-birthday-notification-details.mapper'
import { AnniversaryNotificationDetailsResultUnion } from '../dtos/anniverasry-notification-details.union'
import { CustomerExpirationDiscountsNotificationDetailsResultUnion } from '../dtos/customer-expiring-discounts-notification-details.union'
import { NotificationItemDto } from '../dtos/notification-item.dto'
import { NotificationItemResultUnion } from '../dtos/notification-item.union'
import { NotificationListDto } from '../dtos/notification-list.dto'
import { NotificationListResultUnion } from '../dtos/notification-list.union'
import { NotificationsCountResultUnion } from '../dtos/notifications-count.union'
import { TeammatesBirthdayNotificationDetailsResultUnion } from '../dtos/teammates-birthday-anniverasry-notification-details.union'
import { GetAnniversaryNotificationDetailsArgsDto } from '../inputs/get-anniversary-notification-details-args.dto'
import { GetDiscountsExpirationDetailsArgsDto } from '../inputs/get-discounts-expiration-details-args.dto'
import { GetNotificationArgsDto } from '../inputs/get-notification-args.dto'
import { GetNotificationListArgsDto } from '../inputs/get-notification-list-args.dto'
import { GetTeammatesBirthdayNotificationDetailsArgsDto } from '../inputs/get-teammates-anniversary-notification-details-args.dto'

import type { GQLServerContext } from '../../server/core/gql-server-context'

const logger = createLogger('notification.resolver')

@Authorized()
@Resolver()
export class NotificationQueriesResolver {
  @ValidateArgs(GetNotificationListArgsDto)
  @Query((_returns) => NotificationListResultUnion)
  async getNotificationsList(
    @Args() { pagination, filters }: GetNotificationListArgsDto,
    @Ctx() ctx: GQLServerContext,
  ): Promise<typeof NotificationListResultUnion> {
    const useCase = new GetNotificationListUseCase()
    const notificationsResult = await useCase.getNotificationsByCompanyUserUuid(
      {
        companyUserUuid: ctx.auth?.companyUserUuid || '',
        pagination,
        filters,
      },
    )

    if (isErr(notificationsResult)) {
      const { error } = notificationsResult

      logger.error(error.key, error)

      if (error.key === 'NotificationErrors.NotExist') {
        return new DefaultErrorDto({
          code: 'Empty',
          message: error.message,
        })
      }

      return new DefaultErrorDto({
        code: 'Bad',
        message: error.message,
      })
    }

    return new NotificationListDto({
      list: NotificationMapper.toNotificationDtoList(
        notificationsResult.data.list,
      ),
      pagination: PaginationCursorMapper.toDto(notificationsResult.data),
    })
  }

  @ValidateArgs(GetNotificationArgsDto)
  @Query((_returns) => NotificationItemResultUnion)
  async getNotification(
    @Args() { uuid }: GetNotificationArgsDto,
    @Ctx() ctx: GQLServerContext,
  ): Promise<typeof NotificationItemResultUnion> {
    const useCase = new GetNotificationUseCase()
    const notificationsResult = await useCase.getNotificationByUuid({
      companyUserUuid: ctx.auth?.companyUserUuid || '',
      uuid,
    })

    if (isErr(notificationsResult)) {
      const { error } = notificationsResult

      logger.error(error.key, error)

      if (error.key === 'NotificationErrors.NotExist') {
        return new DefaultErrorDto({
          code: 'Empty',
          message: error.message,
        })
      }

      return new DefaultErrorDto({
        code: 'Bad',
        message: error.message,
      })
    }

    return new NotificationItemDto({
      notification: NotificationMapper.toNotificationDto(
        notificationsResult.data,
      ),
    })
  }

  @Query((_returns) => NotificationsCountResultUnion)
  async getUnreadNotificationsCount(
    @Ctx() ctx: GQLServerContext,
  ): Promise<typeof NotificationsCountResultUnion> {
    const useCase = new GetNotificationListUseCase()
    const notificationsCountResult = await useCase.getUnreadCount({
      companyUserUuid: ctx.auth?.companyUserUuid || '',
    })

    if (isErr(notificationsCountResult)) {
      const { error } = notificationsCountResult

      logger.error(error.key, error)

      if (error.key === 'NotificationErrors.NotExist') {
        return new DefaultErrorDto({
          code: 'Empty',
          message: error.message,
        })
      }

      return new DefaultErrorDto({
        code: 'Bad',
        message: error.message,
      })
    }

    return NotificationCountMapper.toNotificationsCountDto(
      notificationsCountResult.data,
    )
  }

  @Query((_returns) => AnniversaryNotificationDetailsResultUnion)
  async getAnniversaryNotificationDetails(
    @Ctx() ctx: GQLServerContext,
    @Args() { anniversaryTimestamp }: GetAnniversaryNotificationDetailsArgsDto,
  ): Promise<typeof AnniversaryNotificationDetailsResultUnion> {
    const useCase = new GetAnniversaryNotificationDetailsUseCase()
    const notificationsCountResult =
      await useCase.getNotificationsByCompanyUserUuid({
        companyUserUuid: ctx.auth?.companyUserUuid || '',
        anniversaryTimestamp,
      })

    if (isErr(notificationsCountResult)) {
      const { error } = notificationsCountResult

      logger.error(error.key, error)

      if (error.key === 'NotificationErrors.NotExist') {
        return new DefaultErrorDto({
          code: 'Empty',
          message: error.message,
        })
      }

      return new DefaultErrorDto({
        code: 'Bad',
        message: error.message,
      })
    }

    return AnniversaryNotificationDetailsMapper.toAnniversaryNotificationDetailsDto(
      notificationsCountResult.data,
    )
  }

  @Query((_returns) => TeammatesBirthdayNotificationDetailsResultUnion)
  async getTeammatesBirthdayNotificationDetails(
    @Ctx() ctx: GQLServerContext,
    @Args() { birthTimestamp }: GetTeammatesBirthdayNotificationDetailsArgsDto,
  ): Promise<typeof TeammatesBirthdayNotificationDetailsResultUnion> {
    const useCase = new GetTeammatesBirthdayNotificationDetailsUseCase()
    const notificationsCountResult = await useCase.getNotificationDetails({
      companyUserUuid: ctx.auth?.companyUserUuid || '',
      birthTimestamp,
    })

    if (isErr(notificationsCountResult)) {
      const { error } = notificationsCountResult

      logger.error(error.key, error)

      if (error.key === 'NotificationErrors.NotExist') {
        return new DefaultErrorDto({
          code: 'Empty',
          message: error.message,
        })
      }

      return new DefaultErrorDto({
        code: 'Bad',
        message: error.message,
      })
    }

    return TeammatesBirthdayNotificationDetailsMapper.toTeammatesBirthdayNotificationDetailsDto(
      notificationsCountResult.data,
    )
  }

  @Query(
    (_returns) => CustomerExpirationDiscountsNotificationDetailsResultUnion,
  )
  @ValidateArgs(GetDiscountsExpirationDetailsArgsDto)
  async getDiscountsExpirationDetails(
    @Ctx() ctx: GQLServerContext,
    @Args()
    { contactUuid, expirationTimestamp }: GetDiscountsExpirationDetailsArgsDto,
  ): Promise<typeof CustomerExpirationDiscountsNotificationDetailsResultUnion> {
    const useCase = new GetDiscountsExpirationDetailsUseCase()
    const amountResult = await useCase.getDiscountsExpirationDetails({
      contactUuid,
      expirationTimestamp,
    })

    if (isErr(amountResult)) {
      logger.error('getDiscountsExpirationDetails', amountResult.error)
      return new DefaultErrorDto({
        code: 'Bad',
      })
    }

    return CustomerDiscountsExpirationNotificationDetailsMapper.toCustomerDiscountsExpirationNotificationDetailsDto(
      amountResult.data,
    )
  }
}
