import { ArgsType, Field } from 'type-graphql'

import { PaginationCursorArgsDto } from '../../pagination/dtos/pagination-cursor-args.dto'
import { Valid } from '../../validator'

import { NotificationListFiltersInputDto } from './notification-list-filters.input.dto'

@ArgsType()
export class GetNotificationListArgsDto {
  @Field((_type) => PaginationCursorArgsDto, { nullable: true })
  @Valid(PaginationCursorArgsDto, { isOptional: true })
  pagination?: PaginationCursorArgsDto

  @Field((_type) => NotificationListFiltersInputDto, { nullable: true })
  @Valid(NotificationListFiltersInputDto, { isOptional: true })
  filters?: NotificationListFiltersInputDto
}
