import { NotificationDto } from '../../dtos/notification.dto'
import { NotificationContentDto } from '../../dtos/notification-content.dto'

import { NotificationMetaMapper } from './notification-meta.mapper'

import type {
  DBCompanyUserNotificationEntity,
  NotificationsListValueObject,
} from '@emma/storage'

export class NotificationMapper {
  static toNotificationDto(
    notification: DBCompanyUserNotificationEntity,
  ): NotificationDto {
    const notificationDto: NotificationDto = {
      uuid: notification.public_uuid,
      isRead: notification.is_read,
      scheduledOn: notification.scheduled_on,
      sentAt: notification.sent_at,
      content: new NotificationContentDto(notification.content),
      meta: NotificationMetaMapper.toNotificationMeta({
        meta: notification.meta,
        subject: notification.subject,
        subtype: notification.subtype,
      }),
      subject: notification.subject,
      subtype: notification.subtype,
    }

    return new NotificationDto(notificationDto)
  }

  static toNotificationDtoList(
    notificationsList: NotificationsListValueObject['list'],
  ): NotificationDto[] {
    return notificationsList.map(NotificationMapper.toNotificationDto)
  }
}
