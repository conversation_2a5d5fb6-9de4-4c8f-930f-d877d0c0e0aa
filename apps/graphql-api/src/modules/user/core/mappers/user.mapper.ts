import { UserDto } from '../../dtos/user.dto'

import type { DBUser } from '@emma/storage'

export class UserMapper {
  static toUserDto({ user }: { user: DBUser }): UserDto {
    return new UserDto({
      email: user.email,
      firstName: user.first_name,
      uuid: user.public_uuid,
      lastName: user.last_name,
      phone: {
        countryCode: user.phone_country_code,
        number: user.phone_number,
      },
      profileImage: user.avatar_url,
      isOtpEnabled: user.otp?.isEnabled ?? false,
    })
  }
}
