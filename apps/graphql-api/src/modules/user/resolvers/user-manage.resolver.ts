import { isErr } from '@repo/result'
import { Arg, Authorized, Ctx, Mutation, Resolver } from 'type-graphql'

import { ChangePwdUseCase, UpdateUserUseCase } from '@emma/storage'

import { createLogger } from '../../app'
import { DefaultErrorDto } from '../../app/dtos/default-error.dto'
import { StatusOkDto } from '../../app/dtos/status-ok.dto'
import { otpVerifyCode } from '../../otp/inteface/otp-verify-code'
import { ValidateArgs, ValidationErrorDto } from '../../validator'
import { ChangePasswordUnion } from '../dtos/change-password.union'
import { SendFeedbackUnion } from '../dtos/send-feedback.union'
import { UpdateUserUnion } from '../dtos/update-user.union'
import { ChangePasswordInput } from '../inputs/change-password-input.dto'
import { SendFeedbackInputDto } from '../inputs/send-feedback-input.dto'
import { UpdateUserInput } from '../inputs/update-user-input.dto'

import type { GQLServerContext } from '../../server/core/gql-server-context'

const logger = createLogger('user-manage.resolver')

@Authorized()
@Resolver()
export class UserManageResolver {
  @ValidateArgs(ChangePasswordInput)
  @Mutation((_returns) => ChangePasswordUnion)
  async changePassword(
    @Arg('ChangePasswordInput') input: ChangePasswordInput,
    @Ctx() ctx: GQLServerContext,
  ): Promise<typeof ChangePasswordUnion> {
    if (ctx.isOtpEnabled && !input.code) {
      return new ValidationErrorDto({
        fields: [{ name: 'code', message: 'Code Required' }],
      })
    }

    if (ctx.isOtpEnabled && input.code) {
      const verifyResult = await otpVerifyCode({
        email: ctx.userRelations?.user.email,
        base32: ctx.userRelations?.user.otp?.base32,
        code: input.code,
      })
      if (isErr(verifyResult)) {
        logger.error('opt-verify-error', verifyResult.error)
        return new DefaultErrorDto({
          code: 'OtpCodeInvalid',
        })
      }
    }

    if (input.old === input.next) {
      logger.error('pwd-mismatch')
      return new DefaultErrorDto({
        code: 'Empty',
        message: 'pwd must match',
      })
    }

    const useCase = new ChangePwdUseCase()
    const res = await useCase.changePwd({
      userUuid: ctx.auth?.userUuid!,
      oldPassword: input.old,
      newPassword: input.next,
    })

    if (isErr(res)) {
      logger.error('change-pwd', res.error)
      return new DefaultErrorDto({
        code: 'Bad',
      })
    }

    return new StatusOkDto()
  }

  @ValidateArgs(UpdateUserInput)
  @Mutation((_returns) => UpdateUserUnion)
  async updateUser(
    @Arg('UpdateUserInput') input: UpdateUserInput,
    @Ctx() ctx: GQLServerContext,
  ): Promise<typeof UpdateUserUnion> {
    const useCase = new UpdateUserUseCase()
    const res = await useCase.updateUser({
      userUuid: ctx.auth?.userUuid!,
      firstName: input.firstName,
      lastName: input.lastName,
    })

    if (isErr(res)) {
      logger.error('update-user', res.error)
      return new DefaultErrorDto({
        code: 'Bad',
      })
    }

    return new StatusOkDto()
  }

  @ValidateArgs(SendFeedbackInputDto)
  @Mutation((_returns) => SendFeedbackUnion)
  async sendFeedback(
    @Arg('FeedbackInput') input: SendFeedbackInputDto,
    @Ctx() ctx: GQLServerContext,
  ): Promise<typeof SendFeedbackUnion> {
    if (!ctx.userRelations) {
      logger.error('send-feedback-user-empty')
      return new DefaultErrorDto({
        code: 'Bad',
        message: 'user not exist',
      })
    }

    const user = ctx.userRelations.user
    const companyName = ctx.userRelations.company?.name
    const fio = `${user.first_name} ${user.last_name}`.trim()
    const userName = fio ? `${user.first_name} (${user.email})` : user.email

    const res = await fetch(process.env.FEEDBACK_WEBHOOK ?? '', {
      method: 'POST',
      body: JSON.stringify({
        text: 'New Feedback incoming',
        blocks: [
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: `*You've got new feedback*`,
            },
          },
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: `*User*: ${userName}`,
            },
          },
          companyName
            ? {
                type: 'section',
                text: {
                  type: 'mrkdwn',
                  text: `*Company*: ${companyName}`,
                },
              }
            : undefined,
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: `${input.feedback}`,
            },
          },
        ].filter(Boolean),
      }),
    })

    if (!res.ok) {
      logger.error('send-feedback', res)
      return new DefaultErrorDto({
        code: 'Bad',
        message: 'send error',
      })
    }
    return new StatusOkDto()
  }
}
