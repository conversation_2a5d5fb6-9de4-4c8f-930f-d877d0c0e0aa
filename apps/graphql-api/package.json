{"name": "@emma/graphql-api", "version": "1.0.0", "type": "module", "scripts": {"build": "dotenv-run -- tsup && pnpm sentry:build", "test:unit": "repo-unit", "check-types": "tsc", "clean": "rimraf ./.cache ./.turbo ./dist", "clean:node_modules": "rimraf ./node_modules", "dev": "NODE_ENV=development dotenv-run -v -- nodemon ./src/index.ts", "sentry:build": "tsx ./scripts/sentry.ts"}, "nodemonConfig": {"execMap": {"ts": "node --loader ts-node/esm"}, "ext": "ts", "watch": ["src"]}, "dependencies": {"@anthropic-ai/sdk": "~0.39.0", "@apollo/server": "^4.11.2", "@emma/emma-mcp": "workspace:*", "@emma/jobs": "workspace:*", "@emma/logger": "workspace:*", "@emma/s3": "workspace:*", "@emma/storage": "workspace:*", "@graphql-tools/schema": "~10.0.10", "@graphql-yoga/plugin-graphql-sse": "~3.13.6", "@graphql-yoga/redis-event-target": "~3.0.3", "@modelcontextprotocol/sdk": "^1.11.3", "@repo/build-version": "workspace:*", "@repo/date": "workspace:*", "@repo/errors": "workspace:*", "@repo/result": "workspace:*", "@repo/sender": "workspace:*", "@repo/validator": "workspace:*", "@sentry/cli": "~2.43.0", "@sentry/node": "~7.86.0", "@sentry/profiling-node": "~1.2.0", "@types/graphql-upload": "~17.0.0", "@types/helmet": "^4.0.0", "expo-server-sdk": "~3.13.0", "express": "^4.21.2", "graphql": "^16.10.0", "graphql-scalars": "^1.24.0", "graphql-tag": "^2.12.6", "graphql-upload-minimal": "~1.6.1", "graphql-ws": "~5.16.0", "graphql-yoga": "~5.10.4", "helmet": "8.1.0", "hi-base32": "~0.5.1", "ioredis": "~5.6.1", "jsonwebtoken": "^9.0.2", "ms": "2.1.3", "openai": "~4.68.4", "otpauth": "~9.3.6", "qrcode": "~1.5.4", "type-graphql": "2.0.0-rc.2", "ws": "8.18.0"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/ts-config": "workspace:*", "@repo/unit-test": "workspace:*", "@swc/core": "^1.10.1", "@types/cors": "^2.8.17", "@types/jsonwebtoken": "^9.0.7", "@types/ms": "0.7.34", "@types/qrcode": "~1.5.5", "nodemon": "^3.1.9", "reflect-metadata": "^0.2.2"}, "installConfig": {"hoistingLimits": "workspaces"}}