import { isErr, type Result, resultErr, resultOk } from '@repo/result'

import { AiMessageSubscriptionDocument } from '@emma-admin/dev/infra/subscribe/ai-message-subscription.gql-gen'
import { getClient, processGQLRequest } from '@emma-admin/requester'

export function aiMessageSubscriptionAdapter(
  callback: (_: Result<{ token?: string; cmd?: string }>) => void,
) {
  const instance = getClient().subscription(AiMessageSubscriptionDocument, {})
  const subscription = instance.subscribe((opResult) => {
    console.log('-subscription result', opResult)
    const res = processGQLRequest(opResult)

    if (isErr(res)) {
      callback(resultErr(res.error))
      return
    }

    const data = res.data.generateCustomerMessageSubscription
    if (data.__typename === 'CustomerMessageSubscriptionResponsesDto') {
      if ('data' in data) {
        callback(
          resultOk({
            token: data.data?.token,
            cmd: data.data?.cmd,
          }),
        )
        return
      }
      if ('error' in data) {
        callback(resultErr(data.error))
      }
    }
  })

  return subscription
}
