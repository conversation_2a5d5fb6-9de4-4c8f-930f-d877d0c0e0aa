import { validateSchedule } from '../../../schedule/interface/validate-schedule'
import { type TimingValueObject } from '../../core/timing/timing-value.object'

export function validateTiming(timing: TimingValueObject[]): boolean {
  // TODO: add check for other types
  if (!Array.isArray(timing)) return false
  const failed = timing.some((timing: TimingValueObject) => {
    if (timing.type === 'schedule' && timing.prop.isEnabled) {
      return !validateSchedule(timing.prop)
    }

    // TODO: add check for other types
    return false
  })

  if (failed) return false
  return true
}
