import { Alert, Swap } from '@emma/ui-kit'

import { type AlertContainerNodeValueObject } from '../core/alert-container-node.value-object'

type Props = {
  alert?: AlertContainerNodeValueObject
}

export function AlertContainer({ alert }: Props) {
  return (
    <>
      <Swap has={!!alert}>
        <div className="mt-6" />
        {!!alert && <Alert type={alert.type}>{alert.title}</Alert>}
      </Swap>
      <div className="mb-6" />
    </>
  )
}
