import { getBlock } from '@repo/i18n'
import { isErr } from '@repo/result'
import { observer } from '@repo/service'
import { useCallback } from 'react'

import { <PERSON><PERSON>, Modal } from '@emma/ui-kit'
import { ListHeader } from '@emma-admin/header/ui/list.header'
import { handleCreateUser } from '@emma-admin/manage-users/interface/handle-create-user'
import { useInject } from '@emma-admin/service/interface/use-inject'
import { useModal } from '@emma-admin/service/interface/use-modal'

import { ManageUsersStore } from '../../interface/manage-users.store'
import { UserModal } from '../user-modal/user-modal'

import { SearchUserName } from './search-user-name'

import type { CreateUserMetaValueObject } from '../../core/create-user-meta.value-object'

export const ManageHeader = observer(() => {
  const t = getBlock('manageUsers')

  const toggleModal = useModal('user-add')

  const { manageStore } = useInject({
    manageStore: ManageUsersStore,
  })

  const handleApply = useCallback(
    async (value: CreateUserMetaValueObject) => {
      const res = await handleCreateUser(value)
      if (isErr(res)) return
      toggleModal.handleClose()
    },
    [toggleModal],
  )

  return (
    <>
      <ListHeader
        inputSlot={<SearchUserName />}
        actionSlot={
          <Button
            iconLeft="Plus"
            heightType="sm"
            widthType="none"
            onClick={toggleModal.handleOpen}
          >
            {t('add')}
          </Button>
        }
      />

      <Modal onClose={toggleModal.handleClose} isOpen={toggleModal.isOpen}>
        <UserModal
          type="create"
          onClose={toggleModal.handleClose}
          isLoading={manageStore.createUserLoad.isLoading}
          onApply={handleApply}
        />
      </Modal>
    </>
  )
})
