type ProgressHandle = (percent: number) => void

export function createUploadFetch({
  onProgress,
}: {
  onProgress: ProgressHandle
}) {
  return typeof window === 'undefined'
    ? global.fetch
    : uploadFetch({
        onProgress,
      })
}

function uploadFetch({ onProgress }: { onProgress: ProgressHandle }) {
  function customFetch(
    input: RequestInfo | URL,
    init?: RequestInit,
  ): Promise<Response> {
    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest()

      const url = typeof input === 'string' ? input : input.toString()
      const opts = init || {}

      xhr.open(opts.method || 'get', url)

      if (opts.headers) {
        for (const [key, value] of Object.entries(opts.headers)) {
          xhr.setRequestHeader(key, value)
        }
      }

      xhr.onload = (e) => {
        if (xhr.status >= 200 && xhr.status < 300) {
          const headers: Headers = new Headers()
          const headerString = xhr.getAllResponseHeaders()
          const headerPairs = headerString.trim().split(/\s*\n\s*/)
          for (const headerPair of headerPairs) {
            const [key, value] = headerPair.split(/\s*:\s*/)
            headers.set(key, value)
          }

          // @ts-expect-error
          const response: Response = {
            ok: true,
            status: xhr.status,
            statusText: xhr.statusText,
            headers,
            url: xhr.responseURL,
            // @ts-expect-error
            text: () => Promise.resolve(e.target?.responseText),
            // @ts-expect-error
            json: () => Promise.resolve(JSON.parse(e.target?.responseText)),
            redirected: false, // add this property
            type: 'basic', // add this property
            clone: () => response, // add this property
            body: null, // add this property
            bodyUsed: false, // add this property
            arrayBuffer: () => Promise.resolve(new ArrayBuffer(0)), // add this property
            blob: () => Promise.resolve(new Blob([])), // add this property
            formData: () => Promise.resolve(new FormData()), // add this property
          }
          resolve(response)
        } else {
          reject(new Error(`Failed to load ${url}`))
        }
      }
      xhr.onerror = reject

      if (xhr.upload) {
        xhr.upload.onprogress = (event) => {
          const value = (event.loaded / event.total) * 100
          // opts?.onProgress?.(value)
          onProgress(value)
        }
      }
      if (opts.body && !(opts.body instanceof ReadableStream)) {
        xhr.send(opts.body)
      } else {
        xhr.send()
      }
    })
  }

  return customFetch
}
