import { Inject } from '@repo/service'
import { createClient } from 'graphql-ws'

import { TokenStore } from '@emma-admin/token/interface/token.store'

const api = (import.meta.env.VITE_EMMA_ADMIN_ENDPOINT_API || '') as string
const wsUrl = api
  .replace('https://', 'wss://')
  .replace('http://', 'ws://')
  .replace('/app/graphql', '/subscriptions')

export function createWsClient() {
  const url = wsUrl

  const client = createClient({
    url,
    async connectionParams() {
      const tokenStore = Inject(TokenStore)

      return {
        Authorization: tokenStore.accessToken
          ? `Bearer ${tokenStore.accessToken}`
          : '',
      }
    },
    on: {
      connecting: () => {
        console.log('ws connecting')
      },
      connected: () => {
        console.log('ws connected')
      },
    },
  })

  return client
}
