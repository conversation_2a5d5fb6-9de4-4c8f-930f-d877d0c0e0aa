import { observer } from '@repo/service'
import { useCallback } from 'react'

import { useInject } from '@emma-admin/service/interface/use-inject'

import { ContentItemStore } from '../../../../interface/content-item/content-item.store'

import { EmptyUploadArea } from './empty-upload-area'
import { FilePreview } from './file-preview'

export const ContentUploadArea = observer(() => {
  const { contentItemStore } = useInject({
    contentItemStore: ContentItemStore,
  })

  const handleFileChange = useCallback(
    (files?: FileList) => {
      if (!files?.length) return
      const file = files[0]
      contentItemStore.validator.handlers.file(file)
      if (!contentItemStore.validator.values.title) {
        contentItemStore.validator.handlers.title(file.name)
      }
    },
    [contentItemStore],
  )

  if (!contentItemStore.hasFile) {
    return <EmptyUploadArea onFileChange={handleFileChange} />
  }

  const file = contentItemStore.validator.values.file
  if (file instanceof File) {
    return (
      <FilePreview
        mimeType={file.type}
        previewUrl={URL.createObjectURL(file)}
        fileName={file.name}
        onFileChange={handleFileChange}
      />
    )
  }
  return (
    <FilePreview
      mimeType={file.mimeType}
      previewUrl={file.publicUrl || file.thumbnailUrl}
      fileName={file.fileName}
      onFileChange={handleFileChange}
    />
  )
})
