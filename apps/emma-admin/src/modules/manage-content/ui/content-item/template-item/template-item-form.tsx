import { getBlock } from '@repo/i18n'
import { observer } from '@repo/service'

import { Card } from '@emma/ui-kit'
import { useInject } from '@emma-admin/service/interface/use-inject'

import { ContentItemStore } from '../../../interface/content-item/content-item.store'
import { FormControls } from '../../form-components/form-controls'
import { FormDescription } from '../../form-components/form-description'
import { FormExpiration } from '../../form-components/form-expiration'
import { FormKeywords } from '../../form-components/form-keywords'
import { FormLanguage } from '../../form-components/form-language'
import { FormNotification } from '../../form-components/form-notification'
import { FormScheduling } from '../../form-components/form-scheduling'
import { FormTitle } from '../../form-components/form-title'

import { TemplateInsert } from './template-insert'

export const TemplateItemForm = observer(() => {
  const t = getBlock('manageContent')
  const { contentItemStore } = useInject({
    contentItemStore: ContentItemStore,
  })

  const validator = contentItemStore.validator
  const isPublished = validator.values.status === 'publish'

  return (
    <Card widthType="4xl" gap="8-adaptive">
      <FormTitle
        value={validator.values.title || ''}
        onChange={validator.handlers.title}
        error={validator.errors.title}
        isReadOnly={isPublished}
      />
      <FormDescription
        value={validator.values.text || ''}
        onChange={validator.handlers.text}
        error={validator.errors.text}
        descriptionPlaceholder={t('create.templateDescriptionPlaceholder')}
        isReadOnly={isPublished}
      />
      <TemplateInsert />
      <FormKeywords
        keywords={validator.values.keywords || []}
        searchValue={contentItemStore.searchKeywordsValue}
        onSearchChange={contentItemStore.setSearchKeywordValue}
        onKeywordsChange={validator.handlers.keywords}
        isReadOnly={isPublished}
      />
      <FormLanguage
        value={validator.values.lang}
        error={validator.errors.lang}
        onChange={validator.handlers.lang}
        isReadOnly={isPublished}
      />
      <FormNotification
        value={validator.values.notificationSubtype}
        error={validator.errors.notificationSubtype}
        onChange={validator.handlers.notificationSubtype}
        isReadOnly={isPublished}
      />
      <FormScheduling
        error={validator.errors.schedule}
        value={validator.values.schedule}
        onChange={validator.handlers.schedule}
        isReadOnly={isPublished}
      />
      <FormExpiration
        error={validator.errors.expiration}
        value={validator.values.expiration}
        onChange={validator.handlers.expiration}
      />
      <FormControls />
    </Card>
  )
})
