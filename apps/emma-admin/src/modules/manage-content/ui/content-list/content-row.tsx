import { format } from '@repo/date'
import { getBlock } from '@repo/i18n'

import { Button, DropdownMenu, Icon, TableCell, TableRow } from '@emma/ui-kit'
import { handleHistoryPush } from '@emma-admin/history/interface/handle-history-push'
import { Routes } from '@emma-admin/route/interface/routes'

import { type ContentEntity } from '../../core/content.entity'
import { handleArchiveContent } from '../../interface/manage/handle-archive-content'
import { handleCreateShareLink } from '../../interface/manage/handle-create-share-link'
import { handleDuplicateContent } from '../../interface/manage/handle-duplicate-content'
import { handleRemoveContent } from '../../interface/manage/handle-remove-content'

import { ContentStatus } from './content-status'
import { ContentType } from './content-type'
import { Thumb } from './thumb'

type Props = {
  content: ContentEntity
}

export function ContentRow({ content }: Props) {
  const { createdAt, uuid, contentType, file, status, title } = content
  const t = getBlock()
  const tContent = getBlock('manageContent')

  const editRoute = Routes.content.itemSelected.navigate(uuid)
  const isArchived = status === 'archive'
  const isTemplate = contentType === 'template'

  return (
    <TableRow>
      <TableCell>
        <Thumb thumbnail={file?.thumbnailUrl} />
      </TableCell>
      <TableCell>{title}</TableCell>
      <TableCell>
        <ContentType contentType={contentType} />
      </TableCell>
      <TableCell>{format(createdAt, 'dd MMM, yyyy')}</TableCell>
      <TableCell>
        <ContentStatus status={status} />
      </TableCell>
      <TableCell align="right">
        <div className="text-base">
          <DropdownMenu
            placement="right"
            collapsedSlot={
              <Button
                heightType="sm"
                color="gray"
                fill="outline"
                widthType="sm"
              >
                <Icon name="DotsHorizontal" />
              </Button>
            }
          >
            {!isArchived && (
              <DropdownMenu.Option
                onClick={() => {
                  handleHistoryPush(editRoute)
                }}
              >
                {t('app.edit')}
              </DropdownMenu.Option>
            )}
            {!!isTemplate && (
              <DropdownMenu.Option
                onClick={() => {
                  handleDuplicateContent(uuid)
                }}
              >
                {tContent('duplicate.action')}
              </DropdownMenu.Option>
            )}
            {!!import.meta.env.DEV && (
              <DropdownMenu.Option
                onClick={() => {
                  handleCreateShareLink(uuid)
                }}
              >
                create share link
              </DropdownMenu.Option>
            )}
            {status === 'publish' && (
              <DropdownMenu.Option
                onClick={() => {
                  handleArchiveContent(uuid)
                }}
              >
                {t('app.archive')}
              </DropdownMenu.Option>
            )}
            <DropdownMenu.Option
              onClick={() => {
                handleRemoveContent(uuid)
              }}
            >
              <span className="text-red-600">{t('app.remove')}</span>
            </DropdownMenu.Option>
          </DropdownMenu>
        </div>
      </TableCell>
    </TableRow>
  )
}
