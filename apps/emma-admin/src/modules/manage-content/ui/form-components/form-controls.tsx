import { getBlock } from '@repo/i18n'
import { observer } from '@repo/service'
import { useState } from 'react'

import { But<PERSON>, Swap } from '@emma/ui-kit'
import { useInject } from '@emma-admin/service/interface/use-inject'

import { type ContentStatusValueObject } from '../../core/content-status.value-object'
import { ContentItemStore } from '../../interface/content-item/content-item.store'
import { handleSubmitItem } from '../../interface/submit-actions/handle-submit-item'

export const FormControls = observer(() => {
  const t = getBlock('manageContent.create')
  const [clickedButton, setClickedButton] = useState<ContentStatusValueObject>()
  const { contentItemStore } = useInject({
    contentItemStore: ContentItemStore,
  })

  const handleSubmit = async (status: ContentStatusValueObject) => {
    setClickedButton(status)
    await handleSubmitItem(status)
  }

  const isScheduleEnabled = contentItemStore.validator.values.schedule.isEnabled
  const currentStatus = contentItemStore.validator.values.status
  const isPublished = currentStatus === 'publish'
  const isExpired = currentStatus === 'expired'

  if (isPublished) {
    return (
      <div className="flex gap-4">
        <Button
          color="primary"
          widthType="none"
          heightType="md"
          onClick={() => handleSubmit('publish')}
          isLoading={
            !!contentItemStore.contentState.isLoading &&
            clickedButton === 'publish'
          }
        >
          {t('save')}
        </Button>
      </div>
    )
  }

  return (
    <div className="flex gap-4">
      <Swap
        is={isScheduleEnabled}
        isSlot={
          <Button
            color="primary"
            widthType="none"
            heightType="md"
            onClick={() => handleSubmit('schedule')}
            isLoading={
              !!contentItemStore.contentState.isLoading &&
              clickedButton === 'schedule'
            }
          >
            {t('saveSchedule')}
          </Button>
        }
      >
        <Button
          color="primary"
          widthType="none"
          heightType="md"
          onClick={() => handleSubmit('publish')}
          isLoading={
            !!contentItemStore.contentState.isLoading &&
            clickedButton === 'publish'
          }
        >
          {t('savePublish')}
        </Button>
      </Swap>

      {!isScheduleEnabled && !isExpired && (
        <Button
          fill="outline"
          color="primary"
          widthType="none"
          heightType="md"
          onClick={() => handleSubmit('draft')}
          isLoading={
            !!contentItemStore.contentState.isLoading &&
            clickedButton === 'draft'
          }
        >
          {t('saveDraft')}
        </Button>
      )}
    </div>
  )
})
