import { getBlock } from '@repo/i18n'

import { DropdownSelect, Swap } from '@emma/ui-kit'
import { type NotificationAllSubtypeValueObject } from '@emma-admin/manage-notification/core/meta/notification-all-subtype-value.object'

type Props = {
  value?: NotificationAllSubtypeValueObject
  error?: string
  onChange: (subject: NotificationAllSubtypeValueObject | undefined) => void
  isReadOnly?: boolean
}

const subtypeMap: Record<NotificationAllSubtypeValueObject, boolean> = {
  CustomerBirthday: true,
  GroupBirthday: true,
  NewOrder: true,
  OrderFollowup2w: true,
  OrderFollowup2mon: true,
  OrderFollowup6mon: true,
  CorpOrderAssigned: true,
  SubscriptionReminder: true,
  SubscriptionCustomerSkipped: true,
  SubscriptionCustomerPaused: true,
  SubscriptionCustomerCancels: true,
  SubscriptionCustomerPaymentFailed: true,
  CustomerDiscountExpiration: true,
  TeamNewRecruit: true,
  TeamNewRecruitDownline: true,
  TeamStatusChange: true,
  TeamAnniversaries: true,
  TeamBirthdays: true,
  TBC: true,
}

const keys = Object.keys(subtypeMap) as NotificationAllSubtypeValueObject[]

const subtypes: (NotificationAllSubtypeValueObject | 'None')[] = keys.filter(
  (key: NotificationAllSubtypeValueObject) => subtypeMap[key],
)

subtypes.push('None')

export function FormNotification({
  value,
  error,
  onChange,
  isReadOnly,
}: Props) {
  const t = getBlock('manageContent.create.notification')

  const handleChange = (
    subtype: NotificationAllSubtypeValueObject | 'None',
  ) => {
    if (isReadOnly) return
    onChange(subtype === 'None' ? undefined : subtype)
  }

  return (
    <DropdownSelect
      title={value || t('placeholder')}
      label={t('label')}
      isError={!!error}
      isDisabled={isReadOnly}
    >
      {subtypes.map((subtype) => (
        <DropdownSelect.Option
          key={subtype}
          onClick={() => {
            handleChange(subtype)
          }}
        >
          <div className="flex items-center gap-2 text-sm leading-[125%] text-gray-500 font-normal">
            <div className="flex-shrink-0">
              <Swap
                is={value === subtype}
                isSlot={
                  <div className="w-4 h-4 rounded-full border-2 border-primary-700 flex items-center justify-center" />
                }
              >
                <div className="w-4 h-4 rounded-full border border-gray-300" />
              </Swap>
            </div>
            {subtype}
          </div>
        </DropdownSelect.Option>
      ))}
    </DropdownSelect>
  )
}
