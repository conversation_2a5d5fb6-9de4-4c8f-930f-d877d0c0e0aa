import { getBlock } from '@repo/i18n'

import { Heading, HelperText, Swap, Toggle } from '@emma/ui-kit'
import { DateArea } from '@emma-admin/date/ui/date-area'
import { getScheduleValue } from '@emma-admin/schedule/interface/get-schedule-value'

import { getTimesFromSchedule } from '../../interface/get-times-from-schedule'

import type { ScheduleValueObject } from '../../../schedule/core/schedule.value-object'

type Props = {
  label: string
  error?: string
  toggleTitle: string
  value?: ScheduleValueObject
  onChange: (value: ScheduleValueObject) => void
  isReadOnly?: boolean
}

export function Schedule({
  value,
  onChange,
  label,
  error,
  toggleTitle,
  isReadOnly = false,
}: Props) {
  const t = getBlock('manageContent.create.scheduling')
  const timingValue = getTimesFromSchedule(value)

  const handleToggle = (isEnabled: boolean) => {
    if (isReadOnly) return
    onChange(
      getScheduleValue({
        ...value,
        isEnabled,
      }),
    )
  }

  const isError = !!error

  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center gap-2">
        <Heading type="h6">{label}</Heading>
      </div>

      <Toggle checked={!!timingValue.isEnabled} onChange={handleToggle}>
        {toggleTitle}
        {!!isReadOnly && !!timingValue.isEnabled && (
          <span className="ml-2 text-xs text-gray-500">
            {t('cannotBeChanged')}
          </span>
        )}
      </Toggle>

      <Swap has={!!timingValue.isEnabled}>
        <DateArea
          isError={isError}
          value={timingValue}
          label={t('expiration.dateTime')}
          isPreventClick
          isDisabled={isReadOnly}
          onChange={onChange}
        />
        {!!error && <HelperText color="failure">{error}</HelperText>}
      </Swap>
    </div>
  )
}
