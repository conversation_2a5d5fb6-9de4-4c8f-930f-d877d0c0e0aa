import { getBlock } from '@repo/i18n'

import { Badge, Input, Label } from '@emma/ui-kit'
import { useEnterInput } from '@emma-admin/service/interface/use-enter-input'

type Props = {
  keywords: string[]
  searchValue: string
  onSearchChange: (value: string) => void
  onKeywordsChange: (keywords: string[]) => void
  isReadOnly?: boolean
}

export function FormKeywords({
  keywords,
  searchValue,
  onSearchChange,
  onKeywordsChange,
  isReadOnly,
}: Props) {
  const t = getBlock('manageContent.create.keywords')

  useEnterInput({
    inputValue: searchValue,
    values: keywords,
    onAdd: onKeywordsChange,
    onClear: () => {
      onSearchChange('')
    },
  })

  return (
    <div className="flex flex-col gap-4">
      <Label title={t('label')}>
        <Input
          icon="SearchOutline"
          type="search"
          size="sm"
          iconRightSize="xxs"
          iconLeftSize="xs"
          placeholder={t('placeholder')}
          value={searchValue || ''}
          onChange={(value) => {
            onSearchChange(value)
          }}
          hideHelperTextArea
          readOnly={isReadOnly}
        />
        {keywords.length > 0 && (
          <div className="flex flex-wrap gap mt-2">
            {keywords.map((keyword) => (
              <Badge
                color="info"
                size="xs"
                key={keyword}
                onRemove={() => {
                  if (isReadOnly) return
                  onKeywordsChange(keywords.filter((k) => k !== keyword))
                }}
              >
                {keyword}
              </Badge>
            ))}
          </div>
        )}
      </Label>
    </div>
  )
}
