import { getBlock } from '@repo/i18n'

import { HelperText, Input, Label } from '@emma/ui-kit'

const MAX_TITLE_LEN = 100

type Props = {
  value: string
  onChange: (value: string) => void
  error?: string
  isReadOnly?: boolean
}

export function FormTitle({ value, onChange, error, isReadOnly }: Props) {
  const t = getBlock('manageContent.create')

  return (
    <div>
      <Label title={t('title')}>
        <Input
          value={value}
          onChange={onChange}
          error={!!error}
          maxLength={MAX_TITLE_LEN}
          placeholder={t('titlePlaceholder')}
          hideHelperTextArea
          readOnly={isReadOnly}
        />
        {!!error && <HelperText color="failure">{error}</HelperText>}
      </Label>
    </div>
  )
}
