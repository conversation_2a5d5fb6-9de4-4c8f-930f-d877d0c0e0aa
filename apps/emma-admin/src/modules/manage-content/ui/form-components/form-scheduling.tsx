import { getBlock } from '@repo/i18n'

import { Schedule } from './schedule'

import type { ScheduleValueObject } from '../../../schedule/core/schedule.value-object'

type Props = {
  error?: string
  value?: ScheduleValueObject
  onChange: (value: ScheduleValueObject) => void
  isReadOnly?: boolean
}

export function FormScheduling({
  value,
  error,
  onChange,
  isReadOnly = false,
}: Props) {
  const t = getBlock('manageContent.create.scheduling')

  return (
    <Schedule
      error={error}
      label={t('label')}
      toggleTitle={t('schedulingToggle')}
      value={value}
      onChange={onChange}
      isReadOnly={isReadOnly}
    />
  )
}
