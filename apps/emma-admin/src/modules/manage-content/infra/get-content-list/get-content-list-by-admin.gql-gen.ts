import type * as SchemaTypes from '../../../../../../graphql-code/src/types';

import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
export type GetContentListByAdminQueryVariables = SchemaTypes.Exact<{
  sortFields?: SchemaTypes.InputMaybe<SchemaTypes.ContentSortFieldsInput>;
  pagination?: SchemaTypes.InputMaybe<SchemaTypes.PaginationCursorArgs>;
  contentType?: SchemaTypes.InputMaybe<Array<SchemaTypes.ContentTypeEnum>>;
  searchValue?: SchemaTypes.InputMaybe<SchemaTypes.Scalars['String']['input']>;
  lang?: SchemaTypes.InputMaybe<Array<SchemaTypes.ContentLangEnum>>;
}>;


export type GetContentListByAdminQuery = { getContentListByAdmin: (
    { __typename: 'ContentList' }
    & { contentList: Array<(
      Pick<SchemaTypes.Content, 'uuid' | 'domain' | 'contentType' | 'title' | 'text' | 'links' | 'keywords' | 'lang' | 'status' | 'notificationSubtype' | 'usagesCount' | 'isDefault' | 'createdAt' | 'updatedAt' | 'viewCount'>
      & { schedule?: SchemaTypes.Maybe<(
        Pick<SchemaTypes.Schedule, 'isEnabled' | 'tz'>
        & { time?: SchemaTypes.Maybe<Pick<SchemaTypes.Time, 'h' | 'm'>>, day?: SchemaTypes.Maybe<Pick<SchemaTypes.YearMonthDay, 'm' | 'd' | 'y'>> }
      )>, file?: SchemaTypes.Maybe<Pick<SchemaTypes.ContentFile, 'bucketName' | 'fileName' | 'mimeType' | 'publicUrl' | 'thumbnailUrl' | 'fileSize'>>, expiration?: SchemaTypes.Maybe<(
        Pick<SchemaTypes.Schedule, 'isEnabled' | 'tz'>
        & { time?: SchemaTypes.Maybe<Pick<SchemaTypes.Time, 'h' | 'm'>>, day?: SchemaTypes.Maybe<Pick<SchemaTypes.YearMonthDay, 'm' | 'd' | 'y'>> }
      )> }
    )>, pagination: Pick<SchemaTypes.PaginationCursor, 'cursor' | 'take'> }
  ) | (
    { __typename: 'DefaultError' }
    & Pick<SchemaTypes.DefaultError, 'lcid' | 'code' | 'message'>
  ) | (
    { __typename: 'ValidationError' }
    & Pick<SchemaTypes.ValidationError, 'icode'>
    & { fields?: SchemaTypes.Maybe<Array<Pick<SchemaTypes.ValidationItemDto, 'message' | 'name'>>> }
  ) };


export const GetContentListByAdminDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetContentListByAdmin"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"sortFields"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"ContentSortFieldsInput"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"pagination"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"PaginationCursorArgs"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"contentType"}},"type":{"kind":"ListType","type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"ContentTypeEnum"}}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"searchValue"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"lang"}},"type":{"kind":"ListType","type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"ContentLangEnum"}}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"getContentListByAdmin"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"sortFields"},"value":{"kind":"Variable","name":{"kind":"Name","value":"sortFields"}}},{"kind":"Argument","name":{"kind":"Name","value":"pagination"},"value":{"kind":"Variable","name":{"kind":"Name","value":"pagination"}}},{"kind":"Argument","name":{"kind":"Name","value":"contentType"},"value":{"kind":"Variable","name":{"kind":"Name","value":"contentType"}}},{"kind":"Argument","name":{"kind":"Name","value":"searchValue"},"value":{"kind":"Variable","name":{"kind":"Name","value":"searchValue"}}},{"kind":"Argument","name":{"kind":"Name","value":"lang"},"value":{"kind":"Variable","name":{"kind":"Name","value":"lang"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"__typename"}},{"kind":"InlineFragment","typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"ContentList"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"contentList"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"uuid"}},{"kind":"Field","name":{"kind":"Name","value":"domain"}},{"kind":"Field","name":{"kind":"Name","value":"contentType"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"text"}},{"kind":"Field","name":{"kind":"Name","value":"links"}},{"kind":"Field","name":{"kind":"Name","value":"keywords"}},{"kind":"Field","name":{"kind":"Name","value":"lang"}},{"kind":"Field","name":{"kind":"Name","value":"status"}},{"kind":"Field","name":{"kind":"Name","value":"notificationSubtype"}},{"kind":"Field","name":{"kind":"Name","value":"schedule"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"isEnabled"}},{"kind":"Field","name":{"kind":"Name","value":"tz"}},{"kind":"Field","name":{"kind":"Name","value":"time"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"h"}},{"kind":"Field","name":{"kind":"Name","value":"m"}}]}},{"kind":"Field","name":{"kind":"Name","value":"day"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"m"}},{"kind":"Field","name":{"kind":"Name","value":"d"}},{"kind":"Field","name":{"kind":"Name","value":"y"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"usagesCount"}},{"kind":"Field","name":{"kind":"Name","value":"isDefault"}},{"kind":"Field","name":{"kind":"Name","value":"createdAt"}},{"kind":"Field","name":{"kind":"Name","value":"updatedAt"}},{"kind":"Field","name":{"kind":"Name","value":"file"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"bucketName"}},{"kind":"Field","name":{"kind":"Name","value":"fileName"}},{"kind":"Field","name":{"kind":"Name","value":"mimeType"}},{"kind":"Field","name":{"kind":"Name","value":"publicUrl"}},{"kind":"Field","name":{"kind":"Name","value":"thumbnailUrl"}},{"kind":"Field","name":{"kind":"Name","value":"fileSize"}}]}},{"kind":"Field","name":{"kind":"Name","value":"viewCount"}},{"kind":"Field","name":{"kind":"Name","value":"expiration"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"isEnabled"}},{"kind":"Field","name":{"kind":"Name","value":"tz"}},{"kind":"Field","name":{"kind":"Name","value":"time"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"h"}},{"kind":"Field","name":{"kind":"Name","value":"m"}}]}},{"kind":"Field","name":{"kind":"Name","value":"day"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"m"}},{"kind":"Field","name":{"kind":"Name","value":"d"}},{"kind":"Field","name":{"kind":"Name","value":"y"}}]}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"pagination"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"cursor"}},{"kind":"Field","name":{"kind":"Name","value":"take"}}]}}]}},{"kind":"InlineFragment","typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"ValidationError"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"icode"}},{"kind":"Field","name":{"kind":"Name","value":"fields"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"message"}},{"kind":"Field","name":{"kind":"Name","value":"name"}}]}}]}},{"kind":"InlineFragment","typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"DefaultError"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"lcid"}},{"kind":"Field","name":{"kind":"Name","value":"code"}},{"kind":"Field","name":{"kind":"Name","value":"message"}}]}}]}}]}}]} as unknown as DocumentNode<GetContentListByAdminQuery, GetContentListByAdminQueryVariables>;