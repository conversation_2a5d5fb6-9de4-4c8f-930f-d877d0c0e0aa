import { Inject } from '@repo/service'

import { getContentListAdapter } from '../../infra/get-content-list/get-content-list.adapter'
import { FiltersStore } from '../filters/filters.store'

import { ManageContentStore } from './manage-content.store'

const INITIAL_TAKE = 100

export async function handleLoadContentList() {
  const manageContentStore = Inject(ManageContentStore)
  const filtersStore = Inject(FiltersStore)
  const res = await getContentListAdapter({
    searchValue: filtersStore.filters.searchValue,
    contentType: filtersStore.filters.contentType,
    sortFields: filtersStore.filters.sortFields,
    lang: filtersStore.filters.lang,
    pagination: {
      take: INITIAL_TAKE,
    },
  })

  manageContentStore.contentListState.setResult(res)
}
