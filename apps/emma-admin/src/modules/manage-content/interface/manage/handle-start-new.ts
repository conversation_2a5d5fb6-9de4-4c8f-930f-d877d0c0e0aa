import { Inject } from '@repo/service'

import { CompanyStore } from '@emma-admin/company/interface/company.store'
import { handleHistoryPush } from '@emma-admin/history/interface/handle-history-push'
import { type ContentGroupValueObject } from '@emma-admin/manage-content/core/content-group.value-object'
import { Routes } from '@emma-admin/route/interface/routes'

import { ContentItemStore } from '../content-item/content-item.store'

export async function handleStartNew(
  contentGroup: ContentGroupValueObject = 'media',
) {
  const contentItemStore = Inject(ContentItemStore)
  const companyStore = Inject(CompanyStore)

  contentItemStore.reset()
  contentItemStore.setContentGroup(contentGroup)

  if (companyStore.currentCompany?.lang?.length) {
    contentItemStore.validator.handlers.lang(companyStore.currentCompany.lang)
  }

  handleHistoryPush(Routes.content.item.navigate(contentGroup))
}
