import { isErr } from '@repo/result'
import { Inject } from '@repo/service'

import { uploadContentAdapter } from '../../infra/create-content/upload-content.adapter'

import { UploadContentStore } from './upload-content.store'

const MAX_FILES = 10

const MAX_FILE_SIZE = 200 * 1024 * 1024 // 200 mb

async function uploadSingleFile({
  file,
  index,
}: {
  file: File
  index: number
}) {
  const store = Inject(UploadContentStore)
  const key = `${Date.now()}-${index}`
  const node = {
    file,
    key,
  }

  store.appendFile(node)
  const res = await uploadContentAdapter({
    file,
    onProgress: (progress) => {
      store.updateProgress(key, progress)
    },
  })

  if (isErr(res)) {
    // TODO: show error
    store.setError(key)
    console.log('Error uploading file: ', res.error)
    return
  }

  store.setDone(key, res.data.content)
}

export function handleStartUploadFiles(files: File[]) {
  const processedFiles = files
    .slice(0, MAX_FILES)
    .filter((file) => file.size < MAX_FILE_SIZE)
  processedFiles.forEach((file, index) => {
    uploadSingleFile({ file, index })
  })
}
