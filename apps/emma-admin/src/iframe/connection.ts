import { localStoreToken } from '@emma-admin/token/interface/local-store-token'

const DOMAIN = import.meta.env.VITE_EMMA_LANDING_URL || ''

type AuthState = 'authorized' | 'non-auth' | 'ready'

type AdminResponse = {
  type: AuthState | 'check-auth'
}

function sendMessage(msg: AdminResponse): void {
  window.parent.postMessage(dataToMessage(msg), DOMAIN)
}

function parseData(val: string): AdminResponse | undefined {
  try {
    const res = JSON.parse(val)
    if (!res || typeof res !== 'object') return
    if ('type' in res) {
      return {
        type: res.type,
      }
    }
  } catch (e) {
    return undefined
  }
}

function sendAuthState(): void {
  sendMessage({
    type: localStoreToken.value?.ref ? 'authorized' : 'non-auth',
  })
}

function bindAuthState(): void {
  window.removeEventListener('storage', sendAuthState)
  window.addEventListener('storage', sendAuthState)
}

function listener(event: any): void {
  const data = parseData(event.data)
  if (!data) return
  if (data.type === 'check-auth') {
    sendAuthState()
    bindAuthState()
  }
}

window.addEventListener('message', listener)

function dataToMessage(data: AdminResponse): string {
  return JSON.stringify(data)
}

window.addEventListener('load', () => {
  setTimeout(() => {
    sendMessage({
      type: 'ready',
    })
  }, 10)
})
