<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>Emma admin</title>
    <link rel="icon" type="image/svg+xml" href="assets/favicon.svg" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
    />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="msapplication-starturl" content="/" />
    <meta name="apple-mobile-web-app-title" content="Emma admin" />
    <meta name="application-name" content="Emma admin" />

    <style>
      html {
        font-family:
          'Mona Sans',
          sans-serif,
          -apple-system,
          BlinkMacSystemFont,
          'Segoe UI',
          Roboto,
          Oxygen-Sans,
          Ubuntu,
          Cantarell,
          'Helvetica Neue',
          sans-serif;
        color: #111928;
        background-color: #f9fafb;
      }
    </style>
    <style data-type="loader">
      #loader {
        background-color: #f8f8f8;
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        color: #2d2d2d;
        font-size: 10px;
        display: flex;
        justify-content: center;
        align-items: center;
      }
      #root,
      #portal {
        display: none;
      }
    </style>
  </head>
  <body>
    <div id="loader">Loading...</div>
    <div id="root"></div>
    <div id="portal"></div>
    <script type="module" src="/index.ts"></script>
  </body>
</html>
