{"name": "@emma/admin", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "NODE_ENV=development dotenv-run -- vite", "check-types": "tsc", "build": "pnpm check-types && vite build && pnpm copy-content-share", "copy-content-share": "tsx scripts/copy-content-share.ts", "lint": "eslint .", "preview": "vite preview", "clean": "rimraf dist ./.turbo", "clean:node_modules": "rimraf ./node_modules", "test:unit": "repo-unit"}, "dependencies": {"@emma/content-share": "workspace:*", "@emma/graphql-code": "workspace:*", "@emma/logger": "workspace:*", "@emma/ui-kit": "workspace:*", "@mdxeditor/editor": "~3.20.0", "@repo/build-version": "workspace:*", "@repo/date": "workspace:*", "@repo/errors": "workspace:*", "@repo/i18n": "workspace:*", "@repo/result": "workspace:*", "@repo/service": "workspace:*", "@repo/validator": "workspace:*", "@urql/exchange-auth": "~2.2.0", "graphql-sse": "~2.5.4", "graphql-ws": "~5.16.0", "react": "18.3.1", "react-dom": "18.3.1", "react-error-boundary": "~4.1.2", "react-router": "^7.0.2", "urql": "4.2.1"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/tailwind-config": "workspace:*", "@repo/ts-config": "workspace:*", "@repo/unit-test": "workspace:*", "@types/react": "~18.2.0", "@types/react-dom": "~18.2.0", "@vitejs/plugin-react-swc": "~3.7.2", "rollup-plugin-visualizer": "~5.12.0", "vite": "~5.4.14", "vite-plugin-inspect": "~0.8.9"}}