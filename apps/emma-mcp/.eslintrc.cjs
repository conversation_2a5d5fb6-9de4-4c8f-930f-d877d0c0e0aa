/** @type {import("eslint").Linter.Config} */
module.exports = {
  root: true,
  extends: ['../../configs/eslint/typescript.js'],
  parserOptions: {
    sourceType: 'module',
    ecmaVersion: 2020,
    tsconfigRootDir: __dirname,
    project: ['./tsconfig.json']
  },
  env: {
    node: true
  },
  overrides: [
    {
      files: ['**/*.dto.ts'],
      rules: {
        'max-classes-per-file': 'off'
      }
    }
  ]
}
