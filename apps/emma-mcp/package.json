{"name": "@emma/emma-mcp", "version": "0.1.0", "type": "module", "private": true, "sideEffects": false, "description": "Model Context Protocol server for Emma platform", "exports": {"./mcp-tools": {"types": "./src/mcp-tools.ts", "default": "./dist/mcp-tools.js"}, "./types": {"types": "./src/types.ts", "default": "./dist/types.js"}}, "scripts": {"build": "tsup --config tsup.config.ts && pnpm build:external", "dev": "NODE_ENV=development dotenv-run -v -- tsx watch --clear-screen=false ./src/index.ts", "build:external": "tsup --config tsup-external.config.ts", "check-types": "tsc", "start": "node dist/index.js", "test": "vitest run"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@anthropic-ai/sdk": "~0.39.0", "@emma/logger": "workspace:*", "@emma/storage": "workspace:*", "@modelcontextprotocol/sdk": "^1.11.3", "@repo/build-version": "workspace:*", "@repo/date": "workspace:*", "@repo/errors": "workspace:*", "@repo/result": "workspace:*", "@repo/validator": "workspace:*", "@sentry/node": "~7.86.0", "express": "^4.18.2", "helmet": "8.1.0", "jsonwebtoken": "^9.0.2", "zod-to-json-schema": "~3.24.5"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/ts-config": "workspace:*", "@repo/unit-test": "workspace:*", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.5", "@types/helmet": "^4.0.0"}, "installConfig": {"hoistingLimits": "workspaces"}}