# Emma MCP Server

This package implements a Model Context Protocol (MCP) server for <PERSON>'s AI Assistant, providing access to contacts and other data.

## Overview

The MCP server exposes tools that <PERSON> and other large language models can use to access real data from <PERSON>'s system. It follows the [Model Context Protocol](https://modelcontextprotocol.io/) standard, which enables LLMs to interact with external tools and data.

## Currently Implemented Tools

- `getContactById`: Retrieves detailed information about a specific contact
- `searchContacts`: Searches for contacts based on various criteria like name, email, type, order history, etc.

## Running the Server

```bash
# Development mode
pnpm run dev

# Production mode
pnpm run build
pnpm run start
```

## Environment Variables

- `MCP_HTTP_PORT`: The port to run the HTTP server on (default: 3030)
- `ENABLE_STDIO`: Set to 'true' to enable STDIO transport (default: false)
- `NODE_ENV`: When set to 'production', STDIO transport is automatically enabled

## Authentication & Token Handling

### JWT Token Authentication

The Emma MCP server uses JWT tokens for authentication. Tokens are validated in the `authMiddleware` and the authenticated user context is made available to tool handlers.

Key authentication components:

- `authMiddleware`: Validates JWT tokens from query parameter or Authorization header
- `AsyncLocalStorage`: Maintains authentication context throughout the request lifecycle
- Auth context propagation: Ensures tool calls have access to the authenticated user

### JWT Token with Tool Calls

Tool calls require special handling to maintain the authentication context properly:

1. **Initial Connection**: When a client connects via SSE, the token is validated and stored in the auth context
2. **Tool Calls**: Subsequent tool calls need to maintain this authentication context
3. **Custom Middleware**: The `toolCallAuthMiddleware` ensures proper token handling for tool calls
4. **Context Preservation**: Authentication context is preserved when tool handlers are executed

**Important**: When making tool calls, ensure the JWT token is included in the Authorization header of all requests to the MCP server.

### Client Integration

When integrating with the MCP server from a client:

```typescript
// Example of proper client authentication
const transport = new HttpClientTransport({
  url: process.env.MCP_SERVER_URL || 'http://localhost:3030',
  headers: {
    // Always include authorization header with JWT token
    Authorization: `Bearer ${jwtToken}`,
  },
})

const client = new McpClient()
await client.connect(transport)

// The authentication context will be maintained for tool calls
const result = await client.callTool('search_contacts_v2', params)
```

## Integration with Emma AI Assistant

The Emma MCP server should be integrated with the Emma AI Assistant through the GraphQL API.

### Client Service Example

```typescript
export class McpClientService {
  private client: McpClient | null = null
  private jwtToken: string | null = null

  async initialize(jwtToken: string) {
    this.jwtToken = jwtToken

    const transport = new HttpClientTransport({
      url: process.env.MCP_SERVER_URL || 'http://localhost:3030',
      headers: {
        // Always include the JWT token in Authorization header
        Authorization: `Bearer ${jwtToken}`,
      },
    })

    this.client = new McpClient()
    await this.client.connect(transport)
    return this.client
  }

  // Ensure we have a client with valid authentication
  private async ensureClient(token?: string) {
    if (!this.client || (token && token !== this.jwtToken)) {
      await this.initialize(token || this.jwtToken || '')
    }
    return this.client
  }

  async searchContacts(params: any, token?: string) {
    const client = await this.ensureClient(token)
    return client.callTool('search_contacts_v2', params)
  }

  async getContactById(contactId: string, token?: string) {
    const client = await this.ensureClient(token)
    return client.callTool('getContactById', { id: contactId })
  }
}
```

### Mobile App Integration

The Emma mobile app needs to:

1. Detect when a user asks about contacts
2. Send the query to the GraphQL API
3. Display contact information in a user-friendly way

## Contributing

To add new tools to the MCP server:

1. Create a new file in the `src/server/tools` directory
2. Define the parameters schema using Zod
3. Implement the tool handler function
4. Register the tool in `src/server/tools/register-mcp-tool.ts`

## Future Enhancements

- Add tools for orders, products, and team management
- Implement more sophisticated authentication and permission checks
- Add monitoring and telemetry for MCP tool usage
- Expand contact search capabilities with more filters and sorting options
