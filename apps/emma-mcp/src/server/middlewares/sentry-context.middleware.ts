import * as Sentry from '@sentry/node'

import { isToolInvocationRequest } from './auth-middleware/is-tool-invocation-request'

import type { NextFunction, Request, Response } from 'express'

export const sentryContextMiddleware = (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  if (req.auth?.extra?.companyUserUuid) {
    Sentry.setUser({
      companyUserUuid: req.auth.extra.companyUserUuid,
    })
  } else {
    Sentry.setUser(null)
  }

  Sentry.setContext('request', {
    url: req.originalUrl,
    method: req.method,
    requestId: req.requestId,
  })

  if (isToolInvocationRequest(req)) {
    Sentry.setContext('mcp', {
      toolName: req.body?.params?.name,
      sessionId: req.headers['mcp-session-id'],
    })
  }

  next()
}
