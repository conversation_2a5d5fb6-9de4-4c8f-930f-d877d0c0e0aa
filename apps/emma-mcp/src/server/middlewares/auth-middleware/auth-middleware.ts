import { isErr } from '@repo/result'

import { answerErrorRpc } from '~/system/answer-error-rpc'

import { generateRequestId } from './generate-request-id'
import { getAuthToken } from './get-auth-token'
import { isToolInvocationRequest } from './is-tool-invocation-request'
import { isToolListingRequest } from './is-tool-listing-request'
import { parseToken } from './parse-token'

import type { AuthInfo } from '@modelcontextprotocol/sdk/server/auth/types.js'
import type { NextFunction, Request, Response } from 'express'
import type { IncomingTokenDataValueObject } from '../../../system/core/incoming-token-data.value-object'

const secretKey = process.env.AUTH_ACCESS_SECRET_KEY

if (!secretKey) {
  console.error('[MCP-SERVER]: not-defined !! AUTH_ACCESS_SECRET_KEY !!')
}

/**
 * Authentication middleware for MCP server
 * Validates JWT token from Authorization header
 * Extracts companyUserUuid and attaches it to the request
 *
 * - listTools requests are allowed without authentication
 * - callTool requests require valid authentication
 */
export const authMiddleware = (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  if (!req.requestId) {
    req.requestId = generateRequestId()
  }

  try {
    // Allow tool listing requests without authentication
    if (isToolListingRequest(req)) {
      return next()
    }

    // Extract token from Authorization header or from tools/call params
    const token = getAuthToken(req)

    // If this is a tool invocation and no token found, reject with 401
    if (isToolInvocationRequest(req) && !token) {
      return answerErrorRpc(res, 401, {
        id: req.body?.id,
        message: 'Authentication required',
      })
    }

    // If no token found but not a tool invocation, proceed as unauthenticated
    if (!token) {
      return next()
    }

    if (!secretKey) {
      return answerErrorRpc(res, 500, {
        id: req.body?.id,
        message: 'Server authentication configuration error',
      })
    }

    try {
      // Verify the token and extract user information
      const tokenRes = parseToken<IncomingTokenDataValueObject>(
        token,
        secretKey,
      )
      if (isErr(tokenRes)) {
        const message =
          tokenRes.error.status === 'expired'
            ? 'Token expired'
            : 'Invalid token'
        return answerErrorRpc(res, 401, {
          message,
          id: req.body?.id || null,
        })
      }

      const decoded = tokenRes.data

      // Check if token contains company user UUID
      if (!decoded.companyUserUuid) {
        // If this is a tool invocation, reject with 401
        if (isToolInvocationRequest(req)) {
          return answerErrorRpc(res, 401, {
            message: 'Invalid token: missing companyUserUuid',
            id: req.body?.id,
          })
        }
        return next()
      }

      req.auth = {
        token,
        extra: decoded,
      } as unknown as AuthInfo

      return next()
    } catch (error) {
      return answerErrorRpc(res, 500, {
        message: 'Authentication failed',
        id: req.body?.id || null,
      })
    }
  } catch (error) {
    return answerErrorRpc(res, 500, {
      message: 'Internal server error during authentication',
      id: req.body?.id || null,
    })
  }
}
