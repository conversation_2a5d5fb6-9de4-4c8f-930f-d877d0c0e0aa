import type { AuthContextValueObject } from '../../system/core/auth-context.value-object'

function getRoles(value: unknown): AuthContextValueObject['roles'] {
  if (!value) return []
  if (!Array.isArray(value)) return []
  return value
}

function getString(value?: unknown): string | undefined {
  if (!value) return undefined
  return String(value)
}

export function createAuthContext(
  args?: Record<string, unknown>,
): AuthContextValueObject {
  return {
    isAuthenticated: !!args?.companyUserUuid,
    companyUserUuid: getString(args?.companyUserUuid),
    companyUuid: getString(args?.companyUuid),
    userUuid: getString(args?.userUuid),
    roles: getRoles(args?.roles),
  }
}
