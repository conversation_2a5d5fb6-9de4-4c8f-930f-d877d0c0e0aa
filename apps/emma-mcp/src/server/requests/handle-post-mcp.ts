import { StreamableHTTPServerTransport } from '@modelcontextprotocol/sdk/server/streamableHttp.js'

import { answerErrorRpc } from '~/system/answer-error-rpc'
import { createLogger } from '~/system/create-logger'

import { createMcpServer } from './create-mcp-server'

import type { Request, Response } from 'express'

const logger = createLogger('post-request')

export async function handlePostMcp(req: Request, res: Response) {
  // In stateless mode, create a new instance of transport and server for each request
  // to ensure complete isolation. A single instance would cause request ID collisions
  // when multiple clients connect concurrently.
  const server = createMcpServer()

  const transport = new StreamableHTTPServerTransport({
    // for stateless mode
    sessionIdGenerator: undefined,
  })

  try {
    res.on('close', () => {
      logger.log('closed')
      transport.close()
      server.close()
    })

    res.on('error', (err) => {
      logger.log('error', err)
    })

    await server.connect(transport)
    await transport.handleRequest(req, res, req.body)
  } catch (error) {
    logger.error('unexpected', error)

    if (!res.headersSent) {
      answerErrorRpc(res, 500, {
        id: req.body?.id,
        message: 'Internal server error',
      })
    }
  }
}
