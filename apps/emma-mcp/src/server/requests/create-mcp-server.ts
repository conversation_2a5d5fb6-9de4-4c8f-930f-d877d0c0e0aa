import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js'

import { registerMcpTools } from '../tools/register-mcp-tools'

const DEFAULT_MODEL = process.env.MCP_MODEL_NAME || 'claude-3-5-haiku-20241022'

export function createMcpServer(): McpServer {
  const server = new McpServer({
    name: 'Emma MCP Server',
    version: '1.0.0',
    description:
      'Emma Model Context Protocol server for accessing contacts and other data',
    models: [DEFAULT_MODEL],
    contact: { name: 'Emma Support', url: 'https://getemma.io/' },
  })

  registerMcpTools(server)
  return server
}
