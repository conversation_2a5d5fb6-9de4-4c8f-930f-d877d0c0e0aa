import { dailySummaryInputSchema } from './daily-summary/daily-summary-input-schema'
import { searchContactsInputSchema } from './search-contacts/search-contacts-input-schema'

import type { ValidatorSchema } from '@repo/validator'
import type { ToolNamesValueObject } from '../system/core/tool-names.value-object'

export const toolMap = {
  get_daily_summary: {
    name: 'get_daily_summary',
    description: "Get a daily summary of the user's activity",
    schema: dailySummaryInputSchema,
  },
  search_contacts_v2: {
    name: 'search_contacts_v2',
    description:
      'Search for contacts and customers in your database with various filters. Use this tool when you need to find specific individuals based on their personal information, placed order date range, upcoming order date range, or subscription status.',
    schema: searchContactsInputSchema,
  },
} as const satisfies Record<
  ToolNamesValueObject,
  {
    name: ToolNamesValueObject
    description: string
    schema: ValidatorSchema
  }
>
