import { Validator } from '@repo/validator'

import type { ValidatorInfer } from '@repo/validator'

const z = Validator.scheme

// Schema for address data
const contactAddressSchema = z
  .object({
    city: z.string().nullable().optional(),
    state: z.string().nullable().optional(),
    country: z.string().nullable().optional(),
  })
  .nullable()
  .optional()

// Define contact type enum
const contactTypeSchema = z
  .object({
    value: z.enum(['CONTACT', 'CUSTOMER']),
  })
  .or(z.string())

// Schema for single contact output
export const contactOutputSchema = z.object({
  uuid: z.string(),
  firstName: z.string(),
  lastName: z.string(),
  email: z.string().nullable().optional(),
  phone: z
    .object({
      countryCode: z.string(),
      number: z.string(),
    })
    .nullable()
    .optional(),
  type: contactTypeSchema,
  birthday: z.date().nullable().optional(),
  hasOrdered: z.boolean().optional(),
  latestOrderDate: z.date().nullable().optional(),
  upcomingOrders: z.array(z.date()).nullable().optional(),
  subscriptionActive: z.boolean().optional(),
  address: contactAddressSchema,
  // Additional parameters that may be useful but not in the standard schema
  parameters: z.record(z.any()).optional(),
})

// Schema for the list of contacts in the output
export const searchContactsOutputSchema = z.array(contactOutputSchema)

// Type definitions derived from the schema
export type ContactOutputType = ValidatorInfer<typeof contactOutputSchema>
export type SearchContactsOutputType = ValidatorInfer<
  typeof searchContactsOutputSchema
>
