import { Validator } from '@repo/validator'

import type { ValidatorInfer } from '@repo/validator'

const z = Validator.scheme

export const searchContactsInputSchema = z.object({
  query: z
    .string()
    .optional()
    .describe('Search query for name, email, or city'),
  contactType: z.boolean().optional().describe('Filter for contacts'),
  customerType: z.boolean().optional().describe('Filter for customers'),
  orders: z
    .object({
      gte: z.coerce
        .date()
        .optional()
        .describe('Filter for contacts who ordered after a given date'),
      lte: z.coerce
        .date()
        .optional()
        .describe('Filter for contacts who ordered before a given date'),
    })
    .optional()
    .describe('Filter for contacts by order date range'),
  upcomingOrders: z
    .object({
      gte: z.coerce
        .date()
        .optional()
        .describe(
          'Filter for contacts with upcoming orders after a given date',
        ),
      lte: z.coerce
        .date()
        .optional()
        .describe(
          'Filter for contacts with upcoming orders before a given date',
        ),
    })
    .optional()
    .describe('Filter for contacts orders upcoming at a given date range'),
  birthday: z
    .object({
      gte: z.coerce
        .date()
        .optional()
        .describe('Filter for contacts with birthdays after a given date'),
      lte: z.coerce
        .date()
        .optional()
        .describe('Filter for contacts with birthdays before a given date'),
    })
    .optional()
    .describe('Filter for contacts by birthday range'),
  subscriptionActive: z
    .boolean()
    .optional()
    .describe('Filter for contacts with active subscriptions'),
  sort: z
    .enum(['latest', 'alphabetical', 'recentlyOrdered'])
    .optional()
    .describe('Sort order'),
  limit: z.number().optional().describe('Maximum number of results to return'),
})

export type SearchContactsArgs = ValidatorInfer<
  typeof searchContactsInputSchema
>
