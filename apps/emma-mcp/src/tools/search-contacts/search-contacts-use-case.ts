import { getContactType, GetContactUseCase } from '@emma/storage'

import type { SearchContactsArgs } from './search-contacts-input-schema'

export async function searchContactsUseCase(
  args: SearchContactsArgs,
  { companyUserUuid }: { companyUserUuid: string },
) {
  const {
    query,
    contactType,
    customerType,
    orders,
    upcomingOrders,
    birthday,
    subscriptionActive,
    sort,
    limit,
  } = args
  // Make the search call to storage module
  const contactUseCase = new GetContactUseCase()

  // Get contacts using the companyUserUuid from args
  const contactsResult = await contactUseCase.getContactsByCompanyUserUuid({
    companyUserUuid,
    filters: {
      hasActiveSubscriptions: subscriptionActive,
      searchValue: query,
      orderBy: sort,
      type: getContactType({
        isCustomer: customerType,
        isContact: contactType,
      }),
      orders:
        orders?.gte && orders?.lte
          ? {
              gte: orders.gte,
              lte: orders.lte,
            }
          : undefined,
      birthday:
        birthday?.gte && birthday?.lte
          ? {
              gte: birthday.gte,
              lte: birthday.lte,
            }
          : undefined,
      upcomingOrders:
        upcomingOrders?.gte && upcomingOrders?.lte
          ? {
              gte: upcomingOrders.gte,
              lte: upcomingOrders.lte,
            }
          : undefined,
    },
    pagination: {
      take: limit,
    },
  })

  return contactsResult
}
