import { isErr } from '@repo/result'
import { Validator } from '@repo/validator'

import { createLogger } from '~/system/create-logger'
import { toolResultErr } from '~/system/tool/tool-result-err'
import { toolResultOk } from '~/system/tool/tool-result-ok'

import { dailySummaryInputSchema } from './daily-summary-input-schema'
import { dailySummaryUseCase } from './daily-summary-use-case'

import type { CallToolResult } from '@modelcontextprotocol/sdk/types.js'
import type { AuthContextValueObject } from '~/system/core/auth-context.value-object'
import type { DailySummaryArgs } from './daily-summary-input-schema'

const logger = createLogger('daily-summary-tool')

export async function dailySummaryTool(
  args: DailySummaryArgs,
  authContext: AuthContextValueObject,
): Promise<CallToolResult> {
  // Get authenticated user from context
  if (!authContext?.isAuthenticated) {
    const error = 'Missing companyUserUuid: Authentication required'

    logger.error('missing-company-user-uuid', error)

    return toolResultErr(error)
  }

  const toolSchema = Validator.scheme.object(dailySummaryInputSchema.shape)
  const validationResult = toolSchema.safeParse(args)

  if (!validationResult.success) {
    logger.error('invalid-arguments', validationResult.error)

    return toolResultErr(`Invalid arguments: ${validationResult.error.message}`)
  }

  const validatedArgs = validationResult.data

  // Call the use case
  const result = await dailySummaryUseCase(validatedArgs, {
    companyUserUuid: authContext.companyUserUuid || '',
  })

  if (isErr(result)) {
    logger.error('tool-failed', result.error)

    return toolResultErr(String(result.error.message))
  }

  const { notifications } = result.data

  return toolResultOk(notifications)
}
