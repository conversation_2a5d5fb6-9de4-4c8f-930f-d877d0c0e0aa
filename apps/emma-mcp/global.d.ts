// Extend the Express Request to include user information
import type { AuthInfo } from '@modelcontextprotocol/sdk/server/auth/types.js'
import type { IncomingTokenDataValueObject } from '~/system/core/incoming-token-data.value-object'

declare global {
  namespace Express {
    interface Request {
      auth: AuthInfo
      user?: {
        companyUserUuid: string
        userUuid?: string
        companyUuid?: string
        roles?: IncomingTokenDataValueObject['roles']
      }
      requestId?: string
    }
  }
}

// Add this export statement to make the file a module
export {}
