'use client'

import { motion } from 'framer-motion'

import { ContainerLayout } from '~/ui/layouts/container'
import { ContentLayout } from '~/ui/layouts/content'

import { CORPORATE_SOCIAL_PROOF_DATA, SOCIAL_PROOF_DATA } from './data'
import { SocialProofCard } from './social-proof-card'

import type { Paths } from '~/constants'
import type { SocialProofCardData } from './types'

type Props = {
  page: Exclude<
    Paths,
    'for-consultants' | 'for-consultants-bottom' | 'about-us' | 'blog'
  >
}

type SocialProofContent = {
  title: string
  subtitle?: string
  description?: string
  data: SocialProofCardData[]
  paddings: string
  width: string
}

const socialProofContentMap: Record<
  Exclude<
    Paths,
    'for-consultants' | 'for-consultants-bottom' | 'about-us' | 'blog'
  >,
  SocialProofContent
> = {
  main: {
    title: 'Social Proof',
    subtitle: 'See why Consultants love us',
    description:
      "From increasing sales to improving productivity, <PERSON> is revolutionizing the way Consultants operate. Don't just take your word for it—here's what our users have to say:",
    data: SOCIAL_PROOF_DATA,
    paddings: 'pt-[160px] pb-[180px]',
    width: 'max-w-[560px] mb-[176px]',
  },
  'for-corporate': {
    title: 'Hear from Industry Leaders',
    data: CORPORATE_SOCIAL_PROOF_DATA,
    paddings: 'pb-[160px]',
    width: 'max-w-full mb-[76px]',
  },
}

export function SocialProof({ page }: Props): JSX.Element {
  const { title, subtitle, description, data, paddings, width } =
    socialProofContentMap[page]
  return (
    <ContainerLayout className="bg-gray-50">
      <ContentLayout
        className={`px-[60px] ${paddings} lg:pt-[136px] lg:pb-[140px] md:pt-20 md:px-0 mb:pb-[100px]`}
      >
        <div
          className={`${width} mx-auto flex flex-col items-center lg:max-w-[400px] lg:mb-10 md:max-w-full md:px-[60px] mb:px-2.5`}
        >
          <motion.h3
            initial={{ opacity: 0, y: 32 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.2, ease: 'easeOut', delay: 0 }}
            viewport={{ once: true }}
            className="text-[54px] leading-[1.15] font-medium tracking-[-1.5px] mb-6 lg:text-[40px] lg:leading-[1.2] lg:tracking-[-1px] lg:mb-4 md:text-[35px] mb:text-[32px]"
          >
            {title}
          </motion.h3>
          {subtitle && (
            <motion.span
              initial={{ opacity: 0, y: 32 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.2, ease: 'easeOut', delay: 0.1 }}
              viewport={{ once: true }}
              className="text-[20px] tracking-[-1px] font-medium mb-10 lg:text-[18px] lg:leading-[1.2] md:text-[16px] mb:text-[28px] mb:leading-[1.3] mb:tracking-[-0.5px] mb:font-medium"
            >
              {subtitle}
            </motion.span>
          )}
          {description && (
            <motion.span
              initial={{ opacity: 0, y: 32 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.2, ease: 'easeOut', delay: 0.15 }}
              viewport={{ once: true }}
              className="text-[20px] text-center mb-10 lg:text-[18px] md:text-[16px]"
            >
              {description}
            </motion.span>
          )}
        </div>
        <div className="grid grid-cols-2 gap-x-5 gap-y-12 max-w-[1184px] w-full mx-auto lg:max-w-[752px] lg:grid-cols-1 lg:px-16 lg:pt-10 md:max-w-[600px] md:px-[56px] mb:pt-8 mb:px-12">
          {data.map((data, idx) => (
            <SocialProofCard key={idx} {...data} />
          ))}
        </div>
      </ContentLayout>
    </ContainerLayout>
  )
}
