import type { SocialProofCardData } from './types'

export const SOCIAL_PROOF_DATA: Array<SocialProofCardData> = [
  {
    name: '<PERSON>',
    image: '/images/social-proof/jennifer.png',
    text: 'Very quick & easy. <b>The alerts are amazing</b> - I love the ability to use a preloaded script to send a message right away. So easy - no need to go to another app or website.',
  },
  {
    name: '<PERSON>',
    image: '/images/social-proof/lisa.png',
    text: 'Very easy to use and I can get to everything so easily. I love the followup messaging capability. <b>This will save me time!</b>',
  },
  {
    name: '<PERSON>',
    image: '/images/social-proof/stephanie.png',
    text: "I am obsessed with having everything at my fingertips. <b>I love the pre-designed templates</b> and being able to create my own. And it's been so handy getting those notifications that pop up whenever there's activity.",
  },
  {
    name: '<PERSON>',
    image: '/images/social-proof/katie.png',
    text: 'I love the home page.  Notifications, quick links, open events, and sales & earnings all being simple and upfront is great. It seems as though <b>I can do everything I want to do on my phone.</b>',
  },
]

export const CORPORATE_SOCIAL_PROOF_DATA: Array<SocialProofCardData> = [
  {
    name: '— Jessica Turner, Field Development Leader at GlowBeauty Direct Sales',
    image: '/images/emma-for-corporate/social-proof/jessica.png',
    text: "Implementing Emma has transformed our sales operations. <b>Our Consultants are more engaged</b>, and we've seen a significant increase in sales productivity.",
  },
  {
    name: '— Michael Reynolds, VP of Sales at Lifestyle Home Goods',
    image: '/images/emma-for-corporate/social-proof/michael.png',
    text: "Emma's AI-driven insights have given us a competitive edge in a crowded marketplace. It's streamlined our processes and boosted our bottom line.",
  },
]
