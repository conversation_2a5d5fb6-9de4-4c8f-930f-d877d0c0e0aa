import Image from 'next/image'

import { ContainerLayout } from '~/ui/layouts/container'
import { ContentLayout } from '~/ui/layouts/content'

export function OurCommitment(): JSX.Element {
  return (
    <ContainerLayout className="bg-gray-50">
      <ContentLayout className={`py-[100px] px-10 lg:px-8 md:px-6`}>
        <div className="max-w-[860px] w-full mx-auto">
          <h2 className="text-[56px] leading-[1.2] tracking-[-1.5px] font-medium text-center mb-9">
            Our Commitment
          </h2>
          <p className="text-[20px] leading-[1.5] block mb-8 text-center">
            In today's gig economy, direct sales companies face more competition
            than ever. Side gigs, social media influencers, and affiliate
            marketers are all vying for the same audience. To stay competitive,
            your Consultants need more than just motivation—they need the right
            tools.
          </p>
        </div>

        <div className="max-w-[1116px] w-full mx-auto">
          <h3 className="text-[36px] font-semibold text-left">
            We believe in:
          </h3>

          <div className="grid grid-cols-3 w-full mx-auto py-10 gap-x-4 gap-y-12 lg:grid-cols-1 lg:ml-0 lg:max-w-[752px] lg:pt-5 lg:pb-10 md:pb-5 mb:gap-8">
            <div className="flex flex-col items-start w-full lg:max-w-[600px] lg:pr-5">
              <div
                className={`bg-blue-200 w-[60px] h-[60px] rounded-2xl flex items-center justify-center mb-8 lg:mb-6`}
              >
                <Image
                  src="/images/emma-for-consultants/solutions/shield.svg"
                  alt="Empowerment"
                  width={24}
                  height={24}
                  priority
                />
              </div>
              <h4 className="text-[24px] font-bold mt-4">
                Empowerment Through Technology
              </h4>
              <p className="text-[16px] mt-2">
                Providing tools that simplify daily tasks, allowing you to focus
                on building relationships and growing your business.
              </p>
            </div>

            <div className="flex flex-col items-start">
              <div
                className={`bg-green-200 w-[60px] h-[60px] rounded-2xl flex items-center justify-center mb-8 lg:mb-6`}
              >
                <Image
                  src="/images/emma-for-corporate/solutions/time.svg"
                  alt="Continuous Innovation"
                  width={24}
                  height={24}
                  priority
                />
              </div>
              <h4 className="text-[24px] font-bold mt-4">
                Continuous Innovation
              </h4>
              <p className="text-[16px] mt-2">
                Staying ahead of industry trends to offer the most advanced and
                effective solutions.
              </p>
            </div>
            <div className="flex flex-col items-start">
              <div
                className={`bg-teal-200 w-[60px] h-[60px] rounded-2xl flex items-center justify-center mb-8 lg:mb-6`}
              >
                <Image
                  src="/images/emma-for-consultants/solutions/check-circle.svg"
                  alt="Growth"
                  width={24}
                  height={24}
                  priority
                />
              </div>
              <h4 className="text-[24px] font-bold mt-4">
                Customer-Centric Approach
              </h4>
              <p className="text-[16px] mt-2">
                Listening to your feedback and adapting to serve you better.
              </p>
            </div>
          </div>
        </div>
      </ContentLayout>
    </ContainerLayout>
  )
}
