import { motion } from 'framer-motion'
import Image from 'next/image'

import { useModal } from '~/hooks/use-modal'

import { Button } from '../../button/button'

export function CorporateHeroContent(): JSX.Element {
  const { handleOpenModal } = useModal()

  return (
    <div className="w-full h-full flex flex-row-reverse items-center justify-content gap-10 lg:gap-[80px] md:gap-[40px] mb:gap-0 mb:flex-col">
      <div className="max-w-[520px] flex flex-col items-start lg:w-[412px] lg:shrink-0 md:w-[300px] mb:w-full mb:pt-2.5 mb:pr-2">
        <motion.h1
          initial={{ opacity: 0, y: 32 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.2, ease: 'easeOut', delay: 0 }}
          viewport={{ once: true }}
          className="font-semibold text-[64px] leading-[1.1] tracking-[-2px] mb-6 lg:text-[48px] lg:mb-4 md:text-[42px] md:leading-[1.2] mb:text-[36px]"
        >
          Empowering Direct Sales Companies in a Changing Marketplace
        </motion.h1>
        <motion.span
          initial={{ opacity: 0, y: 32 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.2, ease: 'easeOut', delay: 0.05 }}
          viewport={{ once: true }}
          className="text-[20px] mb-10 lg:text-[18px] md:text-[16px] mb:mb-8"
        >
          The AI-Powered Mobile App That Boosts Your Sales by Solving Lead
          Tracking and Follow-Up Challenges
        </motion.span>
        <Button
          className="flex items-center justify-center gap-4 lg:mt-8 mb:mt-0 mb:mb-8"
          onClick={handleOpenModal}
        >
          Book a Demo
        </Button>
      </div>
      <div className="relative w-[660px] h-[683px] shrink-0 mb:h-[362px] mb:mx-auto mb:w-full">
        <motion.div
          initial={{ opacity: 0, y: 32 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, ease: 'easeOut', delay: 0.15 }}
          className="absolute rounded-[14px] top-[100px] right-[430px] md:w-[200px] mb:w-[125px] mb:right-0 mb:left-[70px] mb:top-[50px]"
        >
          <Image
            src="/images/emma-for-corporate/hero/hero-1.png"
            alt="Hero image"
            width={235}
            height={509}
            priority
            className="rotate-[-10deg] shadow-[3px_12px_17px_-1px_rgba(10,11,13,0.05)]"
          />
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 32 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, ease: 'easeOut', delay: 0.2 }}
          className="absolute right-[105px] rounded-[20px] shadow-[5px_2px_14px_5px_rgba(4,4,5,0.08)] mb:w-[145px] mb:right-12"
        >
          <Image
            src="/images/emma-for-corporate/hero/hero-3.png"
            alt="Hero image"
            width={273}
            height={592}
            priority
          />
        </motion.div>

        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 0.2, ease: 'easeOut', delay: 0.1 }}
          className="absolute w-[192px] right-[309px] bottom-[10px] rounded-[20px] shadow-[4px_9px_20px_11px_rgba(85,85,85,0.15)] mb:inset-x-0 mb:mx-auto mb:w-[102px]"
        >
          <Image
            src="/images/emma-for-corporate/hero/hero-2.png"
            alt="Hero image"
            width={192}
            height={161}
            priority
          />
        </motion.div>
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 0.2, ease: 'easeOut', delay: 0.1 }}
          className="absolute rounded-[20px] shadow-[16px_17px_20px_4px_rgba(0,0,0,0.06)] right-0 top-[150px] mb:w-[109px] mb:top-20"
        >
          <Image
            src="/images/emma-for-corporate/hero/hero-4.png"
            alt="Hero image"
            width={205}
            height={90}
            priority
          />
        </motion.div>
      </div>
    </div>
  )
}
