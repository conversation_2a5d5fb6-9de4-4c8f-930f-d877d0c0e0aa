import Image from 'next/image'
import Link from 'next/link'

import { BlogDate } from './blog-date'

import type { BlogPost } from '~/types'

export function BlogCard({ post }: { post: BlogPost }): JSX.Element {
  return (
    <Link
      href={`/blog/${post.slug}`}
      className="group max-w-96 flex flex-col bg-white rounded-[40px] overflow-hidden shadow-md hover:shadow-lg transition-shadow duration-300 lg:max-w-full"
    >
      <div className="aspect-[7/4] relative overflow-hidden">
        <Image
          src={post.frontMatter.coverImage}
          alt={post.frontMatter.title}
          fill
          className="object-cover transition-transform duration-500 group-hover:scale-105"
        />
      </div>
      <div className="p-8 flex-1 flex flex-col">
        <BlogDate date={post.frontMatter.date} />
        <h2 className="text-[32px] leading-[100%] font-bold mb-4">
          {post.frontMatter.title}
        </h2>
        <p className="text-lg mb-4 line-clamp-4">{post.frontMatter.snippet}</p>
        <p className="text-lg font-bold mb-4 text-blue-700">Read more</p>
      </div>
    </Link>
  )
}
