'use client'

import { Spin<PERSON> } from '@emma/ui-kit'
import { getPaginatedBlogPosts } from '~/actions/get-paginated-blog-posts'
import { useInfiniteScroll } from '~/hooks/use-infinite-scroll'
import { ContainerLayout } from '~/ui/layouts/container'
import { ContentLayout } from '~/ui/layouts/content'

import { BlogGrid } from './blog-grid'
import { EmptyState } from './empty-state'

import type { BlogPost } from '~/types'

export function BlogContent({
  initialPosts,
  initialHasMore,
  totalPosts,
}: {
  initialPosts: BlogPost[]
  initialHasMore: boolean
  totalPosts: number
}): JSX.Element {
  const {
    data: posts,
    loading,
    error,
    hasMore,
    loadingRef,
  } = useInfiniteScroll<BlogPost>(
    async (page) => {
      const result = await getPaginatedBlogPosts(page)
      return { data: result.posts, hasMore: result.hasMore }
    },
    {
      initialPage: 1,
      initialHasMore,
      skipInitialLoad: true,
    },
  )

  const allPosts = [
    ...initialPosts,
    ...posts.filter(
      (post) =>
        !initialPosts.some((initialPost) => initialPost.slug === post.slug),
    ),
  ]

  if (error) {
    console.error('Error loading posts:', error)
  }

  return (
    <ContainerLayout className="bg-gray-50">
      <ContentLayout className="py-24 px-16 md:px-12 md:py-16 mb:py-6 mb:px-6">
        {totalPosts === 0 ? (
          <EmptyState />
        ) : (
          <>
            <BlogGrid posts={allPosts} />
            {hasMore && (
              <div
                ref={loadingRef}
                className="flex justify-center items-center my-8"
              >
                {loading && <Spinner size="md" />}
              </div>
            )}
          </>
        )}
      </ContentLayout>
    </ContainerLayout>
  )
}
