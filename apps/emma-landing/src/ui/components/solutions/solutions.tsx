import { ContainerLayout } from '~/ui/layouts/container'
import { ContentLayout } from '~/ui/layouts/content'

import {
  CONSULTANTS_BOTTOM_SOLUTIONS_DATA,
  CONSULTANTS_SOLUTIONS_DATA,
  CORPORATE_SOLUTIONS_DATA,
  SOLUTIONS_DATA,
} from './data'
import { SolutionsItem } from './solutions-item'

import type { Paths } from '~/constants'
import type { SolutionsItemData } from './types'

type Props = {
  page: Exclude<Paths, 'blog'>
}

type SolutionsContent = {
  title: string
  data: SolutionsItemData[]
  paddingBottom: string
}

const solutionsContentMap: Record<Exclude<Paths, 'blog'>, SolutionsContent> = {
  main: {
    title: 'Tailor-Made Solutions for Sustainable Growth',
    data: SOLUTIONS_DATA,
    paddingBottom: 'pb-[100px]',
  },
  'for-corporate': {
    title: "We Know What You're Facing",
    data: CORPORATE_SOLUTIONS_DATA,
    paddingBottom: 'pb-[160px]',
  },
  'for-consultants': {
    title: "Why You'll Love Emma",
    data: CONSULTANTS_SOLUTIONS_DATA,
    paddingBottom: 'pb-[160px]',
  },
  'for-consultants-bottom': {
    title: 'Why Choose Emma?',
    data: CONSULTANTS_BOTTOM_SOLUTIONS_DATA,
    paddingBottom: 'pb-[160px] pt-[160px]',
  },
  'about-us': {
    title: 'About Us',
    data: CONSULTANTS_SOLUTIONS_DATA,
    paddingBottom: 'pb-[160px]',
  },
}

export function Solutions({ page }: Props): JSX.Element {
  const { title, data, paddingBottom } = solutionsContentMap[page]
  return (
    <ContainerLayout className="bg-gray-50">
      <ContentLayout
        className={`pt-20 ${paddingBottom} px-10 lg:py-[56px] lg:px-6 md:py-10 md:px-4`}
      >
        <div className="flex flex-col mx-auto lg:max-w-[800px]">
          <h3 className="max-w-[630px] w-full mx-auto text-[56px] leading-[1.15] tracking-[-1.5px] font-medium mb-[90px] text-center lg:text-[40px] lg:mb-4 md:text-[36px] md:max-w-[400px]">
            {title}
          </h3>
          <div className="grid grid-cols-2 max-w-[1265px] w-full mx-auto py-10 gap-x-4 gap-y-12 lg:grid-cols-1 lg:max-w-[752px] lg:pt-5 lg:px-6 lg:pb-10 md:pb-5 mb:px-4 mb:gap-8">
            {data.map((data, idx) => (
              <SolutionsItem key={idx} {...data} />
            ))}
          </div>
        </div>
      </ContentLayout>
    </ContainerLayout>
  )
}
