import { ContainerLayout } from '~/ui/layouts/container'
import { ContentLayout } from '~/ui/layouts/content'

export function Video() {
  return (
    <ContainerLayout className="bg-gray-50">
      <ContentLayout
        className={`flex items-center justify-center pt-[160px] px-[60px] md:px-4 md:pt-20`}
      >
        <div className="relative flex items-center justify-center rounded-[35px] overflow-hidden max-w-[728px] w-full">
          <iframe
            className="w-full aspect-video"
            src="https://www.youtube.com/embed/8paAD45F2NE?si=KA3LQVGdvT-gwjN-"
            title="YouTube video player"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
            referrerPolicy="strict-origin-when-cross-origin"
            allowFullScreen
          ></iframe>
        </div>
      </ContentLayout>
    </ContainerLayout>
  )
}
