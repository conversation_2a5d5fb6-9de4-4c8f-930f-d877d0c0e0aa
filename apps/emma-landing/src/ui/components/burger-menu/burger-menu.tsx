import { motion } from 'framer-motion'
import Image from 'next/image'

import { getHeaderTextColor } from '~/utils/get-header-text-color'

import type { Paths } from '~/constants'
import type { AuthState } from '../header/iframe-connector'

type Props = {
  authState: AuthState
  path: Paths
  handleClose: () => void
}

const adminUrl = process.env.VITE_EMMA_ADMIN_URL || ''

export function BurgerMenu({
  path,
  authState,
  handleClose,
}: Props): JSX.Element {
  const adminTitle = authState === 'authorized' ? 'Go to Admin' : 'Log In'

  return (
    <motion.div
      initial={{ opacity: 0, y: '-100%' }}
      whileInView={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: '-100%' }}
      viewport={{ once: true }}
      transition={{ duration: 0.2, ease: 'easeOut', delay: 0.1 }}
      className="fixed inset-0 bg-white z-[60]"
    >
      <div className="flex items-center justify-between py-[26px] px-4">
        <a href="/">
          <Image
            src="/images/emma-logo.svg"
            alt="Emma Logo"
            width={123}
            height={51}
            className="md:w-[100px] mb:w-[80px]"
            priority
          />
        </a>
        <button
          className="flex items-center justify-center hover:scale-110"
          onClick={handleClose}
        >
          <Image
            src="/images/burger-close.svg"
            alt="Menu"
            width={15}
            height={15}
          />
        </button>
      </div>
      <nav className="h-full py-[131px] px-[53px]">
        <ul className="flex flex-col space-y-8">
          <li>
            <a
              href="/for-corporate"
              className={getHeaderTextColor(path, 'for-corporate')}
            >
              For Corporate
            </a>
          </li>
          <li>
            <a
              href="/for-consultants"
              className={getHeaderTextColor(path, 'for-consultants')}
            >
              For Consultants
            </a>
          </li>
          <li>
            <a
              href="/about-us"
              className={getHeaderTextColor(path, 'about-us')}
            >
              About Us
            </a>
          </li>
          <li>
            <a href="/blog" className={getHeaderTextColor(path, 'blog')}>
              Blog
            </a>
          </li>
          <hr />
          <li>
            <a href={adminUrl} className="text-black-700">
              {adminTitle}
            </a>
          </li>
        </ul>
      </nav>
    </motion.div>
  )
}
