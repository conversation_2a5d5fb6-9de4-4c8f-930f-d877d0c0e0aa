import Link from 'next/link'

export default function NotFound() {
  return (
    <div className="container mx-auto px-4 py-24 text-center max-w-3xl">
      <h1 className="text-4xl font-bold mb-6">Blog Post Not Found</h1>
      <p className="text-lg text-gray-600 mb-8">
        Sorry, we couldn't find the blog post you're looking for. It may have
        been moved or deleted.
      </p>
      <Link
        href="/blog"
        className="inline-flex items-center px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-5 w-5 mr-2"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M10 19l-7-7m0 0l7-7m-7 7h18"
          />
        </svg>
        Back to Blog
      </Link>
    </div>
  )
}
