import { notFound } from 'next/navigation'

import { getBlogPostBySlug } from '~/actions/get-blog-post-by-slug'
import { getBlogPosts } from '~/actions/get-blog-posts'
import { BlogPostLayout } from '~/ui/components/blog/blog-post/blog-post-layout'
import { ContainerLayout } from '~/ui/layouts/container'
import { ContentLayout } from '~/ui/layouts/content'

import type { Metadata } from 'next'

type Props = {
  params: {
    slug: string
  }
}

// Generate metadata for the page
export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const post = await getBlogPostBySlug(params.slug)

  if (!post) {
    return {
      title: 'Post Not Found',
    }
  }

  return {
    title: `${post.frontMatter.title} | Emma Blog`,
    description: post.frontMatter.snippet,
    openGraph: {
      title: post.frontMatter.title,
      description: post.frontMatter.snippet,
      images: post.frontMatter.coverImage,
    },
  }
}

// Generate static paths
export async function generateStaticParams(): Promise<{ slug: string }[]> {
  const posts = await getBlogPosts()

  return posts.map((post) => ({
    slug: post.slug,
  }))
}

export default async function BlogPostPage({
  params,
}: Props): Promise<JSX.Element> {
  const post = await getBlogPostBySlug(params.slug)
  const posts = await getBlogPosts()

  if (!post) {
    notFound()
  }

  const relatedPosts = posts.filter((p) => p.slug !== post.slug).slice(0, 2)

  return (
    <ContainerLayout className="bg-gray-50">
      <ContentLayout className="px-8 pt-16 pb-32 md:pt-8 md:pb-16">
        <BlogPostLayout post={post} relatedPosts={relatedPosts} />
      </ContentLayout>
    </ContainerLayout>
  )
}
