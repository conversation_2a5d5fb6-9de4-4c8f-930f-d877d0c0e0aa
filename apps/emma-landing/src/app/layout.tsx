import { Analytics } from '@vercel/analytics/react'
import Script from 'next/script'

import { Footer } from '~/ui/components/footer/footer'
import { GoogleAnalytics } from '~/ui/components/google-analytics/google-analytics'
import { Header } from '~/ui/components/header/header'

import '../style/globals.css'

import type { Metadata } from 'next'

export const metadata: Metadata = {
  metadataBase: new URL('https://getemma.io'),
  title: 'Emma - Transform Your Direct Sales Business with Emma',
  description:
    'Emma - simplify operations, empower your Consultants, and boost sales—all through one powerful app.',
  icons: {
    icon: '/images/favicon.svg',
  },
  openGraph: {
    images: '/images/og-image.png',
  },
}

const GA_TRACKING_ID = process.env.GA_TRACKING_ID || ''

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>): JSX.Element {
  return (
    <html lang="en">
      <head>
        {/* Google Analytics */}
        <Script
          strategy="afterInteractive"
          src={`https://www.googletagmanager.com/gtag/js?id=${GA_TRACKING_ID}`}
        />
        <Script
          id="google-analytics"
          strategy="afterInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());
              gtag('config', '${GA_TRACKING_ID}', {
                page_path: window.location.pathname,
              });
            `,
          }}
        />
      </head>
      <body>
        <main className="relative flex min-h-screen flex-col">
          <Header />
          {children}
          <Footer />
          <Analytics />
          <GoogleAnalytics />
        </main>
      </body>
    </html>
  )
}
