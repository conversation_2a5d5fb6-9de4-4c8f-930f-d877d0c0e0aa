'use client'

import { ModalContextProvider } from '~/hooks/use-modal'
import { CallToAction } from '~/ui/components/call-to-action/call-to-action'
import { Features } from '~/ui/components/features/features'
import { <PERSON> } from '~/ui/components/hero/hero'
import { ModalContainer } from '~/ui/components/modal/modal-container'
import { SocialProofLight } from '~/ui/components/social-proof-light/social-proof-light'
import { Solutions } from '~/ui/components/solutions/solutions'
import { StayAheadBlock } from '~/ui/components/stay-ahead/stay-ahead'

export default function ForConsultantsPage(): JSX.Element {
  return (
    <ModalContextProvider>
      <ModalContainer />
      <Hero />
      <StayAheadBlock page="for-consultants" />
      <Solutions page="for-consultants" />
      <Features page="for-consultants" />
      <SocialProofLight />
      <Solutions page="for-consultants-bottom" />
      <CallToAction />
    </ModalContextProvider>
  )
}
