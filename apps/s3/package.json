{"name": "@emma/s3", "version": "1.0.0", "sideEffects": false, "type": "module", "main": "dist/index.js", "module": "dist/index.js", "source": "src/index.ts", "types": "src/index.ts", "scripts": {"build": "dotenv-run -v -- tsup", "check-types": "tsc", "test:unit:basic": "repo-unit", "clean": "rimraf ./dist ./.turbo", "clean:node_modules": "rimraf ./node_modules", "dev": "NODE_ENV=development dotenv-run -v -- tsup --watch", "dev:setup-bucket": "pnpm dev:setup-bucket:up && pnpm dev:setup-bucket:creds && (pnpm dev:setup-bucket:bucket || true) && dev:setup-bucket:policies", "dev:setup-bucket:up": "NODE_ENV=development dotenv-run -v -- docker-compose up -d", "dev:setup-bucket:creds": "NODE_ENV=development dotenv-run -v -- docker compose exec -it minio sh -c \"mc alias set local http://minio:9000 root password\"", "dev:setup-bucket:bucket": "NODE_ENV=development dotenv-run -v -- docker compose exec -it minio sh -c \"mc mb local/content\"", "dev:setup-bucket:policies": "NODE_ENV=development dotenv-run -v -- docker compose exec -it minio sh -c \"mc anonymous set download local/content\""}, "dependencies": {"@aws-sdk/client-s3": "~3.787.0", "@aws-sdk/lib-storage": "^3.787.0", "@aws-sdk/s3-request-presigner": "~3.787.0", "@emma/logger": "workspace:*", "@repo/errors": "workspace:*", "@repo/result": "workspace:*", "fluent-ffmpeg": "^2.1.3", "nanoid": "^5.1.5", "pdf2pic": "~3.1.3", "sharp": "^0.34.1"}, "devDependencies": {"@repo/unit-test": "workspace:*", "@repo/eslint-config": "workspace:*", "@repo/ts-config": "workspace:*", "@types/fluent-ffmpeg": "^2.1.27", "@types/sharp": "^0.32.0"}}