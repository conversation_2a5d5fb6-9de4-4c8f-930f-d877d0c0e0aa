{"name": "@emma/content-share", "private": true, "version": "0.0.0", "type": "module", "exports": {"./index.html": "./dist/index.html", "./get-content-share": {"types": "./src/modules/content-loader/interface/get-content-share.ts", "default": "./dist/get-content-share"}}, "scripts": {"dev:app": "NODE_ENV=development dotenv-run -- vite", "dev": "pnpm build:web && pnpm build:lib && pnpm dev:app", "check-types": "tsc", "build": "pnpm check-types && pnpm build:web && pnpm build:lib", "build:web": "vite build", "build:lib": "vite build --config vite-lib.config.ts", "lint": "eslint .", "preview": "vite preview", "clean": "rimraf dist ./.turbo", "clean:node_modules": "rimraf ./node_modules", "test:unit": "repo-unit"}, "dependencies": {"@repo/build-version": "workspace:*", "@repo/date": "workspace:*", "@repo/result": "workspace:*"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/ts-config": "workspace:*", "@repo/unit-test": "workspace:*", "@emma/graphql-code": "workspace:*", "vite": "~5.4.14", "vite-plugin-dts": "^4.5.0", "vite-plugin-singlefile": "^2.1.0"}}