.spinner-container {
  margin: 0 auto;
  max-width: 320px;
  width: 100%;
  height: 285px;
  background: #F3F4F6;
  border-radius: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.spinner {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 6px solid #F2F4F7;
  border-top: 6px solid #2563EB;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
