import { Spinner } from '../spinner/spinner'

import { handleLoadedData } from './handle-loaded-data'

import './pdf.css'

type Props = {
  src: string
}

export function Pdf({ src }: Props) {
  return (
    <div className="pdf-container">
      <Spinner />
      <iframe
        className="viewer"
        src={`https://docs.google.com/viewer?url=${src}&embedded=true`}
        width="100%"
        height="100%"
        onLoad={handleLoadedData}
      >
        <p>
          Your browser doesn't support iframes. Please{' '}
          <a href={src} target="_blank" rel="noopener">
            download the PDF
          </a>{' '}
          instead.
        </p>
      </iframe>
      {/* <embed width="100%" height="100%" type="application/pdf" src={src} />*/}
    </div>
  )
}
