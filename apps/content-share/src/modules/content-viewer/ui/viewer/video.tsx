import { Spinner } from '../spinner/spinner'

import { handleLoadedData } from './handle-loaded-data'

import './media.css'

type Props = {
  src: string
}

export function Video({ src }: Props) {
  return (
    <div>
      <Spinner />
      <video
        className="viewer"
        src={src}
        controls
        onLoadedData={handleLoadedData}
      >
        <source src={src} />
      </video>
    </div>
  )
}
