/* eslint-disable @typescript-eslint/no-explicit-any */
import { useQuery } from '@tanstack/react-query'
import { OrdersTable } from '../components/orders-table/orders-table'
import type { Order } from '../components/orders-table/types'
import { useState } from 'react'
import { OrderDetailsModal } from './order-details-modal'

export function OrdersPage() {
  const { data: orders = [], isLoading } = useQuery({
    queryKey: ['orders'],
    queryFn: async () => {
      const response = await fetch('/a/api/orders')
      const data = await response.json()
      return data.orders as Order[]
    },
  })

  const [isModalOpen, setIsModalOpen] = useState(false)
  const [selectedOrderId, setSelectedOrderId] = useState<number | null>(null)

  // Fetch order details when an order is selected
  const { data: orderDetails, isLoading: isOrderDetailsLoading } = useQuery({
    queryKey: ['order-details', selectedOrderId],
    queryFn: async () => {
      if (!selectedOrderId) return null
      const response = await fetch(`/a/api/order/${selectedOrderId}`)
      if (!response.ok) throw new Error('Failed to fetch order details')
      return response.json()
    },
    enabled: !!selectedOrderId && isModalOpen,
  })

  function handleRowClick(order: Order) {
    setSelectedOrderId(order.id)
    setIsModalOpen(true)
  }

  // Map BE order details to modal props
  // TODO: Type order details argument properly instead of 'any'
  function getModalPropsFromOrder(order: any) {
    if (!order) {
      // fallback to empty values to satisfy modal props
      return {
        orderNumber: '',
        customerName: '',
        email: '',
        phone: '',
        orderDate: '',
        trackingNumber: '',
        shippingAddress: '',
        billingAddress: '',
        status: '',
        shipmentType: '',
        summary: {
          subtotal: '',
          shipping: '',
          taxes: '',
          total: '',
          ppv: '',
          cv: '',
        },
        items: [],
      }
    }
    const customerName = [order.buyer?.firstName, order.buyer?.lastName]
      .filter(Boolean)
      .join(' ')
    const formatCurrency = (amount: number | string, currency = 'USD') =>
      new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency,
        minimumFractionDigits: 2,
      }).format(Number(amount) / 100)
    return {
      orderNumber: order.orderNumber || order.number || '',
      customerName,
      email: order.buyer?.email || '',
      phone: order.buyer?.phone || '',
      orderDate: order.createdAt
        ? new Date(order.createdAt).toLocaleString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
          })
        : '',
      trackingNumber: order.trackingNumber || '',
      shippingAddress: order.address
        ? `${order.address.street_1}\n${order.address.city} ${order.address.state_code || ''} ${order.address.zip_code}\n${order.address.country_code}`
        : '',
      billingAddress: order.billingAddress
        ? `${order.billingAddress.street_1}\n${order.billingAddress.city} ${order.billingAddress.state_code || ''} ${order.billingAddress.zip_code}\n${order.billingAddress.country_code}`
        : '',
      status: order.financialStatus || 'Shipped',
      shipmentType: 'Shipment',
      summary: {
        subtotal: formatCurrency(order.totalPrice || 0, order.currency),
        shipping: formatCurrency(order.totalShipping || 0, order.currency),
        taxes: formatCurrency(order.totalTax || 0, order.currency),
        total: formatCurrency(order.totalPrice || 0, order.currency),
        ppv: order.ppv ? String(order.ppv) : '0.00',
        cv: order.cv ? String(order.cv) : '0.00',
      },
      items: Array.isArray(order.items)
        ? order.items.map((item: any) => ({
            image: item.skuData?.image_url || '',
            name: item.skuData?.name || '',
            sku: item.skuData?.code || '',
            qty: item.quantity || 1,
            price: formatCurrency(
              item.itemData?.price_cents || 0,
              order.currency,
            ),
          }))
        : [],
    }
  }

  return (
    <div className="min-h-screen flex flex-col">
      {/* Header Row */}
      <div className="flex items-center justify-between px-8 py-6 border-b border-gray-200">
        <h1 className="text-3xl font-bold text-gray-800">Orders</h1>
      </div>
      {/* Content: Table at the top, full width */}
      <div className="flex-1 w-full px-8 py-8">
        {isLoading ? (
          <div className="text-center text-gray-500">Loading...</div>
        ) : orders.length === 0 ? (
          <div className="text-center text-gray-500">No orders found.</div>
        ) : (
          <OrdersTable orders={orders} onRowClick={handleRowClick} />
        )}
      </div>
      {isModalOpen && (
        <OrderDetailsModal
          isOpen={isModalOpen}
          onClose={() => {
            setIsModalOpen(false)
            setSelectedOrderId(null)
          }}
          loading={isOrderDetailsLoading}
          {...getModalPropsFromOrder(orderDetails)}
        />
      )}
    </div>
  )
}
