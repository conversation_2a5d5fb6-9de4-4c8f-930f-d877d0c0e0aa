export type OrderAddress = {
  public_uuid: string
  city: string
  country: string
  country_code: string
  zip_code: string
  state_code: string | null
  mail_stop: string | null
  street_1: string
  street_2: string | null
  created_at: string
  updated_at: string
  archived_at: string | null
}

export type OrderBuyer = {
  firstName: string
  lastName: string
  email: string
}

export type OrderSkuData = {
  public_uuid: string
  name: string
  code: string
  image_url: string
  current_price_cents: number
  shopify_product_id: string
  shopify_variant_id: string
  auto_created: boolean
  created_at: string
  updated_at: string
  archived_at: string | null
}

export type OrderItemData = {
  public_uuid: string
  price_cents: number
  created_at: string
  archived_at: string | null
}

export type OrderItem = {
  itemData: OrderItemData
  skuData: OrderSkuData
}

export type Order = {
  orderNumber: string
  id: string
  type: string
  createdAt: string
  updatedAt: string
  name: string
  totalPrice: number
  totalShipping: number
  currency: string
  financialStatus: string
  buyer: OrderBuyer
  items: OrderItem[]
  address: OrderAddress
  billingAddress: OrderAddress
}
