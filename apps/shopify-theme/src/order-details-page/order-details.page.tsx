import { getQueryParams } from '../components/get-query-params'
import { useQuery } from '@tanstack/react-query'
import type { Order } from './types'

export function OrderDetailsPage() {
  const { id } = getQueryParams<{ id: string }>()

  const { data, isLoading, error } = useQuery<Order>({
    queryKey: ['order', id],
    queryFn: async () => {
      const res = await fetch(`/a/api/order/${id}`)
      if (!res.ok) throw new Error('Failed to fetch order')
      return res.json()
    },
    enabled: !!id,
  })

  if (isLoading) return <div>Loading...</div>
  if (error) return <div>Error loading order.</div>
  if (!data) return <div>No Order found.</div>

  // Helper functions for formatting
  function formatCurrency(amount: number | string, currency = 'USD') {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency,
      minimumFractionDigits: 2,
    }).format(Number(amount) / 100)
  }

  const orderDate = new Date(data.createdAt).toLocaleString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  })

  const customer = [data.buyer.firstName, data.buyer.lastName]
    .filter(Boolean)
    .join(' ')

  return (
    <div className="w-full min-h-screen bg-slate-50 font-sans px-0 py-8 flex flex-col items-center">
      <div className="w-full bg-white rounded-lg shadow p-12">
        <div className="flex justify-between items-center w-full">
          <div>
            <h1>Order #{data.orderNumber}</h1>
            <a
              href="/pages/orders"
              className="text-sm text-slate-500 underline"
            >
              Return to Orders List
            </a>
          </div>
        </div>
        <div>Placed on {orderDate}</div>
        <div className="!grid !grid-cols-5 gap-4 w-full">
          <div className="col-span-4">
            <div className="!grid !grid-cols-5 gap-4 border border-slate-200 p-4">
              <div className="text-gray-700 text-start">Product</div>
              <div className="text-gray-700 text-center">SKU</div>
              <div className="text-gray-700 text-end">Price</div>
              <div className="text-gray-700 text-end">Quantity</div>
              <div className="text-gray-700 text-end">Total</div>
            </div>
            {data.items.map((item) => {
              return (
                <div className="!grid !grid-cols-5 gap-4 items-center border border-t-0 border-slate-200 p-4">
                  <div className="flex items-center justify-start">
                    <img
                      width={64}
                      height={64}
                      src={item.skuData.image_url}
                      alt={item.skuData.name}
                      className="w-16 h-16 object-cover rounded"
                    />
                  </div>
                  <div className="py-2 text-center">{item.skuData.name}</div>
                  <div className="py-2 text-end">
                    {formatCurrency(
                      item.skuData.current_price_cents,
                      data.currency,
                    )}
                  </div>
                  <div className="py-2 text-end">{1}</div>
                  <div className="py-2 text-end">
                    {formatCurrency(item.itemData.price_cents, data.currency)}
                  </div>
                </div>
              )
            })}
            <div className="flex flex-col border border-t-0 border-slate-200 p-4">
              <div className="flex justify-between">
                <div className="text-gray-700 text-start">Subtotal</div>
                <div className="text-gray-700 text-end">
                  {formatCurrency(data.totalPrice, data.currency)}
                </div>
              </div>
              <div className="flex justify-between">
                <div className="text-gray-700 text-start">Shipping</div>
                <div className="text-gray-700 text-end">
                  {formatCurrency(data.totalShipping, data.currency)}
                </div>
              </div>
              <div className="text-2xl font-semibold flex justify-between">
                <div className="text-gray-700 text-start">Total</div>
                <div className="text-gray-700 text-end">
                  {formatCurrency(data.totalPrice, data.currency)}
                </div>
              </div>
            </div>
          </div>
          <div className="col-span-1">
            <div className="flex flex-col gap-2">
              <div className="text-2xl font-bold">Billing Address</div>
              <div>Payment Status: Paid</div>
              <div>{customer}</div>
              <div>{data.billingAddress.street_1}</div>
              <div>
                {data.billingAddress.city} {data.billingAddress.state_code}{' '}
                {data.billingAddress.zip_code}
              </div>
              <div>{data.billingAddress.country_code}</div>
            </div>
            <div className="flex flex-col gap-2">
              <div className="text-2xl font-bold">Shipping Address</div>
              <div>Fullfillment Status: Fulfilled</div>
              <div>{customer}</div>
              <div>{data.address.street_1}</div>
              <div>
                {data.address.city} {data.address.state_code}{' '}
                {data.address.zip_code}
              </div>
              <div>{data.address.country_code}</div>
            </div>
            <div></div>
          </div>
        </div>
      </div>
    </div>
  )
}
