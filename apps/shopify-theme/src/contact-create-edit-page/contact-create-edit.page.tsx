import { getQueryParams } from '../components/get-query-params'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useState, useMemo } from 'react'
import { ShopifyRouter } from '../components/shopify-router'

type ContactForm = {
  firstName: string
  lastName: string
  birthMonth: string
  birthDay: string
  birthYear: string
  email: string
  phone: string
  address: {
    id: number
    phone: string
    address1: string
    address2: string
    city: string
    province: string
    zipCode: string
    country: string
  }
}

export function ContactCreateEditPage() {
  const { id } = getQueryParams<{ id?: string }>()
  const queryClient = useQueryClient()

  // Fetch contact if editing
  const { data, isLoading } = useQuery({
    queryKey: ['contact', id],
    queryFn: async () => {
      const res = await fetch(`/a/api/contact/${id}`)
      if (!res.ok) throw new Error('Failed to fetch contact')
      return res.json()
    },
    enabled: !!id,
  })

  const isEmailDisabled = !!data?.email

  // Parse birthday into month/day for form
  const birthday = useMemo(() => {
    if (!data?.birthday) return { year: '', month: '', day: '' }
    const [year, m, d] = data.birthday.split('-')
    return {
      year: year || '',
      month: m || '',
      day: d || '',
    }
  }, [data])

  // Form state
  const [form, setForm] = useState<ContactForm>({
    firstName: data?.firstName || '',
    lastName: data?.lastName || '',
    birthYear: data?.birthYaer || '',
    birthMonth: birthday.month || '',
    birthDay: birthday.day || '',
    email: data?.email || '',
    phone: data?.phone || '',
    address: {
      id: data?.addresses?.[0]?.id || '',
      address1: data?.addresses?.[0]?.address1 || '',
      address2: data?.addresses?.[0]?.address2 || '',
      city: data?.addresses?.[0]?.city || '',
      province: data?.addresses?.[0]?.province || '',
      zipCode: data?.addresses?.[0]?.zipCode || '',
      country: data?.addresses?.[0]?.country || '',
      phone: data?.addresses?.[0]?.phone || '',
    },
  })

  // Update form state when data loads
  useMemo(() => {
    if (data) {
      setForm({
        firstName: data?.firstName || '',
        lastName: data?.lastName || '',
        birthMonth: birthday.month || '',
        birthDay: birthday.day || '',
        birthYear: birthday.year || '',
        email: data?.email || '',
        phone: data?.phone || '',
        address: {
          id: data?.addresses?.[0]?.id || '',
          address1: data?.addresses?.[0]?.address1 || '',
          address2: data?.addresses?.[0]?.address2 || '',
          city: data?.addresses?.[0]?.city || '',
          province: data?.addresses?.[0]?.province || '',
          zipCode: data?.addresses?.[0]?.zipCode || '',
          country: data?.addresses?.[0]?.country || '',
          phone: data?.addresses?.[0]?.phone || '',
        },
      })
    }
    // eslint-disable-next-line
  }, [data])

  // Mutations for save/delete
  const saveMutation = useMutation({
    mutationFn: async (payload: ContactForm & { birthday: string }) => {
      const method = id ? 'PUT' : 'POST'
      const url = id ? `/a/api/contact/${id}` : `/a/api/contacts`
      const res = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload),
      })
      if (!res.ok) throw new Error('Failed to save contact')
      return res.json()
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['contact'] })
      // Redirect to contact details page
      window.location.href = ShopifyRouter.contact.details.path(data.id)
    },
  })

  const deleteMutation = useMutation({
    mutationFn: async () => {
      const res = await fetch(`/a/api/contact/${id}`, { method: 'DELETE' })
      if (!res.ok) throw new Error('Failed to delete contact')
      return res.json()
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['contact'] })
      // Optionally redirect
    },
  })

  // Handle form changes
  function handleChange(
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>,
  ) {
    const { name, value } = e.target
    setForm((prev) => ({
      ...prev,
      [name]: value,
    }))
  }
  // Handle form changes
  function handleChangeAddress(
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>,
  ) {
    const { name, value } = e.target
    setForm((prev) => ({
      ...prev,
      address: {
        ...prev.address,
        [name]: value,
      },
    }))
  }

  function handleSubmit(e: React.FormEvent) {
    e.preventDefault()
    // Compose birthday string
    let birthday = ''
    if (form.birthMonth && form.birthDay) {
      birthday = `${form.birthYear}-${form.birthMonth.padStart(2, '0')}-${form.birthDay.padStart(2, '0')}`
    }

    saveMutation.mutate({
      ...form,
      birthday,
    })
  }

  const currentYear = new Date().getFullYear()

  if (isLoading) return <div>Loading...</div>

  return (
    <form
      className="max-w-4xl mx-auto p-8 bg-white rounded"
      onSubmit={handleSubmit}
    >
      <h2 className="text-2xl font-bold mb-6">Edit</h2>
      <div className="grid grid-cols-2 gap-6 mb-4">
        <div>
          <label className="block text-sm font-medium mb-1">First Name</label>
          <input
            name="firstName"
            value={form.firstName}
            onChange={handleChange}
            className="w-full border rounded p-2 bg-slate-50"
          />
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">Last Name</label>
          <input
            name="lastName"
            value={form.lastName}
            onChange={handleChange}
            className="w-full border rounded p-2 bg-slate-50"
          />
        </div>
      </div>
      <div className="grid grid-cols-3 gap-6 mb-4">
        <div>
          <label className="block text-sm font-medium mb-1">Birth Year</label>
          <input
            type="number"
            name="birthYear"
            value={form.birthYear}
            onChange={handleChange}
            min={1900}
            max={currentYear}
            className="w-full border rounded p-2 bg-slate-50"
            placeholder="Year"
          />
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">Birth Month</label>
          <input
            type="number"
            name="birthMonth"
            value={form.birthMonth}
            onChange={handleChange}
            min={1}
            max={12}
            className="w-full border rounded p-2 bg-slate-50"
            placeholder="Month"
          />
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">Birth Day</label>
          <input
            type="number"
            name="birthDay"
            value={form.birthDay}
            onChange={handleChange}
            min={1}
            max={31}
            className="w-full border rounded p-2 bg-slate-50"
            placeholder="Day"
          />
        </div>
      </div>
      <div className="mb-4">
        <label className="block text-sm font-medium mb-1">Email Address</label>
        <input
          name="email"
          value={form.email}
          onChange={handleChange}
          disabled={!!isEmailDisabled}
          className="w-full border rounded p-2 bg-slate-50"
        />
        {isEmailDisabled && (
          <div className="text-xs text-slate-400 mt-1">
            You cannot edit the customer's email address because it is used as
            their account login. Contact the customer or Partner Care Center to
            edit.
          </div>
        )}
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-4">
        <div>
          <label className="block text-sm font-medium mb-1">Phone Number</label>
          <input
            name="phone"
            value={form.phone}
            onChange={handleChange}
            className="w-full border rounded p-2 bg-slate-50"
          />
        </div>
      </div>
      <div className="flex gap-6 mb-4"></div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-4">
        <div>
          <label className="block text-sm font-medium mb-1">
            Address Line 1
          </label>
          <input
            name="address1"
            value={form.address.address1}
            onChange={handleChangeAddress}
            className="w-full border rounded p-2 bg-slate-50"
          />
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">
            Address Line 2 (optional)
          </label>
          <input
            name="address2"
            value={form.address.address2}
            onChange={handleChangeAddress}
            className="w-full border rounded p-2 bg-slate-50"
          />
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">City</label>
          <input
            name="city"
            value={form.address.city}
            onChange={handleChangeAddress}
            className="w-full border rounded p-2 bg-slate-50"
          />
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">Select State</label>
          <input
            name="province"
            value={form.address.province}
            onChange={handleChangeAddress}
            className="w-full border rounded p-2 bg-slate-50"
            placeholder="State"
          />
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">Zip Code</label>
          <input
            name="zipCode"
            value={form.address.zipCode}
            onChange={handleChangeAddress}
            className="w-full border rounded p-2 bg-slate-50"
          />
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">Country</label>
          <input
            name="country"
            value={form.address.country}
            onChange={handleChangeAddress}
            className="w-full border rounded p-2 bg-slate-50"
          />
        </div>
      </div>
      <div className="flex justify-between items-center mt-8">
        <div>
          {id && (
            <>
              <button
                type="button"
                className="text-blue-600 underline mr-4"
                onClick={() => deleteMutation.mutate()}
              >
                Delete Contact
              </button>
              <button
                type="button"
                className="text-blue-600 underline"
                onClick={() => window.history.back()}
              >
                Cancel
              </button>
            </>
          )}
        </div>
        <button
          type="submit"
          className="bg-slate-800 text-white rounded px-8 py-2 text-lg hover:bg-slate-700"
        >
          Save
        </button>
      </div>
    </form>
  )
}
