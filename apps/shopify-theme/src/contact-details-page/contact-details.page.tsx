import { getQueryParams } from '../components/get-query-params'
import { useQuery } from '@tanstack/react-query'
import { useMemo } from 'react'
import { ShopifyRouter } from '../components/shopify-router'
import { Icon } from '../components/icons'
import { OrdersTable } from '../components/orders-table/orders-table'

export function ContactDetailsPage() {
  const { id } = getQueryParams<{ id: string }>()

  const { data, isLoading, error } = useQuery({
    queryKey: ['contact', id],
    queryFn: async () => {
      const res = await fetch(`/a/api/contact/${id}`)
      if (!res.ok) throw new Error('Failed to fetch contact')
      return res.json()
    },
    enabled: !!id,
  })

  const addressString = useMemo(() => {
    if (!data?.addresses?.length) return '--'
    const addr = data.addresses[0]
    return [addr.address1, addr.city, addr.province, addr.zipCode, addr.country]
      .filter(Boolean)
      .join(', ')
  }, [data])

  if (isLoading) return <div>Loading...</div>
  if (error) return <div>Error loading contact.</div>
  if (!data) return <div>No contact found.</div>

  return (
    <div className="w-full min-h-screen bg-slate-50 font-sans px-0 py-8">
      <div className="w-full px-8">
        <button
          onClick={() => window.history.back()}
          className="text-blue-600 hover:text-blue-700 text-base mb-4 flex flex-row items-center gap-2 bg-transparent border-none p-0"
        >
          <Icon name="ArrowLeft" />
          <span>Go Back</span>
        </button>
        <h1 className="mt-2 mb-0 font-black text-5xl leading-tight">
          {data.firstName} {data.lastName}
        </h1>
        <div className="flex items-center gap-2 my-4 text-lg">
          <a
            href={`mailto:${data.email}`}
            className="text-blue-600 no-underline break-all"
          >
            {data.email}
          </a>
          <span>|</span>
          <a href={`tel:${data.phone}`} className="text-blue-600 no-underline">
            {data.phone}
          </a>
        </div>
        <div className="flex gap-3 my-6">
          <a href={ShopifyRouter.contact.createEdit.path(id)}>
            <button className="border border-slate-300 bg-white rounded px-6 py-2 cursor-pointer hover:bg-slate-100 text-lg">
              Edit Contact
            </button>
          </a>
        </div>
        <div className="my-8 space-y-3 text-lg">
          <div className="flex">
            <div className="w-44 font-semibold">Birthday:</div>
            <div>{data.birthday || '--'}</div>
          </div>
          <div className="flex">
            <div className="w-44 font-semibold">Address:</div>
            <div>{addressString}</div>
          </div>
          <div className="flex">
            <div className="w-44 font-semibold">Date Added:</div>
            <div>
              {data.createdAt
                ? new Date(data.createdAt).toLocaleDateString(undefined, {
                    month: 'short',
                    day: 'numeric',
                    year: 'numeric',
                  })
                : '--'}
            </div>
          </div>
        </div>
        <OrdersTable orders={data.orders} />
      </div>
    </div>
  )
}
