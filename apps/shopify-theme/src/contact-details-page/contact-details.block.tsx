import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import '../index.css'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { ContactDetailsPage } from './contact-details.page'

const queryClient = new QueryClient()

createRoot(document.getElementById('contact-details-page')!).render(
  <StrictMode>
    <QueryClientProvider client={queryClient}>
      <ContactDetailsPage />
    </QueryClientProvider>
  </StrictMode>,
)
