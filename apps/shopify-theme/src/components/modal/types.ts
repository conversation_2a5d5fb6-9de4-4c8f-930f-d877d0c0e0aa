import type { MutableRefObject, ReactNode } from 'react'

export type ModalSize =
  | 'md'
  | 'lg'
  | 'xl'
  | '2xl'
  | '7xl'
  | '6xl'
  | '3xl'
  | '8xl'

export type ModalVerticalPosition = 'center' | 'top'
export type ModalHorizontalPosition = 'center' | 'right'

export type ModalHeaderType =
  | 'without-border'
  | 'only-close-button'
  | 'with-border'

export type ModalScreenFill = 'none' | 'sm' | 'all'

export type ModalAnimation = 'right-to-left'

type BaseModalProps = {
  children: ReactNode
  id?: string
  'data-test-id'?: string
  onClose?: () => void
  target?: string
  screenFill?: ModalScreenFill
  horizontalPosition?: ModalHorizontalPosition
  withScroll?: boolean
  position?: ModalVerticalPosition
  isOverflowHidden?: boolean
  isBodyScrollLockDisabled?: boolean
  size?: ModalSize
  rounded?: ModalRounded
  isOpen: boolean
}

export type ModalProps = BaseModalProps & {
  withPadding?: boolean
}

export type AnimatedModalProps = BaseModalProps & {
  animationVariant?: ModalAnimation
}

export type ModalContainerProps = Omit<BaseModalProps, 'isOpen' | 'onClose'> & {
  size: ModalSize
  containerRef: MutableRefObject<HTMLDivElement | null>
  position: ModalVerticalPosition
  screenFill: ModalScreenFill
  horizontalPosition: ModalHorizontalPosition
  overlayClass?: string
  modalClass?: string
  withPadding: boolean
  isOverflowHidden: boolean
  rounded?: ModalRounded
}

export type ModalRounded = 'none' | 'onlyLeft' | 'onlyRight' | 'both'
