import { useModal } from '../hooks/use-modal'
import { defaultModalProps } from '../modal.const'
import { ModalCloseButton } from '../modal-close-button'
import { ModalContainer } from '../modal-container'

import { ModalBody } from './modal-body'
import { ModalFooter } from './modal-footer'
import { ModalHeader } from './modal-header'

import type { ModalProps } from '../types'

function ModalBase({
  isOpen,
  onClose,
  children,
  isBodyScrollLockDisabled,
  ...props
}: ModalProps) {
  const { ref } = useModal({
    isOpen,
    onClose,
    isBodyScrollLockDisabled,
  })

  if (!isOpen) return null

  const modalContainerProps = { ...defaultModalProps, ...props }

  return (
    <ModalContainer containerRef={ref} {...modalContainerProps}>
      {children}
    </ModalContainer>
  )
}

export const Modal = Object.assign(ModalBase, {
  Footer: ModalFooter,
  Header: <PERSON><PERSON><PERSON>ead<PERSON>,
  Body: Modal<PERSON><PERSON>,
  CloseButton: ModalCloseButton,
})
