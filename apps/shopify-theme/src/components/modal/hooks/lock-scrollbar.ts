export function unlockScrollWithPosFixed(): void {
  const body = document.body
  const scrollY = parseInt(body.style.top || '0', 10)
  body.style.position = ''
  body.style.top = ''
  body.style.width = ''
  body.style.paddingRight = ''
  window.scrollTo(0, -scrollY)
}

export function lockScrollWithPosFixed(
  scrollY: number,
  scrollBarWidth: number,
): void {
  const body = document.body
  body.style.position = 'fixed'
  body.style.top = `-${scrollY}px`
  body.style.width = '100%'
  body.style.paddingRight = `${scrollBarWidth}px`
}
