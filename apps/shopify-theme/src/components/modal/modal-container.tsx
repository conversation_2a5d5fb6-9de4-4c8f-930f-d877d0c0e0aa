import clsx from 'clsx'

import { Portal } from './portal'

import {
  horiziontalPositionMap,
  positionMap,
  roundedMap,
  screenFillMap,
  sizeMap,
} from './modal.const'

import type { ModalContainerProps } from './types'

export function ModalContainer({
  children,
  id,
  'data-test-id': dataTestId,
  size,
  screenFill,
  withScroll,
  position,
  target,
  withPadding,
  isOverflowHidden,
  containerRef,
  horizontalPosition,
  modalClass: modalClassFromProps,
  overlayClass: overlayClassFromProps,
  rounded = 'none',
}: ModalContainerProps) {
  const isFullScreen = screenFill === 'all'
  const overlayClass = clsx(
    overlayClassFromProps,
    horiziontalPositionMap[horizontalPosition],
    'fixed inset-0 bottom-0 left-0 right-0 top-0 z-40 flex h-full w-screen overflow-y-auto bg-gray-900/50',
  )

  const modalContainerClass = clsx(
    sizeMap[size],
    !isFullScreen && 'lg:my-10',
    'max-h-full w-full',
  )
  const modalClass = clsx(
    roundedMap[rounded],
    screenFillMap[screenFill],
    withScroll && 'overflow-y-auto overscroll-y-none overflow-x-hidden',
    positionMap[position],
    isOverflowHidden && 'overflow-hidden',
    withPadding && 'p-5',
    modalClassFromProps,
    'relative max-h-full w-full bg-white flex flex-col shadow-xs max-md:h-full max-md:w-full',
  )

  return (
    <Portal target={target}>
      <div className={overlayClass}>
        <div className={modalContainerClass}>
          <div className="flex h-full max-h-full items-center justify-center">
            <div
              id={id}
              ref={containerRef}
              data-test-id={dataTestId}
              className={modalClass}
            >
              {children}
            </div>
          </div>
        </div>
      </div>
    </Portal>
  )
}
