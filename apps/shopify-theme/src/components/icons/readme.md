# How to add new icon

- open figma file with all icons
- select Icon with 22 * 22 size
- define name for component as name of icon in figma
- copy svg result
- change width and height with 100%, like `width="100%"`
- if this is monochrome icon, change colors to `currentColor` value.
- if this is colored icon, no need to change colors
- check that viewBox is `0 0 22 22`
- put new svg as component into `assets/22` if this is ordinary icon, or into `assets/logos` if this is logo
- declare export in `icon-22.ts` or `logos.ts` file
- check storybook with new icon

![example of how to copy](add-icon-figma.png 'example of how to copy')
