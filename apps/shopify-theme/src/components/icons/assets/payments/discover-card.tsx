export const DiscoverCard = () => {
  return (
    <svg
      width="100%"
      height="100%"
      viewBox="0 0 22 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="0.973999"
        y="4"
        width="20"
        height="13.4483"
        rx="2"
        fill="white"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M2.64067 4H19.3073C20.2278 4 20.974 4.75262 20.974 5.68103V15.7672C20.974 16.6957 20.2278 17.4483 19.3073 17.4483H2.64067C1.72019 17.4483 0.973999 16.6957 0.973999 15.7672V5.68103C0.973999 4.75262 1.72019 4 2.64067 4ZM20.1407 5.68103C20.1407 5.21683 19.7676 4.84052 19.3074 4.84052H2.64069C2.18045 4.84052 1.80735 5.21683 1.80735 5.68103V15.7672C1.80735 16.2314 2.18045 16.6078 2.64069 16.6078H19.3074C19.7676 16.6078 20.1407 16.2314 20.1407 15.7672V5.68103Z"
        fill="#231F20"
      />
      <path
        d="M20.1404 11.447V15.7673C20.1404 16.2315 19.7673 16.6078 19.3071 16.6078H5.94043C15.1404 15.0192 20.1404 11.447 20.1404 11.447Z"
        fill="#EE7623"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M2.64038 8.47154H3.40288C3.77515 8.43939 4.14351 8.56777 4.41668 8.82488C4.68986 9.08199 4.84229 9.44376 4.83621 9.82057C4.83542 10.2196 4.66007 10.5979 4.35705 10.8544C4.08667 11.0709 3.74774 11.1814 3.40288 11.1654H2.64038V8.47154ZM3.29868 10.7242C3.55732 10.7467 3.81409 10.6634 4.01118 10.493C4.20074 10.3184 4.30821 10.0711 4.30701 9.8122C4.30742 9.55589 4.19988 9.31146 4.01118 9.13979C3.81149 8.97514 3.55545 8.89661 3.29868 8.92126H3.16118V10.7242H3.29868Z"
        fill="#231F20"
      />
      <rect
        x="5.07776"
        y="8.46729"
        width="0.520833"
        height="2.69806"
        fill="#231F20"
      />
      <path
        d="M6.87773 9.50118C6.56523 9.38771 6.46106 9.30786 6.46106 9.16497C6.46106 9.02209 6.62356 8.86239 6.84856 8.86239C7.0127 8.86934 7.16532 8.94939 7.26523 9.08092L7.53606 8.7237C7.32225 8.5266 7.04237 8.41849 6.75273 8.42112C6.53932 8.40731 6.3296 8.4821 6.17222 8.62813C6.01485 8.77417 5.9235 8.97874 5.91939 9.19439C5.91939 9.56842 6.08606 9.75754 6.57356 9.93405C6.69887 9.97454 6.82022 10.0266 6.93606 10.0895C7.03427 10.1467 7.09466 10.2525 7.09439 10.3669C7.09364 10.4748 7.0482 10.5775 6.96907 10.6501C6.88994 10.7228 6.78431 10.7587 6.67773 10.7493C6.43473 10.752 6.21285 10.6105 6.11106 10.3879L5.77356 10.7157C5.97052 11.0521 6.33683 11.2498 6.72356 11.2284C6.96406 11.245 7.20038 11.1588 7.37475 10.991C7.54913 10.8231 7.64547 10.589 7.64023 10.3459C7.64023 9.91724 7.46939 9.71972 6.87773 9.50118Z"
        fill="#231F20"
      />
      <path
        d="M7.81146 9.81647C7.808 10.1938 7.95625 10.5565 8.22239 10.8218C8.48854 11.0871 8.84987 11.2323 9.22396 11.2243C9.45117 11.2257 9.67543 11.1724 9.87813 11.0688V10.4511C9.72074 10.6367 9.4911 10.744 9.24896 10.7452C9.00403 10.7514 8.76765 10.6543 8.5968 10.4771C8.42594 10.3 8.33611 10.059 8.34896 9.81226C8.34111 9.57197 8.42941 9.33859 8.594 9.16468C8.75859 8.99077 8.98562 8.89095 9.22396 8.88769C9.47507 8.88871 9.71353 8.99901 9.87813 9.19028V8.5725C9.68175 8.4627 9.46099 8.40487 9.23646 8.4044C8.86151 8.39991 8.50027 8.54644 8.23278 8.8115C7.96529 9.07655 7.81365 9.43826 7.81146 9.81647Z"
        fill="#231F20"
      />
      <path
        d="M14.0154 10.2786L13.2987 8.46729H12.7321L13.8654 11.2326H14.1446L15.3029 8.46729H14.7362L14.0154 10.2786Z"
        fill="#231F20"
      />
      <path
        d="M15.5363 11.1654H17.0196V10.7073H16.0571V9.98028H16.9821V9.5222H16.0571V8.92543H17.0196V8.46735H15.5363V11.1654Z"
        fill="#231F20"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M19.0821 9.2617C19.1103 9.63918 18.845 9.97462 18.4738 10.0308L19.3071 11.1655H18.6738L17.9488 10.0812H17.8863V11.1655H17.3655V8.47162H18.1363C18.7363 8.47162 19.0821 8.7616 19.0821 9.2617ZM17.9031 9.70715H18.0572C18.3697 9.70715 18.5531 9.59788 18.5531 9.31211C18.5531 9.02633 18.3781 8.89185 18.0572 8.89185H17.9031V9.70715Z"
        fill="#231F20"
      />
      <path
        d="M12.8861 9.81644C12.8861 10.6079 12.25 11.2495 11.4653 11.2495C10.6806 11.2495 10.0444 10.6079 10.0444 9.81644C10.0444 9.02497 10.6806 8.38336 11.4653 8.38336C12.249 8.38567 12.8838 9.02593 12.8861 9.81644Z"
        fill="#EE7623"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M19.3533 8.51786C19.3564 8.54856 19.337 8.57702 19.3075 8.5851L19.3783 8.69016H19.3158L19.2408 8.5893V8.69016H19.1866V8.44641H19.2658C19.3241 8.44641 19.3533 8.47163 19.3533 8.51786ZM19.2572 8.49268V8.55151H19.2655C19.3006 8.55151 19.3003 8.54556 19.2993 8.52864C19.2991 8.52546 19.2989 8.52189 19.2989 8.51789C19.2989 8.49268 19.2822 8.49268 19.2572 8.49268Z"
        fill="#231F20"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M19.1389 8.42139C19.1764 8.38198 19.2279 8.35922 19.2821 8.35812C19.3391 8.35458 19.395 8.37528 19.4361 8.41522C19.4773 8.45515 19.5 8.51065 19.4987 8.56825C19.4987 8.6843 19.4055 8.77838 19.2904 8.77838C19.1753 8.77838 19.0821 8.6843 19.0821 8.56825C19.081 8.51363 19.1014 8.4608 19.1389 8.42139ZM19.2822 8.73645C19.322 8.73693 19.3599 8.72008 19.3864 8.69022C19.4213 8.65952 19.441 8.615 19.4406 8.56834C19.4428 8.52416 19.4273 8.48092 19.3975 8.44847C19.3677 8.41602 19.3261 8.39711 19.2822 8.39604C19.238 8.39714 19.1961 8.41591 19.1656 8.44823C19.1351 8.48055 19.1186 8.52375 19.1197 8.56834C19.1186 8.61253 19.1352 8.65531 19.1658 8.68696C19.1964 8.71862 19.2384 8.73646 19.2822 8.73645Z"
        fill="#231F20"
      />
    </svg>
  )
}
