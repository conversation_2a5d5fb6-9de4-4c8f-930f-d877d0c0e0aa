export function WalkingSolid() {
  return (
    <svg
      width="100%"
      height="100%"
      viewBox="0 0 22 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M15.0098 2.87683C14.9837 3.39818 14.8012 3.84132 14.4624 4.20626C14.0974 4.54513 13.6543 4.7276 13.1329 4.75367C12.6116 4.7276 12.1685 4.54513 11.8035 4.20626C11.4646 3.84132 11.2822 3.39818 11.2561 2.87683C11.2822 2.35549 11.4646 1.91235 11.8035 1.54741C12.1685 1.20854 12.6116 1.02607 13.1329 1C13.6543 1.02607 14.0974 1.20854 14.4624 1.54741C14.8012 1.91235 14.9837 2.35549 15.0098 2.87683ZM9.61388 8.31183H9.57478L8.75367 8.70283C8.02379 9.04171 7.52851 9.58912 7.26784 10.3451L7.15054 10.697C6.91593 11.1922 6.51189 11.3877 5.93842 11.2835C5.70381 11.1792 5.53438 11.0098 5.43011 10.7752C5.29977 10.5666 5.2737 10.332 5.35191 10.0714L5.46921 9.75855C5.91235 8.48126 6.7465 7.55588 7.97165 6.9824L8.79277 6.5914C9.67905 6.20039 10.6044 6.00489 11.5689 6.00489C12.5334 6.03095 13.3936 6.31769 14.1496 6.8651C14.8794 7.41251 15.4008 8.15543 15.7136 9.09384L16.1437 10.5015L17.1994 11.5963C17.5904 12.0394 17.5904 12.4695 17.1994 12.8866C16.7823 13.2776 16.3522 13.2776 15.9091 12.8866L14.7752 11.7918C14.5927 11.6093 14.4624 11.3877 14.3842 11.1271L13.9932 9.87586L12.8592 13.5513L14.8143 15.1544C15.0749 15.3891 15.2574 15.6628 15.3617 15.9756L16.2219 19.8856C16.3001 20.433 16.0655 20.798 15.5181 20.9804C14.9707 21.0587 14.6057 20.824 14.4233 20.2766L13.5631 16.523L9.45748 13.2385C8.7276 12.5868 8.49299 11.7788 8.75367 10.8143L9.61388 8.31183ZM11.7253 7.88172L10.5523 11.4399C10.5002 11.5702 10.5262 11.6745 10.6305 11.7527L11.2952 12.3001L12.5464 8.23363C12.5725 8.18149 12.5855 8.14239 12.5855 8.11632C12.3249 7.98599 12.0381 7.90779 11.7253 7.88172ZM7.97165 16.7576L8.91007 14.3724L10.435 15.6237L9.69208 17.5005C9.58781 17.709 9.47051 17.8785 9.34018 18.0088L6.60313 20.7067C6.15999 21.0978 5.71685 21.0978 5.2737 20.7067C4.90877 20.2897 4.90877 19.8596 5.2737 19.4164L7.97165 16.7576Z"
        fill="currentColor"
      />
    </svg>
  )
}
