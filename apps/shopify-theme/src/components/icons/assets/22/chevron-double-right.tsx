export function ChevronDoubleRight() {
  return (
    <svg
      width="100%"
      height="100%"
      viewBox="0 0 22 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M11.3223 17.2777C11.116 17.0714 11.0002 16.7917 11.0002 16.5C11.0002 16.2083 11.116 15.9286 11.3223 15.7223L16.0446 11L11.3223 6.27769C11.2172 6.17622 11.1334 6.05484 11.0758 5.92064C11.0181 5.78643 10.9878 5.64209 10.9865 5.49603C10.9852 5.34998 11.0131 5.20513 11.0684 5.06994C11.1237 4.93476 11.2054 4.81194 11.3086 4.70866C11.4119 4.60537 11.5347 4.5237 11.6699 4.46839C11.8051 4.41308 11.95 4.38525 12.096 4.38652C12.2421 4.38779 12.3864 4.41813 12.5206 4.47578C12.6548 4.53343 12.7762 4.61723 12.8777 4.72229L18.3777 10.2223C18.5839 10.4286 18.6997 10.7083 18.6997 11C18.6997 11.2917 18.5839 11.5714 18.3777 11.7777L12.8777 17.2777C12.6714 17.4839 12.3916 17.5998 12.1 17.5998C11.8083 17.5998 11.5285 17.4839 11.3223 17.2777Z"
        fill="currentColor"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M4.72233 17.2777C4.51612 17.0714 4.40027 16.7917 4.40027 16.5C4.40027 16.2083 4.51612 15.9286 4.72233 15.7223L9.44463 11L4.72233 6.2777C4.52196 6.07023 4.41109 5.79237 4.41359 5.50396C4.4161 5.21554 4.53178 4.93964 4.73573 4.7357C4.93968 4.53175 5.21558 4.41606 5.50399 4.41355C5.79241 4.41105 6.07027 4.52192 6.27773 4.7223L11.7777 10.2223C11.984 10.4286 12.0998 10.7083 12.0998 11C12.0998 11.2917 11.984 11.5714 11.7777 11.7777L6.27773 17.2777C6.07145 17.4839 5.79171 17.5998 5.50003 17.5998C5.20835 17.5998 4.92861 17.4839 4.72233 17.2777Z"
        fill="currentColor"
      />
    </svg>
  )
}
