export function QuestionMarkCircle() {
  return (
    <svg
      width="100%"
      height="100%"
      viewBox="0 0 22 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M19.8 11C19.8 13.3339 18.8728 15.5722 17.2225 17.2225C15.5722 18.8729 13.3339 19.8 11 19.8C8.66605 19.8 6.42773 18.8729 4.77741 17.2225C3.12709 15.5722 2.19995 13.3339 2.19995 11C2.19995 8.66609 3.12709 6.42778 4.77741 4.77746C6.42773 3.12714 8.66605 2.2 11 2.2C13.3339 2.2 15.5722 3.12714 17.2225 4.77746C18.8728 6.42778 19.8 8.66609 19.8 11ZM11 7.7C10.8067 7.69981 10.6168 7.75055 10.4493 7.8471C10.2819 7.94366 10.1429 8.08262 10.0463 8.25C9.97647 8.37966 9.88137 8.494 9.76659 8.58623C9.6518 8.67845 9.51967 8.7467 9.37802 8.78691C9.23637 8.82713 9.08809 8.83849 8.94197 8.82033C8.79584 8.80218 8.65485 8.75487 8.52735 8.68121C8.39985 8.60756 8.28844 8.50905 8.19972 8.39154C8.11099 8.27402 8.04677 8.13989 8.01084 7.99709C7.97492 7.85429 7.96803 7.70574 7.99059 7.56023C8.01314 7.41472 8.06468 7.27522 8.14215 7.15C8.50539 6.52092 9.06605 6.02928 9.73718 5.75131C10.4083 5.47334 11.1524 5.42458 11.8541 5.61259C12.5557 5.80061 13.1758 6.21488 13.618 6.79118C14.0602 7.36748 14.2999 8.07358 14.3 8.8C14.3001 9.48268 14.0887 10.1486 13.6947 10.7061C13.3007 11.2636 12.7435 11.6853 12.1 11.913V12.1C12.1 12.3917 11.9841 12.6715 11.7778 12.8778C11.5715 13.0841 11.2917 13.2 11 13.2C10.7082 13.2 10.4284 13.0841 10.2221 12.8778C10.0158 12.6715 9.89995 12.3917 9.89995 12.1V11C9.89995 10.7083 10.0158 10.4285 10.2221 10.2222C10.4284 10.0159 10.7082 9.9 11 9.9C11.2917 9.9 11.5715 9.78411 11.7778 9.57782C11.9841 9.37153 12.1 9.09174 12.1 8.8C12.1 8.50826 11.9841 8.22847 11.7778 8.02218C11.5715 7.81589 11.2917 7.7 11 7.7ZM11 16.5C11.2917 16.5 11.5715 16.3841 11.7778 16.1778C11.9841 15.9715 12.1 15.6917 12.1 15.4C12.1 15.1083 11.9841 14.8285 11.7778 14.6222C11.5715 14.4159 11.2917 14.3 11 14.3C10.7082 14.3 10.4284 14.4159 10.2221 14.6222C10.0158 14.8285 9.89995 15.1083 9.89995 15.4C9.89995 15.6917 10.0158 15.9715 10.2221 16.1778C10.4284 16.3841 10.7082 16.5 11 16.5Z"
        fill="currentColor"
      />
    </svg>
  )
}
