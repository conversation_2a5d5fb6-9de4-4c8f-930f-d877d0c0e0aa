export const Link = () => (
  <svg
    width="100%"
    height="100%"
    viewBox="0 0 22 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M13.8445 5.04461C14.0475 4.83448 14.2902 4.66688 14.5587 4.55158C14.8271 4.43628 15.1157 4.37559 15.4079 4.37306C15.7 4.37052 15.9897 4.42618 16.26 4.5368C16.5304 4.64742 16.776 4.81077 16.9826 5.01734C17.1892 5.2239 17.3525 5.46954 17.4632 5.73991C17.5738 6.01028 17.6294 6.29997 17.6269 6.59209C17.6244 6.8842 17.5637 7.17289 17.4484 7.4413C17.3331 7.70971 17.1655 7.95246 16.9553 8.15541L13.6553 11.4554C13.2428 11.8678 12.6833 12.0995 12.0999 12.0995C11.5166 12.0995 10.9571 11.8678 10.5445 11.4554C10.3371 11.255 10.0592 11.1442 9.7708 11.1467C9.48238 11.1492 9.20649 11.2649 9.00254 11.4688C8.79859 11.6728 8.68291 11.9486 8.6804 12.2371C8.67789 12.5255 8.78877 12.8033 8.98914 13.0108C9.81426 13.8357 10.9332 14.2991 12.0999 14.2991C13.2667 14.2991 14.3856 13.8357 15.2107 13.0108L18.5107 9.71081C19.3122 8.88096 19.7557 7.76951 19.7457 6.61584C19.7357 5.46218 19.2729 4.3586 18.4571 3.5428C17.6413 2.72701 16.5378 2.26427 15.3841 2.25424C14.2304 2.24422 13.119 2.68771 12.2891 3.48921L10.6391 5.13921C10.5341 5.24068 10.4503 5.36206 10.3926 5.49626C10.335 5.63047 10.3046 5.77481 10.3034 5.92087C10.3021 6.06692 10.3299 6.21177 10.3852 6.34696C10.4405 6.48214 10.5222 6.60496 10.6255 6.70824C10.7288 6.81152 10.8516 6.8932 10.9868 6.94851C11.122 7.00382 11.2668 7.03165 11.4129 7.03038C11.5589 7.02911 11.7033 6.99877 11.8375 6.94112C11.9717 6.88347 12.0931 6.79967 12.1945 6.69461L13.8445 5.04461ZM8.34454 10.5446C8.7571 10.1322 9.31658 9.90048 9.89994 9.90048C10.4833 9.90048 11.0428 10.1322 11.4553 10.5446C11.5568 10.6497 11.6782 10.7335 11.8124 10.7911C11.9466 10.8488 12.0909 10.8791 12.237 10.8804C12.3831 10.8817 12.5279 10.8538 12.6631 10.7985C12.7983 10.7432 12.9211 10.6615 13.0244 10.5582C13.1277 10.455 13.2093 10.3321 13.2646 10.197C13.32 10.0618 13.3478 9.91692 13.3465 9.77087C13.3452 9.62481 13.3149 9.48047 13.2573 9.34626C13.1996 9.21206 13.1158 9.09068 13.0107 8.98921C12.1856 8.16433 11.0667 7.70095 9.89994 7.70095C8.73322 7.70095 7.61426 8.16433 6.78914 8.98921L3.48914 12.2892C3.0689 12.6951 2.7337 13.1806 2.5031 13.7174C2.2725 14.2542 2.15112 14.8316 2.14604 15.4158C2.14096 16.0001 2.25229 16.5795 2.47353 17.1202C2.69476 17.6609 3.02148 18.1522 3.4346 18.5653C3.84773 18.9785 4.339 19.3052 4.87974 19.5264C5.42049 19.7477 5.99988 19.859 6.58411 19.8539C7.16834 19.8488 7.7457 19.7275 8.28252 19.4969C8.81934 19.2663 9.30486 18.9311 9.71074 18.5108L11.3607 16.8608C11.4658 16.7593 11.5496 16.638 11.6073 16.5038C11.6649 16.3695 11.6952 16.2252 11.6965 16.0791C11.6978 15.9331 11.67 15.7882 11.6146 15.6531C11.5593 15.5179 11.4777 15.3951 11.3744 15.2918C11.2711 15.1885 11.1483 15.1068 11.0131 15.0515C10.8779 14.9962 10.7331 14.9684 10.587 14.9696C10.4409 14.9709 10.2966 15.0012 10.1624 15.0589C10.0282 15.1165 9.90681 15.2003 9.80534 15.3054L8.15534 16.9554C7.9524 17.1655 7.70964 17.3331 7.44123 17.4484C7.17282 17.5637 6.88414 17.6244 6.59203 17.627C6.29991 17.6295 6.01022 17.5738 5.73984 17.4632C5.46947 17.3526 5.22384 17.1892 5.01727 16.9827C4.81071 16.7761 4.64735 16.5305 4.53673 16.2601C4.42612 15.9897 4.37045 15.7 4.37299 15.4079C4.37553 15.1158 4.43622 14.8271 4.55152 14.5587C4.66682 14.2903 4.83442 14.0476 5.04454 13.8446L8.34454 10.5446Z"
      fill="currentColor"
    />
  </svg>
)
