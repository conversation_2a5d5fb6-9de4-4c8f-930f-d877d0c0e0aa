export function Truck() {
  return (
    <svg
      width="22"
      height="22"
      viewBox="0 0 22 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M7.71301 16.8797C8.05682 16.5359 8.24998 16.0696 8.24998 15.5833C8.24998 15.0971 8.05682 14.6308 7.71301 14.287C7.36919 13.9432 6.90288 13.75 6.41665 13.75C5.93042 13.75 5.4641 13.9432 5.12028 14.287C4.77647 14.6308 4.58331 15.0971 4.58331 15.5833C4.58331 16.0696 4.77647 16.5359 5.12028 16.8797C5.4641 17.2235 5.93042 17.4167 6.41665 17.4167C6.90288 17.4167 7.36919 17.2235 7.71301 16.8797Z"
        stroke="#111928"
      />
      <path
        d="M16.8797 16.8797C17.2235 16.5359 17.4166 16.0696 17.4166 15.5833C17.4166 15.0971 17.2235 14.6308 16.8797 14.287C16.5359 13.9432 16.0695 13.75 15.5833 13.75C15.0971 13.75 14.6308 13.9432 14.287 14.287C13.9431 14.6308 13.75 15.0971 13.75 15.5833C13.75 16.0696 13.9431 16.5359 14.287 16.8797C14.6308 17.2235 15.0971 17.4167 15.5833 17.4167C16.0695 17.4167 16.5359 17.2235 16.8797 16.8797Z"
        stroke="#111928"
      />
      <path
        d="M11.9167 14.6667V5.49999C11.9167 5.25688 11.8201 5.02372 11.6482 4.85181C11.4763 4.67991 11.2431 4.58333 11 4.58333H3.66667C3.42355 4.58333 3.19039 4.67991 3.01849 4.85181C2.84658 5.02372 2.75 5.25688 2.75 5.49999V14.6667C2.75 14.9098 2.84658 15.1429 3.01849 15.3148C3.19039 15.4868 3.42355 15.5833 3.66667 15.5833H4.58333M11.9167 14.6667C11.9167 14.9098 11.8201 15.1429 11.6482 15.3148C11.4763 15.4868 11.2431 15.5833 11 15.5833H8.25M11.9167 14.6667V7.33333C11.9167 7.09021 12.0132 6.85706 12.1852 6.68515C12.3571 6.51324 12.5902 6.41666 12.8333 6.41666H15.2038C15.4469 6.41671 15.68 6.51332 15.8519 6.68525L18.9814 9.81474C19.1533 9.98661 19.2499 10.2197 19.25 10.4628V14.6667C19.25 14.9098 19.1534 15.1429 18.9815 15.3148C18.8096 15.4868 18.5764 15.5833 18.3333 15.5833H17.4167M11.9167 14.6667C11.9167 14.9098 12.0132 15.1429 12.1852 15.3148C12.3571 15.4868 12.5902 15.5833 12.8333 15.5833H13.75M4.58333 15.5833C4.58333 16.0696 4.77649 16.5359 5.1203 16.8797C5.46412 17.2235 5.93044 17.4167 6.41667 17.4167C6.9029 17.4167 7.36921 17.2235 7.71303 16.8797C8.05685 16.5359 8.25 16.0696 8.25 15.5833M4.58333 15.5833C4.58333 15.0971 4.77649 14.6308 5.1203 14.287C5.46412 13.9431 5.93044 13.75 6.41667 13.75C6.9029 13.75 7.36921 13.9431 7.71303 14.287C8.05685 14.6308 8.25 15.0971 8.25 15.5833M17.4167 15.5833C17.4167 16.0696 17.2235 16.5359 16.8797 16.8797C16.5359 17.2235 16.0696 17.4167 15.5833 17.4167C15.0971 17.4167 14.6308 17.2235 14.287 16.8797C13.9432 16.5359 13.75 16.0696 13.75 15.5833M17.4167 15.5833C17.4167 15.0971 17.2235 14.6308 16.8797 14.287C16.5359 13.9431 16.0696 13.75 15.5833 13.75C15.0971 13.75 14.6308 13.9431 14.287 14.287C13.9432 14.6308 13.75 15.0971 13.75 15.5833"
        stroke="#111928"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}
