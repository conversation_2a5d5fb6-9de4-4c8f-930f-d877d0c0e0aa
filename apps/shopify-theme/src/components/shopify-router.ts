export const ShopifyRouter = {
  contact: {
    details: {
      path: (contactId: string | number) =>
        `/pages/contact-details?id=${contactId}`,
    },
    createEdit: {
      path: (contactId?: string | number) => {
        if (contactId) {
          return `/pages/contact-edit?id=${contactId}`
        }
        return '/pages/contact-edit'
      },
    },
  },
  order: {
    details: {
      path: (orderId: string | number) => `/pages/order-details?id=${orderId}`,
    },
  },
}
