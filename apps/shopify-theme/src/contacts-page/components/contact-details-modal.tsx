/* eslint-disable @typescript-eslint/no-explicit-any */
import { Modal } from '../../components/modal'
import { useState } from 'react'
import { OrderDetailsModal } from '../../orders-page/order-details-modal'
import { useQuery } from '@tanstack/react-query'

export interface ContactOrder {
  id: string
  orderNumber: string
  createdAt: string
  totalPrice: number
  currency: string
  financialStatus: string
  buyerName: string
}

export interface ContactNote {
  id: string
  date: string
  text: string
}

export interface ContactDetailsModalProps {
  isOpen: boolean
  onClose: () => void
  name: string
  email: string
  phone: string
  address: string
  birthday: string
  orders: ContactOrder[]
  notes: ContactNote[]
  loading?: boolean
}

function formatCurrency(amount: number, currency = 'USD') {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
    minimumFractionDigits: 2,
  }).format(amount / 100)
}

function formatDate(date: string) {
  return new Date(date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  })
}

function mapOrderDetailsToModalProps(order: any) {
  if (!order) return null
  const customerName = order.buyer
    ? [order.buyer.firstName, order.buyer.lastName].filter(Boolean).join(' ')
    : ''
  // Format address fields if they are objects
  function formatAddress(addr: any) {
    if (!addr || typeof addr === 'string') return addr || ''
    return [
      addr.street_1,
      addr.street_2,
      `${addr.city}${addr.state_code ? ', ' + addr.state_code : ''} ${addr.zip_code || ''}`.trim(),
      addr.country_code,
    ]
      .filter(Boolean)
      .join('\n')
  }
  return {
    orderNumber: order.orderNumber || order.number || '',
    customerName,
    email: order.buyer?.email || '',
    phone: '',
    orderDate: order.createdAt
      ? new Date(order.createdAt).toLocaleString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit',
        })
      : '',
    trackingNumber: order.trackingNumber || '',
    shippingAddress: formatAddress(order.shippingAddress || order.address),
    billingAddress: formatAddress(order.billingAddress),
    status: order.financialStatus || '',
    shipmentType: 'Shipment',
    summary: {
      subtotal: formatCurrency(order.totalPrice || 0, order.currency),
      shipping: formatCurrency(order.totalShipping || 0, order.currency),
      taxes: formatCurrency(order.totalTax || 0, order.currency),
      total: formatCurrency(order.totalPrice || 0, order.currency),
      ppv: order.ppv ? String(order.ppv) : '0.00',
      cv: order.cv ? String(order.cv) : '0.00',
    },
    items: Array.isArray(order.items)
      ? order.items.map((item: any) => ({
          image: item.skuData?.image_url || '',
          name: item.skuData?.name || '',
          sku: item.skuData?.code || '',
          qty: item.quantity || 1,
          price: formatCurrency(
            item.itemData?.price_cents || 0,
            order.currency,
          ),
        }))
      : [],
  }
}

export function ContactDetailsModal({
  isOpen,
  onClose,
  name,
  email,
  phone,
  address,
  birthday,
  orders,
  notes,
  loading = false,
}: ContactDetailsModalProps) {
  const [note, setNote] = useState('')
  const [orderModalOpen, setOrderModalOpen] = useState(false)
  const [selectedOrderId, setSelectedOrderId] = useState<string | null>(null)

  function handleOrderRowClick(order: ContactOrder) {
    setSelectedOrderId(order.id)
    setOrderModalOpen(true)
  }

  // Fetch order details when an order is selected
  const { data: orderDetails, isLoading: isOrderLoading } = useQuery({
    queryKey: ['order-details', selectedOrderId],
    queryFn: async () => {
      if (!selectedOrderId) return null
      const response = await fetch(`/a/api/order/${selectedOrderId}`)
      if (!response.ok) throw new Error('Failed to fetch order details')
      return response.json()
    },
    enabled: !!selectedOrderId && orderModalOpen,
  })

  const orderModalProps = orderDetails
    ? mapOrderDetailsToModalProps(orderDetails)
    : null

  return (
    <>
      <Modal
        horizontalPosition="right"
        screenFill="all"
        size="8xl"
        rounded="onlyLeft"
        isOpen={isOpen}
        onClose={onClose}
      >
        <Modal.Header onClose={onClose} />
        <Modal.Body>
          {loading ? (
            <div className="flex flex-col items-center justify-center min-h-[400px]">
              <div className="mb-6">
                <svg
                  className="animate-spin h-10 w-10 text-purple-400"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"
                  ></path>
                </svg>
              </div>
              <div className="text-lg font-semibold text-purple-700">
                Loading contact details...
              </div>
            </div>
          ) : (
            <>
              {/* Header */}
              <div className="text-2xl font-bold mb-6">{name}</div>
              {/* Info Card */}
              <div className="rounded-2xl bg-purple-50 p-6 flex flex-col md:flex-row gap-8 text-sm text-gray-700 mb-6">
                <div className="flex-1 flex flex-col gap-2">
                  <div className="font-medium text-xs text-gray-400">Email</div>
                  <div className="flex items-center gap-2">
                    <span>{email}</span>
                  </div>
                  <div className="font-medium text-xs text-gray-400 mt-4">
                    Address
                  </div>
                  <div className="flex items-center gap-2">
                    <span style={{ whiteSpace: 'pre-line' }}>{address}</span>
                  </div>
                </div>
                <div className="flex-1 flex flex-col gap-2">
                  <div className="font-medium text-xs text-gray-400">Phone</div>
                  <div className="flex items-center gap-2">
                    <span>{phone}</span>
                  </div>
                  <div className="font-medium text-xs text-gray-400 mt-4">
                    Birthday
                  </div>
                  <div className="flex items-center gap-2">
                    <span>{birthday}</span>
                  </div>
                </div>
              </div>
              {/* Tabs */}
              <div className="flex flex-row gap-2 mb-4">
                <button className="px-6 py-2 rounded-full bg-gray-100 font-semibold text-black">
                  Orders
                </button>
                <button className="px-6 py-2 rounded-full bg-white font-semibold text-black border border-gray-200 flex items-center gap-2">
                  Active Subscription{' '}
                  <span className="text-red-500">&#x26A0;</span>
                </button>
                <button className="px-6 py-2 rounded-full bg-white font-semibold text-black border border-gray-200">
                  Inactive Subscriptions
                </button>
                <button className="px-6 py-2 rounded-full bg-white font-semibold text-black border border-gray-200">
                  Activity tab
                </button>
              </div>
              {/* Orders Table */}
              <div className="rounded-2xl bg-white p-4 border mb-6">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="text-gray-400 text-left">
                      <th className="py-2 font-semibold">Order #</th>
                      <th className="py-2 font-semibold">Date</th>
                      <th className="py-2 font-semibold">Total</th>
                      <th className="py-2 font-semibold">Status</th>
                    </tr>
                  </thead>
                  <tbody>
                    {orders.map((order) => (
                      <tr
                        key={order.id}
                        className="border-t border-gray-100 hover:bg-gray-50 cursor-pointer"
                        onClick={() => handleOrderRowClick(order)}
                      >
                        <td className="py-2">{order.orderNumber}</td>
                        <td className="py-2">{formatDate(order.createdAt)}</td>
                        <td className="py-2">
                          {formatCurrency(order.totalPrice, order.currency)}
                        </td>
                        <td className="py-2 font-medium">
                          {order.financialStatus}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
              {/* Notes Section */}
              <div className="rounded-2xl bg-gray-50 p-6">
                <div className="font-bold text-lg mb-2">Notes</div>
                <textarea
                  className="w-full rounded-lg border border-gray-200 p-3 mb-4 min-h-[80px] resize-none"
                  placeholder="Add a note about this customer"
                  value={note}
                  onChange={(e) => setNote(e.target.value)}
                />
                <button className="bg-black text-white px-6 py-2 rounded-full font-semibold mb-4">
                  Add Note
                </button>
                {notes.map((n) => (
                  <div
                    key={n.id}
                    className="bg-white rounded-lg p-4 flex items-center justify-between text-sm mb-2"
                  >
                    <div>
                      <div className="text-xs text-gray-400 mb-1">{n.date}</div>
                      <div>{n.text}</div>
                    </div>
                    <div className="flex gap-2">
                      <button className="text-gray-500 hover:text-black">
                        <svg
                          width="18"
                          height="18"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            d="M15.232 5.232l3.536 3.536M9 11l6 6M3 21h18"
                          />
                        </svg>
                      </button>
                      <button className="text-gray-500 hover:text-black">
                        <svg
                          width="18"
                          height="18"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            d="M6 18L18 6M6 6l12 12"
                          />
                        </svg>
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </>
          )}
        </Modal.Body>
      </Modal>
      {orderModalOpen && selectedOrderId && (
        <OrderDetailsModal
          isOpen={orderModalOpen}
          onClose={() => setOrderModalOpen(false)}
          loading={isOrderLoading}
          {...(orderModalProps || {})}
        />
      )}
    </>
  )
}
