<script>
  document.addEventListener('DOMContentLoaded', function () {
    const partnerSearchScript = document.createElement('script')
    partnerSearchScript.type = 'module'
    partnerSearchScript.src = '{{'partner-search.js' | asset_url}}?v=' + Date.now()
    partnerSearchScript.defer = true
    document.body.appendChild(partnerSearchScript)
  })
</script>

{% schema %}
{
  "name": "Partner Search",
  "target": "section"
}
{% endschema %}

<div class='partner-search-wrapper'>
  <div class='partner-search-center'>
    <div class='partner-center-content'>
      <div class='search-container'>
        <div id='find-partner-wrapper' style='display:block'>
          <button
            id='find-partner-button'
            type='button'
            class='find-partner-button'
            style='display:inline'
          >
            Find Partner
          </button>
        </div>
        <div class='search-input-wrapper' id='partner-search-input-wrapper' style='display:none'>
          <svg
            class='search-icon'
            width='16'
            height='16'
            viewBox='0 0 20 20'
            fill='none'
            xmlns='http://www.w3.org/2000/svg'
          >
            <path d="M19 19L14.65 14.65M17 9C17 13.4183 13.4183 17 9 17C4.58172 17 1 13.4183 1 9C1 4.58172 4.58172 1 9 1C13.4183 1 17 4.58172 17 9Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          <input
            type='text'
            id='partner-search-input'
            placeholder='Search partner...'
            autocomplete='off'
          >
          <button
            id='partner-cancel-button'
            class='partner-cancel-button'
            type='button'
            aria-label='Cancel'
            style='display:none'
          >
            <svg width='18' height='18' viewBox='0 0 18 18' fill='none' xmlns='http://www.w3.org/2000/svg'>
              <path d="M13.5 4.5L4.5 13.5" stroke="#212529" stroke-width="2" stroke-linecap="round"/>
              <path d="M4.5 4.5L13.5 13.5" stroke="#212529" stroke-width="2" stroke-linecap="round"/>
            </svg>
          </button>
        </div>
        <div id='partner-search-results' class='search-results' style='display:none'></div>
      </div>
      <div id='current-partner' class='current-partner' style='display:none'>
        <div class='partner-info'>
          <div class='partner-info-text'>
            <span class='partner-email'>You are shopping with:</span>
            <span class='partner-name'></span>
          </div>
          <span id='partner-change-button' class='partner-change-button'>
            <span>|</span>
            <span class='partner-change-button-text'>Change</span>
          </span>
        </div>
      </div>
    </div>
  </div>
</div>
<noscript>
  <style>
    #find-partner-wrapper {
      display: block !important;
    }
    #partner-search-input-wrapper,
    #partner-search-results,
    #current-partner {
      display: none !important;
    }
  </style>
</noscript>

<style>
  .partner-info-text {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 4px;
  }

  .find-partner-button {
    background: none;
    border: none;
    padding: 0;
    margin: 0;
    color: #212529;
    text-decoration: underline;
    cursor: pointer;
    font-size: 15px;
    display: none;
  }

  .partner-change-button {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 4px;
  }

  .partner-change-button-text {
    text-decoration: underline;
    cursor: pointer;
  }

  .partner-search-wrapper {
    background-color: #f8f9fa;
    padding: 6px 0;
    border-bottom: 1px solid #e9ecef;
  }

  .partner-search-center {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
  }
  .partner-center-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    max-width: 500px;
  }
  .search-container {
    position: relative;
    max-width: 400px;
    margin: 0 auto;
  }
  .search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
  }
  .search-icon {
    position: absolute;
    left: 12px;
    color: #6c757d;
    pointer-events: none;
  }
  #partner-search-input {
    width: 100%;
    padding: 5px 36px 5px 44px;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    font-size: 15px;
    background-color: white;
    transition: all 0.2s ease;
    min-width: 300px;
    line-height: 1px;
  }
  #partner-search-input:focus {
    outline: none;
    border-color: #4a90e2;
    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
  }
  .partner-cancel-button {
    position: absolute;
    right: 8px;
    background: none;
    border: none;
    padding: 0;
    margin: 0;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
  }
  .search-results {
    position: absolute;
    top: calc(100% + 8px);
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    max-height: 300px;
    overflow-y: auto;
    display: none;
    z-index: 1000;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  }
  .search-result-item {
    padding: 12px 16px;
    cursor: pointer;
    border-bottom: 1px solid #f1f3f5;
    transition: background-color 0.15s ease;
  }
  .search-result-item:last-child {
    border-bottom: none;
  }
  .search-result-item:hover {
    background-color: #f8f9fa;
  }
  .current-partner {
    display: none;
    align-items: center;
    justify-content: center;
    width: 100%;
  }
  .partner-info {
    display: flex;
    flex-direction: row;
    gap: 4px;
    width: 100%;
    justify-content: center;
  }
  .partner-name {
    font-weight: 500;
    font-size: 15px;
    color: #212529;
  }
  @media (max-width: 768px) {
    .partner-search-center {
      flex-direction: column;
      align-items: stretch;
    }
    .partner-center-content {
      max-width: 100%;
    }
    .search-container {
      width: 100%;
      min-width: 0;
      max-width: 100%;
    }
    .current-partner {
      width: 100%;
    }
  }
</style>
