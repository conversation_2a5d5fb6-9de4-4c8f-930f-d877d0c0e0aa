<script>
  document.addEventListener('DOMContentLoaded', function () {
    const customerTags = {{ customer.tags | json }} || []
    window.customerTags = customerTags

    // Create JS tags
    const partnerUrlScript = document.createElement('script')
    partnerUrlScript.type = 'module'
    partnerUrlScript.src = '{{'partner-url.js' | asset_url}}?v=' + Date.now()
    partnerUrlScript.defer = true
    document.body.appendChild(partnerUrlScript)

  })
</script>

{% schema %}
{
  "name": "Partner URL",
  "target": "section"
}
{% endschema %}
