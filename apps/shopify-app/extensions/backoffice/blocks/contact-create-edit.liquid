<div id="contact-create-edit-page"></div>
<script>
  // Define assets to be loaded
  const assets = {
    css: ['{{'vite-index.css' | asset_url}}'],
    js: ['{{'vite-contact-create-edit.js' | asset_url}}'],
  };

  // Function to update query parameter with current timestamp
  function updateQueryParam(url) {
    const timestamp = Math.floor(Date.now() / 1000);
    try {
      const urlObj = new URL(url);
      urlObj.searchParams.set('v', timestamp);
      return urlObj.toString();
    } catch (e) {
      console.error('Invalid URL:', url);
      return url;
    }
  }

  // Function to create asset tags
  function createAssetTags() {
    // Create CSS tags
    assets.css.forEach((cssFile) => {
      const link = document.createElement('link');
      link.rel = 'stylesheet';
      link.href = updateQueryParam(cssFile);
      document.head.appendChild(link);
    });

    // Create JS tags
    assets.js.forEach((jsFile) => {
      const script = document.createElement('script');
      script.type = 'module';
      script.src = updateQueryParam(jsFile);
      script.defer = true;
      document.body.appendChild(script);
    });
  }

  // Initialize local values
  window.localValues = {
    customerEmail: '{{ customer.email }}',
    customerId: '{{ customer.id }}',
    cart: '{{ cart | json }}',
  };

  // Create asset tags when DOM is loaded
  document.addEventListener('DOMContentLoaded', createAssetTags);
</script>

{% schema %}
{
  "name": "Contact Create Edit Page",
  "target": "section"
}
{% endschema %}
