class CommissionableVolume {
  constructor() {
    this.productData = {}
    this.productForm = document.querySelector('product-form')
  }

  getFormData() {
    const formData = new FormData(this.productForm.form)
    const formObject = {}
    for (const [key, value] of formData.entries()) {
      formObject[key] = value
    }
    return formObject
  }

  async loadData(productId) {
    const response = await fetch(`/a/api/product/${productId}`)
    const data = await response.json()
    if (!data.product) {
      return
    }
    this.productData = data
  }

  renderVolume() {
    const volumeElement = document.getElementById('commissionable-volume-value')
    if (!volumeElement) {
      return
    }

    const formData = this.getFormData()
    const variantId = formData.id
    const variant = this.productData.product.variants.find(
      (v) => v.shopifyId === variantId,
    )
    const volume = variant?.priceBookEntry?.commissionableVolume || 0

    volumeElement.textContent = `${(volume / 100).toFixed(2)}`
  }

  subscribeToProductChanges() {
    this.productForm.form.addEventListener('change', () => this.renderVolume())
  }

  async initAsPartner(productId) {
    await this.loadData(productId)
    this.renderVolume()
    this.subscribeToProductChanges()
  }
}
