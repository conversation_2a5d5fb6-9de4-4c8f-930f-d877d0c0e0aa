export class Cookie {
  static getCookie(name) {
    const value = `; ${document.cookie}`
    const parts = value.split(`; ${name}=`)
    if (parts.length === 2) {
      const cookieValue = parts.pop().split(';').shift()
      try {
        return cookieValue ? JSON.parse(cookieValue) : {}
      } catch (e) {
        return cookieValue || {}
      }
    }
    return {}
  }

  static setCookie(name, value, days) {
    const date = new Date()
    date.setTime(date.getTime() + days * 24 * 60 * 60 * 1000)
    const expires = `expires=${date.toUTCString()}`
    const valueToStore =
      typeof value === 'object' ? JSON.stringify(value) : value
    document.cookie = `${name}=${valueToStore};${expires};path=/`
  }
}
