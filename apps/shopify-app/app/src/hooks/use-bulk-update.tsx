import { useLoaderData, useSubmit } from '@remix-run/react'
import {
  BlockStack,
  Text,
  TextField,
  Thumbnail,
  Checkbox,
} from '@shopify/polaris'
import { ImageIcon } from '@shopify/polaris-icons'
import type { FormEvent } from 'react'
import { useState } from 'react'
import { getProducts } from '../server/modules/product/get-product'

export function useBulkUpdateProducts() {
  const submit = useSubmit()
  const { variants, pageInfo } = useLoaderData<typeof getProducts>()

  const getcommissionableVolume = (args: typeof variants) => {
    const commissionableVolume = args?.reduce(
      (acc, variant) => {
        acc[variant.id] = variant.commissionableVolume?.toString() || ''
        return acc
      },
      {} as Record<string, string>,
    )

    return commissionableVolume || {}
  }

  const [commissionableValue, setCommissionableValue] = useState<
    Record<string, string>
  >(getcommissionableVolume(variants))

  const [isStarterKitValues, setIsStarterKitValues] = useState<
    Record<string, boolean>
  >(
    variants?.reduce(
      (acc, variant) => {
        acc[variant.id] = variant.product.isStarterKit || false
        return acc
      },
      {} as Record<string, boolean>,
    ) || {},
  )

  const handleSubmit = (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault()

    const variantsUpdated = variants?.map((variant) => {
      return {
        ...variant,
        commissionableValue: commissionableValue[variant.id] || null,
        product: {
          ...variant.product,
          isStarterKit: isStarterKitValues[variant.id] || false,
        },
      }
    })

    submit({ data: JSON.stringify(variantsUpdated) }, { method: 'PUT' })
  }

  const handleStarterKitChange = (variantId: string, checked: boolean) => {
    // Find the product ID for the changed variant
    const changedVariant = variants?.find((v) => v.id === variantId)
    if (!changedVariant) return

    // Update all variants of the same product
    setIsStarterKitValues((prev) => {
      const newState = { ...prev }
      variants?.forEach((variant) => {
        if (variant.product.id === changedVariant.product.id) {
          newState[variant.id] = checked
        }
      })
      return newState
    })
  }

  const isHidePagination = !variants?.length

  const handleCommissionableVolume = ({
    variantId,
    value,
  }: {
    variantId: string
    value: string
  }) => {
    setCommissionableValue((prev) => {
      return {
        ...prev,
        [variantId]: value,
      }
    })
  }

  const handlePagination = (direction: 'next' | 'previous') => {
    const cursor =
      direction === 'next' ? pageInfo?.endCursor : pageInfo?.startCursor

    if (!cursor) {
      return
    }
    submit({ cursor, direction }, { method: 'get', replace: true })
  }

  const handleDownload = () => {
    console.log('request')
    submit({}, { method: 'POST' })
  }
  // Prepare rows for DataTable
  const rows = variants?.map((variant) => {
    const image = {
      url:
        variant.image?.url ||
        variant.product.featuredMedia?.preview?.image?.url ||
        '',
      alt:
        variant.image?.altText ||
        variant.product.featuredMedia?.preview?.image?.altText ||
        variant.title,
    }
    return [
      <Thumbnail
        key={`${variant.id}-thumbnail`}
        source={image.url || ImageIcon}
        alt={image.alt}
        size="small"
      />,
      <BlockStack key={`${variant.id}-product-title`} gap="200">
        <Text as="p" variant="bodyMd" fontWeight="bold">
          {variant.product.title}
        </Text>
        <Text as="p" variant="bodySm" tone="subdued">
          {variant.title}
        </Text>
      </BlockStack>,
      <Text key={`${variant.id}-title`} as="p" variant="bodyMd">
        {variant.title}
      </Text>,
      <Text key={`${variant.id}-price`} as="p" variant="bodyMd">
        ${variant.price}
      </Text>,
      <Text key={`${variant.id}-price`} as="p" variant="bodyMd">
        ${variant.compareAtPrice}
      </Text>,
      <Text key={`${variant.id}-sku`} as="p" variant="bodyMd">
        {variant.sku || '-'}
      </Text>,

      <TextField
        key={`${variant.id}-commissionable-value`}
        label="New Price"
        type="currency"
        min={0}
        prefix=""
        autoComplete="off"
        labelHidden
        name={`discount-${variant.id}-price`}
        value={commissionableValue[variant.id] || ''}
        onChange={(value) =>
          handleCommissionableVolume({ variantId: variant.id, value })
        }
      />,
      <Checkbox
        key={`${variant.id}-starter-kit`}
        label="Starter Kit"
        checked={isStarterKitValues[variant.id] || false}
        onChange={(checked) => handleStarterKitChange(variant.id, checked)}
      />,
    ]
  })
  return {
    rows: rows || [],
    handlePagination,
    handleSubmit,
    handleDownload,
    pageInfo,
    isHidePagination,
  }
}
