import { authenticate } from 'app/shopify.server'
import { ShopifyIdConverter } from '../../shopify-ids/shopify-id.path'

export class ProductTagsService {
  static async getProductTags({
    admin,
    productId,
  }: {
    admin: Awaited<ReturnType<typeof authenticate.admin>>['admin']
    productId: string[]
  }) {
    try {
      const query = `#graphql
        query GetProductsByIds($ids: [ID!]!) {
            nodes(ids: $ids) {
                ... on Product {
                id
                tags
                }
            }
        }
        `

      const response = await admin.graphql(query, {
        variables: {
          ids: productId.map((id) => ShopifyIdConverter.product(id)),
        },
      })

      const responseJson = await response.json()
      if (!responseJson.data?.nodes) {
        return []
      }
      return responseJson.data.nodes.reduce(
        (acc: { id: string; tags: string[] }[], node) => {
          if (!node) {
            return acc
          }
          acc.push({
            id: node.id,
            tags: node.tags,
          })
          return acc
        },
        [],
      )
    } catch (error) {
      console.error('Error getting product tags', error)
      return []
    }
  }
}
