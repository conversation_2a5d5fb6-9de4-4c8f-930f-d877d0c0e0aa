import { GetPartnerByPersonalLinkUseCase } from '@emma/storage'
import { isErr } from '@repo/result'
import { json } from '@remix-run/node'
import { authenticatedPartner } from '../decorators/partner-decorator'
import { userUnauthenticated } from '../decorators/user-unauthenticated-decorator'

export const getPartnerByPersonalLink = userUnauthenticated(
  async ({ unauthContext, params }) => {
    const personalLink = params['personal-link']
    if (!personalLink) {
      return json({ error: 'Personal link is not present' }, { status: 400 })
    }

    const useCase = new GetPartnerByPersonalLinkUseCase()
    const userPersonalLink = await useCase.get({
      personalLink,
      shopifyCompanyId: unauthContext.shop,
    })

    if (isErr(userPersonalLink)) {
      return json({ error: 'Customer not found' }, { status: 404 })
    }

    const shopifyPartner = userPersonalLink.data.shopifyPartner
    const companyUser = shopifyPartner?.shopify_partner?.company_user

    return json({
      personalLink: companyUser?.personal_link,
      partnerId: shopifyPartner.shopify_id,
      firstName: companyUser?.user.first_name,
      lastName: companyUser?.user.last_name,
      email: companyUser?.user.email,
    })
  },
)
