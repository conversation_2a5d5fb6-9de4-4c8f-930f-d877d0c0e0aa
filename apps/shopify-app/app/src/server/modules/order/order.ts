import {
  CreateShopifyOrderUseCase,
  DBOrderTypeValueObject,
  GetRandomShopifyCustomerPartnerUseCase,
  GetShopifyPartnerUseCase,
  UpsertShopifyBuyerByPartnerUseCase,
  UpsertShopifyCustomerUseCase,
  FindOrCreateSkuUseCase,
  GetCompanyUserUseCase,
  CreateShopifyOrderItemAdapter,
  prisma,
} from '@emma/storage'
import { OrderWebhookPayload } from './types/webhook-order'
import { isErr } from '@repo/result'
import { MoneyConverter } from 'app/src/utils/money-converter'
import { unauthenticated } from 'app/shopify.server'
import { CustomerShopifyService } from '../customer/shopify/customer'
import { isCustomerPartner } from '../customer/is-customer-partner'
import { ProductTagsService } from '../product/shopify/get-product-tags'

const PARTNER_ID_ATTRIBUTE_NAME = 'partnerId'
const PRODUCT_TAGS_STARTER_KIT = 'starter-kit'

export class OrderService {
  constructor() {}

  async createOrder({
    payload,
    shop,
  }: {
    payload: OrderWebhookPayload
    shop: string
  }) {
    try {
      const partner = await this.getPartner({
        payload,
        shop,
      })

      if (!partner) {
        console.log('No partnerId found')
        return
      }

      const partnerId = partner.shopify_customer.shopify_id
      const buyer = await this.getBuyer({
        payload,
        shop,
        partnerShopifyId: partnerId,
      })

      const isStarterKit = await this.getIsStarterKit({
        payload,
        shop,
      })

      const type = this.getOrderType({
        partnerId: partnerId,
        customerId: buyer?.customerId,
        isCustomerPartner: buyer?.isCustomerPartner,
        isStarterKit,
      })

      // Get company ID for auto-SKU creation
      const getCompanyUserUseCase = new GetCompanyUserUseCase()
      const companyUserResult =
        await getCompanyUserUseCase.getCompanyUserRelations({
          companyUserUuid: partner.company_user.public_uuid,
        })

      if (isErr(companyUserResult)) {
        console.error('Failed to get company user:', companyUserResult.error)
        return
      }

      const { items, shopifyLineItems } = await this.getItems(
        payload,
        companyUserResult.data.company.id,
        shop,
      )

      const useCase = new CreateShopifyOrderUseCase()

      const order = await useCase.createOrder({
        shopifyId: payload.id.toString(),
        orderNumber: payload.number.toString(),
        billingAddress: {
          city: payload.billing_address.city,
          country: payload.billing_address.country,
          countryCode: payload.billing_address.country_code,
          street1: payload.billing_address.address1,
          street2: payload.billing_address.address2 || undefined,
          zipCode: payload.billing_address.zip,
        },
        shippingAddress: {
          city: payload.shipping_address.city,
          country: payload.shipping_address.country,
          countryCode: payload.shipping_address.country_code,
          street1: payload.shipping_address.address1,
          street2: payload.shipping_address.address2 || undefined,
          zipCode: payload.shipping_address.zip,
        },
        companyUserUuid: partner.company_user.public_uuid,
        contactUuid: buyer?.contact.public_uuid,
        currency: payload.currency,
        items,
        shippingCostCents: MoneyConverter.convertPriceToCents(
          payload.shipping_lines[0].price,
        ),
        status: 'Created',
        totalCostCents: MoneyConverter.convertPriceToCents(payload.total_price),
        trackingLink: 'tracking-link',
        type,
      })
      if (isErr(order)) {
        console.log('Order creation failed', order.error)
        return
      }

      // Create ShopifyOrderItems after successful order creation
      if (shopifyLineItems.length > 0) {
        try {
          const shopifyOrderId = order.data.shopify_order?.id
          if (shopifyOrderId) {
            const createShopifyOrderItemAdapter =
              new CreateShopifyOrderItemAdapter(prisma)

            const shopifyOrderItemInputs = shopifyLineItems.map(
              ({ lineItem, skuUuid }) => ({
                shopifyLineItemId: lineItem.id.toString(),
                shopifyProductId: lineItem.product_id.toString(),
                shopifyVariantId: lineItem.variant_id?.toString(),
                quantity: lineItem.quantity,
                priceCents: MoneyConverter.convertPriceToCents(lineItem.price),
                totalDiscountCents: MoneyConverter.convertPriceToCents(
                  lineItem.total_discount || '0',
                ),
                productTitle: lineItem.name,
                variantTitle: lineItem.variant_title,
                shopifyRawData: lineItem,
                shopifyOrderId,
                skuUuid,
              }),
            )

            await createShopifyOrderItemAdapter.createMultipleShopifyOrderItems(
              shopifyOrderItemInputs,
            )
            console.log(
              `Created ${shopifyOrderItemInputs.length} ShopifyOrderItems`,
            )
          }
        } catch (error) {
          console.error('Failed to create ShopifyOrderItems:', error)
          // Don't fail the whole order creation for this
        }
      }

      if (isStarterKit) {
        await this.handleUpdateCustomerToConsultant({
          customerId: buyer?.customerId,
          shop,
        })
      }
      console.log('Order created', order.data.public_uuid)
    } catch (error) {
      console.log(error)
      console.error('Unexpected error:' + payload.id, error)
    }
  }

  private async getPartner({
    payload,
    shop,
  }: {
    payload: OrderWebhookPayload
    shop: string
  }) {
    const orderPartnerId = payload.note_attributes.find(
      (attribute) => attribute.name === PARTNER_ID_ATTRIBUTE_NAME,
    )?.value

    if (!orderPartnerId) {
      console.log('Go through random company user')
      const randomCompanyUserUseCase =
        new GetRandomShopifyCustomerPartnerUseCase()
      const randomCompanyUserResult = await randomCompanyUserUseCase.get({
        shopifyCompanyId: shop,
      })

      if (isErr(randomCompanyUserResult)) {
        return
      }

      return randomCompanyUserResult.data.shopifyPartner
    }
    const getShopifyPartnerUseCase = new GetShopifyPartnerUseCase()
    const shopifyPartnerResult = await getShopifyPartnerUseCase.get({
      shopifyId: orderPartnerId,
      shopifyCompanyId: shop,
    })
    if (isErr(shopifyPartnerResult)) {
      return
    }

    return shopifyPartnerResult.data.shopifyPartner
  }

  private async getBuyer({
    payload,
    shop,
    partnerShopifyId,
  }: {
    payload: OrderWebhookPayload
    shop: string
    partnerShopifyId: string
  }) {
    const shopifyId = payload.customer.id.toString()
    const isPartner = await isCustomerPartner({
      shopifyId: payload.customer.id.toString(),
      store: shop,
    })

    const upsertShopifyCustomerUseCase =
      new UpsertShopifyBuyerByPartnerUseCase()
    const shopifyCustomerResult = await upsertShopifyCustomerUseCase.upsert({
      shopifyId: payload.customer.id.toString(),
      partnerShopifyId,
      email: payload.customer.email,
      firstName: payload.customer.first_name,
      lastName: payload.customer.last_name,
      phoneCountryCode: '',
      phoneNumber: '',
    })
    if (isErr(shopifyCustomerResult)) {
      return
    }

    return {
      customerId: shopifyId,
      isCustomerPartner: isPartner,
      contact: shopifyCustomerResult.data.contact,
    }
  }

  private getOrderType({
    partnerId,
    customerId,
    isCustomerPartner,
    isStarterKit,
  }: {
    partnerId?: string
    customerId?: string
    isCustomerPartner?: boolean
    isStarterKit?: boolean
  }): DBOrderTypeValueObject {
    if (isStarterKit) {
      return 'starter-kit'
    }

    if (!partnerId && isCustomerPartner) {
      return 'personal'
    }

    if (partnerId === customerId) {
      return 'personal'
    }

    return 'customer'
  }

  private async getItems(
    payload: OrderWebhookPayload,
    companyId: bigint,
    shop: string,
  ) {
    const findOrCreateSkuUseCase = new FindOrCreateSkuUseCase()
    const items: Array<{ priceCents: number; skuId: string }> = []
    const shopifyLineItems: Array<{
      lineItem: any
      skuUuid: string
    }> = []

    for (const lineItem of payload.line_items) {
      // Fetch product image from Shopify
      const imageUrl = await this.getProductImage(
        lineItem.product_id.toString(),
        shop,
      )

      // Auto-create or find existing SKU for this Shopify product
      const skuResult = await findOrCreateSkuUseCase.execute({
        shopifyProductId: lineItem.product_id.toString(),
        shopifyVariantId: lineItem.variant_id?.toString(),
        productTitle: lineItem.name,
        variantTitle: lineItem.variant_title || undefined,
        priceCents: MoneyConverter.convertPriceToCents(lineItem.price),
        companyId,
        imageUrl,
      })

      if (isErr(skuResult)) {
        console.error(
          'Failed to create/find SKU for line item:',
          lineItem.id,
          skuResult.error,
        )
        continue
      }

      const sku = skuResult.data

      // Store Shopify line item data for ShopifyOrderItem creation
      shopifyLineItems.push({
        lineItem,
        skuUuid: sku.publicUuid,
      })

      // Create one OrderItem per quantity for commission calculation
      for (let i = 0; i < lineItem.quantity; i++) {
        items.push({
          priceCents: MoneyConverter.convertPriceToCents(lineItem.price),
          skuId: sku.id.toString(),
        })
      }
    }

    return { items, shopifyLineItems }
  }

  private async getProductImage(
    productId: string,
    shop: string,
  ): Promise<string | undefined> {
    try {
      const { admin } = await unauthenticated.admin(shop)

      // Fetch product with images using GraphQL API
      const query = `#graphql
        query GetProductImages($id: ID!) {
          product(id: $id) {
            id
            images(first: 1) {
              edges {
                node {
                  id
                  url
                }
              }
            }
          }
        }
      `

      const response = await admin.graphql(query, {
        variables: {
          id: `gid://shopify/Product/${productId}`,
        },
      })

      const responseJson = await response.json()
      const product = responseJson.data?.product
      const images = product?.images?.edges || []

      console.log(`Product ${productId} images:`, images.length)

      // Return the first image URL if available
      if (images.length > 0) {
        const imageUrl = images[0].node.url
        console.log(`Product ${productId} image URL:`, imageUrl)
        return imageUrl
      }

      console.log(`No images found for product ${productId}`)
      return undefined
    } catch (error) {
      console.error(
        `Failed to fetch product image for product ${productId}:`,
        error,
      )
      return undefined
    }
  }

  private async getIsStarterKit({
    payload,
    shop,
  }: {
    payload: OrderWebhookPayload
    shop: string
  }) {
    const { admin } = await unauthenticated.admin(shop || '')
    const itemsTags = await ProductTagsService.getProductTags({
      admin,
      productId: payload.line_items.map((item) => item.product_id.toString()),
    })
    return itemsTags.some((item) =>
      item.tags.includes(PRODUCT_TAGS_STARTER_KIT),
    )
  }

  private async handleUpdateCustomerToConsultant({
    customerId,
    shop,
  }: {
    customerId?: string
    shop: string
  }) {
    if (!customerId) {
      return
    }
    const { admin } = await unauthenticated.admin(shop || '')
    await CustomerShopifyService.updateCustomerToPartner({
      admin,
      customerId,
    })
  }
}
