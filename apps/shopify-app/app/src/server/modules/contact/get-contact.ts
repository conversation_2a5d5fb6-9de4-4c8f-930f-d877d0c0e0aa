import { GetContactUseCase } from '@emma/storage'
import { authenticatedPartner } from '../decorators/partner-decorator'
import { isErr } from '@repo/result'
import { json } from '@remix-run/node'

export const getContact = authenticatedPartner(
  async ({ params, authContext }) => {
    if (!params.id) {
      return json({ error: 'Contact ID is required' }, { status: 400 })
    }

    const useCase = new GetContactUseCase()

    const contact = await useCase.getContactByPublicUuid({
      publicUuid: params.id,
      companyUserUuid: authContext.partner.companyUserUuid,
    })

    if (isErr(contact)) {
      return json({ error: 'Contact not found' }, { status: 404 })
    }

    const orders = contact.data.orders?.map((order) => ({
      number: order.public_uuid,
      id: order.public_uuid,
      type: order.type,
      createdAt: order.created_at,
      updatedAt: order.updated_at,
      name: order.public_uuid,
      totalPrice: order.total_cost_cents,
      currency: order.price_currency,
      financialStatus: order.status,
      orderNumber: order.order_number,
      partner: {
        firstName: contact.data.contact.first_name,
        lastName: contact.data.contact.last_name,
        email: contact.data.contact.email,
      },
      buyer: {
        firstName: contact.data.contact.first_name,
        lastName: contact.data.contact.last_name,
        email: contact.data.contact.email,
      },
    }))

    return json({
      id: contact.data.contact.public_uuid,
      firstName: contact.data.contact.first_name,
      lastName: contact.data.contact.last_name,
      email: contact.data.contact.email,
      birthday: contact.data.contact.birthday,
      createdAt: contact.data.contact.created_at,
      orders,
    })
  },
)
