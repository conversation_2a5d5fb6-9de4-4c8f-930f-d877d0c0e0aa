import { UpsertContactUseCase } from '@emma/storage'
import { authenticatedPartner } from '../decorators/partner-decorator'
import { isErr } from '@repo/result'
import { json } from '@remix-run/node'

export const updateContact = authenticatedPartner(
  async ({ params, request, authContext }) => {
    const body = await request.json()
    const useCase = new UpsertContactUseCase()
    const updateContact = await useCase.handle({
      publicUuid: params.id,
      firstName: body.firstName,
      lastName: body.lastName,
      companyUserUuid: authContext.partner.companyUserUuid,
      email: body.email,
      phone: body.phone,
      address: body.address.country
        ? {
            street1: body.address.address1,
            street2: body.address.address2,
            city: body.address.city,
            country: body.address.country,
            countryCode: body.address.country,
            postalCode: body.address.postalCode,
            stateCode: body.address.stateCode,
          }
        : undefined,
    })

    if (isErr(updateContact)) {
      return json({ error: updateContact.error }, { status: 400 })
    }

    return json({
      birthday: updateContact.data.contact.birthday,
      email: updateContact.data.contact.email,
      firstName: updateContact.data.contact.first_name,
      id: updateContact.data.contact.public_uuid,
      lastName: updateContact.data.contact.last_name,
      referralLink: updateContact.data.contact.referral_link,
      type: updateContact.data.contact.type,
    })
  },
)
