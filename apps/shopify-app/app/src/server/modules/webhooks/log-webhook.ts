import { writeFile, mkdir } from 'fs/promises'
import { join } from 'path'

export const logWebhook = async (topic: string, payload: any) => {
  const webhooksDir = join(process.cwd(), 'logs', 'webhooks')
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
  const filename = `${topic}-${timestamp}.json`

  try {
    // Create directory if it doesn't exist
    await mkdir(webhooksDir, { recursive: true })

    await writeFile(
      join(webhooksDir, filename),
      JSON.stringify({ topic, payload }, null, 2),
    )
    console.log(`Webhook data saved to ${filename}`)
  } catch (error) {
    console.error('Error saving webhook data:', error)
  }
}
