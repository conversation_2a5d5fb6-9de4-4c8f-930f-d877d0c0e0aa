import { GetShopifyCompanyUseCase } from '@emma/storage'
import { isErr } from '@repo/result'

export async function getCompany({ shop }: { shop: string }) {
  const getShopifyCompanyUseCase = new GetShopifyCompanyUseCase()
  const result = await getShopifyCompanyUseCase.get({
    shopifyCompanyId: shop,
  })

  if (isErr(result)) {
    console.error('Error processing customer update:', result.error)
    return
  }

  if (!result.data.shopifyCompany) {
    console.error('Error processing customer update: company not found')
    return
  }
  const shopifyCompany = result.data.shopifyCompany
  return shopifyCompany
}
