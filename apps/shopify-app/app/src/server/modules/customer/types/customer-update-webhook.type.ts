export type CustomerAddress = {
  id: number
  customer_id: number
  first_name: string
  last_name: string
  company: string | null
  address1: string | null
  address2: string | null
  city: string | null
  province: string | null
  country: string
  zip: string | null
  phone: string | null
  name: string
  province_code: string | null
  country_code: string
  country_name: string
  default: boolean
}

export type CustomerUpdateWebhookPayload = {
  id: number
  email: string
  created_at: string
  updated_at: string
  first_name: string
  last_name: string
  state: string
  note: string
  verified_email: boolean
  multipass_identifier: string | null
  tax_exempt: boolean
  currency: string
  phone: string | null
  addresses: CustomerAddress[]
  tax_exemptions: any[]
  admin_graphql_api_id: string
  default_address: CustomerAddress
}
