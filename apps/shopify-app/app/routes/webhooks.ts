import type { ActionFunctionArgs } from '@remix-run/node'
import { authenticate } from '../shopify.server'
import { logWebhook } from 'app/src/server/modules/webhooks/log-webhook'
import { ShopifyCustomer } from 'app/src/server/modules/customer/shopify-customer'
import { CustomerCreateWebhookPayload } from 'app/src/server/modules/customer/types/customer-create-webhook.type'
import { OrderService } from 'app/src/server/modules/order/order'
import { OrderWebhookPayload } from 'app/src/server/modules/order/types/webhook-order'

export const action = async ({ request }: ActionFunctionArgs) => {
  try {
    console.log('webhook received')
    const { topic, payload, shop } = await authenticate.webhook(request)

    if (process.env.LOG_WEB_HOOKS) {
      await logWebhook(topic, payload)
    }

    const shopifyCustomer = new ShopifyCustomer()
    switch (topic) {
      case 'ORDERS_CREATE':
        const orderService = new OrderService()
        orderService.createOrder({
          payload: payload as OrderWebhookPayload,
          shop,
        })
        break

      case 'CUSTOMERS_CREATE':
      case 'CUSTOMERS_UPDATE':
        await shopifyCustomer.upsertShopifyCustomer({
          payload: payload as CustomerCreateWebhookPayload,
          shop,
        })
        break

      default:
        break
    }

    return new Response()
  } catch (error) {
    console.log(error)
    return new Response()
  }
}
