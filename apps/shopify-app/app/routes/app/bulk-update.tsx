import type { LoaderFunctionArgs, ActionFunctionArgs } from '@remix-run/node'
import { Form, json } from '@remix-run/react'
import {
  Page,
  Layout,
  BlockStack,
  InlineStack,
  Button,
  Card,
  DataTable,
  Pagination,
} from '@shopify/polaris'

import { useBulkUpdateProducts } from 'app/src/hooks/use-bulk-update'
import { getProducts } from 'app/src/server/modules/product/get-product'
// import { submitUpdateProducts } from 'app/src/server/modules/product/submit-bulk-update-products'

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const data = await getProducts(request)
  return json(data)
}

export const action = async (args: ActionFunctionArgs) => {
  // const results = await submitUpdateProducts(args)
  // return json(results)
}

export default function BulkDiscounts() {
  const {
    handlePagination,
    handleSubmit,
    handleDownload,
    rows,
    pageInfo,
    isHidePagination,
  } = useBulkUpdateProducts()

  return (
    <Page fullWidth>
      <ui-title-bar title="Bulk Discounts" />
      <Layout>
        <Layout.Section>
          <BlockStack gap="500">
            <Form method="post" onSubmit={handleSubmit}>
              <InlineStack gap="400">
                <Button submit variant="primary">
                  Update Prices
                </Button>
                <div onClick={() => console.log('clicked')}>hello</div>
                <Button
                  onClick={() => {
                    console.log('click')
                    handleDownload()
                  }}
                >
                  Download prices!!
                </Button>
              </InlineStack>

              <Card>
                <DataTable
                  columnContentTypes={[
                    'text',
                    'text',
                    'text',
                    'text',
                    'text',
                    'text',
                    'text',
                  ]}
                  headings={[
                    '',
                    'Product',
                    'Variant',
                    'Price',
                    'Compare At Price',
                    'SKU',
                    'Commissionable Value',
                    'Starter kit',
                  ]}
                  rows={rows}
                />
                {!isHidePagination && (
                  <div style={{ padding: '1rem' }}>
                    <Pagination
                      hasPrevious={pageInfo?.hasPreviousPage}
                      hasNext={pageInfo?.hasNextPage}
                      onPrevious={() => handlePagination('previous')}
                      onNext={() => handlePagination('next')}
                    />
                  </div>
                )}
              </Card>
            </Form>
          </BlockStack>
        </Layout.Section>
      </Layout>
    </Page>
  )
}
