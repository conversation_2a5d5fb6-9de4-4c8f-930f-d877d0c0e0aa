import path from 'path'
import fs from 'fs'
import { route } from '@remix-run/route-config'
import type { RouteConfig, RouteConfigEntry } from '@remix-run/route-config'
import { flatRoutes } from '@remix-run/fs-routes'

function generateApiRoutes(): RouteConfigEntry[] {
  function readDirectory(
    dir: string,
    basePath: string = '',
  ): RouteConfigEntry[] {
    const entries = fs.readdirSync(dir, { withFileTypes: true })
    const routes: RouteConfigEntry[] = []

    for (const entry of entries) {
      const fullPath = path.join(dir, entry.name)
      const relativePath = path.join(basePath, entry.name)

      if (entry.isDirectory()) {
        // Recursively read subdirectories
        routes.push(...readDirectory(fullPath, relativePath))
      } else if (entry.isFile() && entry.name.endsWith('.ts')) {
        // Convert filename to route path
        let routePath = relativePath
          .replace(/\.ts$/, '') // Remove .ts extension
          .replace(/\$(\w+)/g, ':$1') // Convert $param to :param
          .replace(/\\/g, '/') // Convert Windows backslashes to forward slashes
          .replace(/\./g, '/') // Convert dots to forward slashes

        // Add /api prefix
        routePath = `/api/${routePath}`

        // Create the route definition
        routes.push(route(routePath, `routes/api/${relativePath}`))
      }
    }

    return routes
  }

  const apiDir = path.join(__dirname, 'routes/api')
  return readDirectory(apiDir)
}

// Generate API routes

const routes = [
  // API routes
  ...generateApiRoutes(),

  // App routes (nested)
  route('/app', 'routes/app.tsx', [
    route('', 'routes/app._index.tsx', {
      index: true,
    }),
    route('bulk-update', 'routes/app/bulk-update.tsx'),
    route('customers', 'routes/app/customers.tsx'),
  ]),
] satisfies RouteConfig

// Get flat routes and filter out the app route since we're defining it manually
const flatRoutesResult = await flatRoutes()
const filteredFlatRoutes = flatRoutesResult.filter(
  (route) => route.id !== 'routes/app',
)

// Export all routes
export default [...routes, ...filteredFlatRoutes] as RouteConfig
