# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "dca54fc737bceec50b1b4b7eb6addd05"
name = "Emma HQ: Direct Sales OS"
handle = "emma-hq-direct-sales-os"
application_url = "https://shopify.getemma.io/"
embedded = true

[build]
include_config_on_deploy = true

[webhooks]
api_version = "2025-04"

  [[webhooks.subscriptions]]
  topics = [ "customers/create", "customers/update", "orders/create" ]
  uri = "/webhooks"

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "read_customers,read_orders,read_price_rules,read_themes,write_customers,write_discounts,write_draft_orders,write_orders,write_price_rules,write_products,write_themes"

[auth]
redirect_urls = [
  "https://shopify.getemma.io/auth/callback",
  "https://shopify.getemma.io/auth/shopify/callback",
  "https://shopify.getemma.io/api/auth/callback"
]

[app_proxy]
url = "https://shopify.getemma.io/api/"
subpath = "api"
prefix = "a"

[pos]
embedded = false

[[extensions]]
name = "backoffice"
id = "1089945337857"