import { env } from '@dotenv-run/core'
import { configDefaults, defineConfig, mergeConfig } from 'vitest/config'
import tsconfigPaths from 'vite-tsconfig-paths'

env({
  root: '../..',
  verbose: true,
})

const config = mergeConfig(
  {}, // Extending from an existing Vite configuration (`vite.config.ts` file)
  // @ts-ignore
  defineConfig({
    test: {
      ...configDefaults, // Extending Vitest's default options
      setupFiles: ['./db-test.setup.ts'],
      hookTimeout: 50000, // TODO: update seed flow to increase speed time
      include: ['**/*.db-test.ts', '**/*.db-spec.ts'],
    },
    // @ts-ignore
    plugins: [tsconfigPaths()],
  }),
)

export default config
