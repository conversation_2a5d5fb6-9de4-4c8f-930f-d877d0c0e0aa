import { resultErr, resultOk } from '@repo/result'
import { createInterface } from 'readline'

import type { Result } from '@repo/result'

const rl = createInterface({
  input: process.stdin,
  output: process.stdout,
})

const stageEnv = 'app-fe1a3211-a0fe-4c4b-aa7d'
const prodEnv = 'app-7d068561-b6fe-4eac'

function getEnvName(path: string): string {
  if (path.includes(prodEnv)) return 'PRODUCTION!'
  if (path.includes(stageEnv)) return 'Stage'
  return 'Local (dev)'
}

export async function ask(dbPath: string) {
  const envName = getEnvName(dbPath)

  return new Promise<Result<'ok', 'cancelled'>>((resolve) => {
    rl.question(
      `Do you want to proceed with _${envName}_ env? (y/N):`,
      (answer) => {
        // Close the readline interface
        rl.close()
        const processAnswer = answer.trim().toLowerCase()

        if (processAnswer === 'y' || processAnswer === 'yes') {
          resolve(resultOk('ok'))
          return
        }
        resolve(resultErr('cancelled'))
      },
    )
  })
}
