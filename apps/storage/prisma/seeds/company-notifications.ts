import { fillAllCompaniesWithSystemNotifications } from './notifycations/fill-all-companies-with-system-notifications'

import type { PrismaClient } from '@prisma/client'

export const seedCompanyNotifications = async (prisma: PrismaClient) => {
  await prisma.$queryRaw`TRUNCATE "company_notifications" CASCADE`
  await prisma.$queryRaw`ALTER SEQUENCE "company_notifications_id_seq" RESTART WITH 1`

  await fillAllCompaniesWithSystemNotifications(prisma)
}
