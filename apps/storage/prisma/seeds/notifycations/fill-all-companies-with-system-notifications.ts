import { FillCompanyNotificationsAdapter } from '~/modules/notification-management/infra/fill-company-notifications.adapter'

import type { PrismaClient } from '@prisma/client'

export const fillAllCompaniesWithSystemNotifications = async (
  prisma: PrismaClient,
) => {
  const companies = await prisma.company.findMany({
    where: {
      archived_at: null,
    },
    select: {
      id: true,
      public_uuid: true,
    },
  })

  const adapter = new FillCompanyNotificationsAdapter(prisma)

  console.log('filling companies: ', companies.length)

  await companies.reduce(async (promise, company) => {
    await promise
    await adapter.fillNotifications({ companyUuid: company.public_uuid })
    return Promise.resolve()
  }, Promise.resolve())
}
