import { faker } from '@faker-js/faker'
import {
  formatDuration,
  intervalToDuration,
  subMonths,
  subWeeks,
} from '@repo/date'

import { createCompanyUserNotification } from '~/modules/notify-prefill/interface/create-company-user-notification'

import { compileNotifyMeta } from './compiles/compile-notify-meta'

import type {
  CompanyUserNotification,
  Prisma,
  PrismaClient,
} from '@prisma/client'
import type {
  DBNotificationAllSubtypeValueObject,
  DBNotificationSubjectValueObject,
} from '~/modules/notification'

type GeneratorArgs = {
  contacts: Record<string, Prisma.ContactGetPayload<{}>[]>
  orders: Record<string, Prisma.OrderGetPayload<{}>[]>
  company_user: Prisma.CompanyUserGetPayload<{
    include: {
      hierarchy_descendants: {
        include: {
          descendant: true
        }
      }
    }
  }>
  company_notification: Prisma.CompanyNotificationGetPayload<{}>
  subtype?: DBNotificationAllSubtypeValueObject
}

const generateNotification = async ({
  contacts,
  orders,
  company_user,
  company_notification,
}: GeneratorArgs) => {
  const scheduled_on =
    faker.helpers.maybe(() => faker.date.recent(), { probability: 0.9 }) ||
    faker.helpers.maybe(() => faker.date.soon(), { probability: 0.1 }) ||
    faker.helpers.arrayElement([faker.date.recent(), faker.date.soon()])
  const subject: DBNotificationSubjectValueObject =
    company_notification.subject ?? faker.helpers.arrayElement(['Team'])

  const {
    html: content,
    title,
    pushTitle,
    pushDesc,
  } = company_notification.initial_content

  const contact = faker.helpers.arrayElement(
    contacts[company_user.id.toString()],
  )

  // Add safety check for orders
  const contactOrders = orders[contact.id.toString()]
  if (!contactOrders?.length) {
    return null // Skip notification if no orders exist for this contact
  }

  const order = faker.helpers.arrayElement(contactOrders)

  const variables = {
    firstName: contact.first_name,
    lastName: contact.last_name,
    fullName: `${contact.first_name} ${contact.last_name}`,
    status: 'Active',
    orderAge: formatDuration(
      intervalToDuration({
        start: faker.helpers.arrayElement([
          subWeeks(new Date(), 2),
          subMonths(new Date(), 2),
          subMonths(new Date(), 6),
        ]),
        end: new Date(),
      }),
      {
        format: ['days', 'weeks', 'months'],
      },
    ),
    orderTotal: (order.total_cost_cents / 100).toFixed(0),
    // Random number between 2 and 5
    number: Math.floor(Math.random() * 4 + 2),
    orderStatus: order.status,
  }

  const subtype = company_notification.subtype

  // Get hierarchy descendants and extract teammate/recruit IDs if available
  const hierarchyTeammate = company_user.hierarchy_descendants.find(
    (d) => d.depth === 1,
  )
  const hierarchyRecruit = company_user.hierarchy_descendants.find(
    (d) => d.depth === 2,
  )

  const teammateUuid = hierarchyTeammate?.descendant.public_uuid
  const recruitUuid = hierarchyRecruit?.descendant.public_uuid

  // Return null if required IDs are missing for certain subtypes
  if (
    subtype === 'TeamNewRecruit' &&
    (!recruitUuid || !teammateUuid || !order?.public_uuid)
  ) {
    return null
  }

  if (subtype === 'TeamStatusChange' && !teammateUuid) {
    return null
  }

  if (subject === 'Order' && !order.public_uuid) {
    return null
  }

  const meta = compileNotifyMeta({
    subject,
    subtype,
    contactUuid: contact.public_uuid,
    orderUuid: order.public_uuid,
    teammateUuid: hierarchyTeammate?.descendant.public_uuid,
    recruitUuid: hierarchyRecruit?.descendant.public_uuid,
  })

  if (!meta) return null

  const sentAt =
    scheduled_on.getTime() / 1000 > Date.now() / 1000 ? undefined : scheduled_on

  const res: Omit<
    CompanyUserNotification,
    'id' | 'public_uuid' | 'created_at' | 'updated_at' | 'archived_at'
  > = {
    ...createCompanyUserNotification({
      companyUserId: company_user.id,
      companyNotificationId: company_notification.id,
      initialContent: {
        html: content ?? faker.lorem.sentence({ min: 3, max: 7 }),
        title: pushTitle ?? faker.lorem.sentence({ min: 3, max: 5 }),
        pushTitle: pushTitle ?? faker.lorem.sentence({ min: 3, max: 5 }),
        pushDesc: pushDesc ?? faker.lorem.sentence({ min: 3, max: 7 }),
      },
      variables,
      meta,
      sentAt,
    }),
    is_read: sentAt ? faker.datatype.boolean() : false,
    scheduled_on,
  }

  return res
}

// Function specifically for customer notifications
const generateCustomerNotification = async ({
  contacts,
  orders,
  company_user,
  company_notification,
}: GeneratorArgs) => {
  const scheduled_on =
    faker.helpers.maybe(() => faker.date.recent(), { probability: 0.9 }) ||
    faker.helpers.maybe(() => faker.date.soon(), { probability: 0.1 }) ||
    faker.helpers.arrayElement([faker.date.recent(), faker.date.soon()])

  const subject: DBNotificationSubjectValueObject = 'Customer'
  const subtype = company_notification.subtype

  const contact = faker.helpers.arrayElement(
    contacts[company_user.id.toString()],
  )

  if (!contact) {
    return null // Skip if no contacts exist for this company user
  }

  // Add safety check for orders
  const contactOrders = orders[contact.id.toString()]
  if (!contactOrders?.length) {
    return null // Skip notification if no orders exist for this contact
  }

  const order = faker.helpers.arrayElement(contactOrders)

  // Use the contact's information for variables
  const variables = {
    firstName: contact.first_name,
    lastName: contact.last_name,
    fullName: `${contact.first_name} ${contact.last_name}`,
    contactUuid: contact.public_uuid,
  }

  const meta = compileNotifyMeta({
    subject,
    subtype,
    contactUuid: contact.public_uuid,
    orderUuid: order.public_uuid,
    teammateUuid: '',
    recruitUuid: '',
  })

  if (!meta) return null

  const sentAt =
    scheduled_on.getTime() / 1000 > Date.now() / 1000 ? undefined : scheduled_on

  return {
    ...createCompanyUserNotification({
      companyUserId: company_user.id,
      companyNotificationId: company_notification.id,
      initialContent: company_notification.initial_content,
      variables,
      meta,
      sentAt,
    }),
    is_read: sentAt ? faker.datatype.boolean() : false,
    scheduled_on,
  }
}

// Add this new function for team notifications
const generateTeamNotification = async ({
  company_user,
  company_notification,
  orders,
  userMap,
}: {
  company_user: Prisma.CompanyUserGetPayload<{
    include: {
      hierarchy_descendants: {
        include: {
          descendant: true
        }
      }
    }
  }>
  company_notification: Prisma.CompanyNotificationGetPayload<{}>
  orders: Record<string, Prisma.OrderGetPayload<{}>[]>
  userMap: Record<string, { id: bigint; first_name: string; last_name: string }>
}) => {
  const scheduled_on =
    faker.helpers.maybe(() => faker.date.recent(), { probability: 0.9 }) ||
    faker.helpers.maybe(() => faker.date.soon(), { probability: 0.1 }) ||
    faker.helpers.arrayElement([faker.date.recent(), faker.date.soon()])

  const subject: DBNotificationSubjectValueObject = 'Team'
  const subtype = company_notification.subtype

  // Skip if no hierarchy descendants (teammates)
  if (company_user.hierarchy_descendants.length === 0) return null

  // Get hierarchy descendants and extract teammate/recruit IDs
  const hierarchyTeammate = company_user.hierarchy_descendants.find(
    (d) => d.depth === 1,
  )
  const hierarchyRecruit = company_user.hierarchy_descendants.find(
    (d) => d.depth === 2,
  )

  if (!hierarchyTeammate?.descendant) return null

  // Get the teammate user data
  const teammate = hierarchyTeammate.descendant
  const teammateUuid = teammate.public_uuid
  const recruitUuid = hierarchyRecruit?.descendant?.public_uuid

  // Return null if required IDs are missing for certain subtypes
  if (subtype === 'TeamNewRecruit' && (!recruitUuid || !teammateUuid)) {
    return null
  }

  if (subtype === 'TeamStatusChange' && !teammateUuid) {
    return null
  }

  // Find any order for meta data - not using contact info
  let orderUuid = null
  if (company_user.id) {
    // Find any available order for this company user
    const allCompanyUserOrders = Object.values(orders).flat()
    const anyOrder = allCompanyUserOrders.find(
      (order) => order.company_user_id === company_user.id,
    )
    if (anyOrder) {
      orderUuid = anyOrder.public_uuid
    }
  }

  // For types that require order data
  if (subtype === 'TeamNewRecruit' && !orderUuid) {
    return null
  }

  // Generate teammate name from user data
  const userInfo = teammate.user_id
    ? userMap[teammate.user_id.toString()]
    : null

  // Use the teammate name instead of contact name
  const variables = {
    firstName: userInfo?.first_name || faker.person.firstName(),
    lastName: userInfo?.last_name || faker.person.lastName(),
    fullName: userInfo
      ? `${userInfo.first_name} ${userInfo.last_name}`
      : faker.person.fullName(),
    status: 'Active',
    teammateUuid,
    recruitUuid,
    // Add any other variables needed for team notifications
  }

  const meta = compileNotifyMeta({
    subject,
    subtype,
    contactUuid: '', // Not using contact data for team notifications
    orderUuid: orderUuid || '',
    teammateUuid,
    recruitUuid,
  })

  if (!meta) return null

  const sentAt =
    scheduled_on.getTime() / 1000 > Date.now() / 1000 ? undefined : scheduled_on

  return {
    ...createCompanyUserNotification({
      companyUserId: company_user.id,
      companyNotificationId: company_notification.id,
      initialContent: company_notification.initial_content,
      variables,
      meta,
      sentAt,
    }),
    is_read: sentAt ? faker.datatype.boolean() : false,
    scheduled_on,
  }
}

const reduceIdsListBy = <L extends any[], K>(list: L, keyField: K) =>
  list.reduce(
    (result, current) => {
      const key = current[keyField].toString()

      if (!Array.isArray(result[key])) {
        result[key] = []
      }

      result[key].push(current)

      return result
    },
    {} as Record<string, unknown[]>,
  )

type SeederArgs = {
  quantity: number
}

export const seedNotifications = async (
  prisma: PrismaClient,
  { quantity }: SeederArgs,
) => {
  await prisma.$queryRaw`TRUNCATE "company_user_notifications" CASCADE`
  await prisma.$queryRaw`ALTER SEQUENCE "company_user_notifications_id_seq" RESTART WITH 1`

  await prisma.$queryRaw`TRUNCATE "company_user_notification_receiver" CASCADE`
  await prisma.$queryRaw`ALTER SEQUENCE "company_user_notification_receiver_id_seq" RESTART WITH 1`

  const companyUsers = await prisma.companyUser.findMany({
    where: {
      roles: {
        hasSome: ['member', 'admin'],
      },
      user: {
        is_root_admin: false,
      },
      contacts: {
        some: {
          type: 'Customer',
        },
      },
    },
    include: {
      hierarchy_descendants: {
        include: {
          descendant: true,
        },
      },
    },
  })
  const company_user_ids = companyUsers.map(({ id }) => id)

  // Get users data for teammate information
  const users = await prisma.user.findMany({
    where: {
      company_users: {
        some: {
          id: { in: company_user_ids },
        },
      },
    },
    select: {
      id: true,
      first_name: true,
      last_name: true,
    },
  })

  const userMap = users.reduce(
    (acc, user) => {
      acc[user.id.toString()] = user
      return acc
    },
    {} as Record<string, { id: bigint; first_name: string; last_name: string }>,
  )

  const [contacts, orders, companyNotifications] = await Promise.all([
    prisma.contact.findMany({
      where: {
        type: 'Customer',
        company_user_id: {
          in: company_user_ids,
        },
        subscriptions: {
          none: {},
        },
      },
      select: {
        first_name: true,
        last_name: true,
        id: true,
        public_uuid: true,
        company_user_id: true,
      },
    }),
    prisma.order.findMany({
      where: {
        company_user_id: {
          in: company_user_ids,
        },
      },
      select: {
        contact_id: true,
        created_at: true,
        public_uuid: true,
        total_cost_cents: true,
        company_user_id: true,
      },
    }),
    prisma.companyNotification.findMany({
      where: {
        archived_at: null,
        company_id: {
          in: companyUsers.map(({ company_id }) => company_id),
        },
        // Only include non-Subscription, non-Team, and non-Customer notifications
        subject: {
          not: {
            in: ['Subscription', 'Team', 'Customer'],
          },
        },
      },
    }),
  ])
  const contactsByCompanyUserId = reduceIdsListBy<
    typeof contacts,
    keyof (typeof contacts)[0]
  >(contacts, 'company_user_id')

  const ordersByContactId = reduceIdsListBy<
    typeof orders,
    keyof (typeof orders)[0]
  >(orders, 'contact_id')

  // Get team notifications separately to handle them with special teammate flow
  const teamNotifications = await prisma.companyNotification.findMany({
    where: {
      archived_at: null,
      company_id: {
        in: companyUsers.map(({ company_id }) => company_id),
      },
      subject: 'Team',
    },
  })

  // Get customer notifications separately to handle them with special contact flow
  const customerNotifications = await prisma.companyNotification.findMany({
    where: {
      archived_at: null,
      company_id: {
        in: companyUsers.map(({ company_id }) => company_id),
      },
      subject: 'Customer',
    },
  })

  const data = await Promise.all(
    companyNotifications
      .map((companyNotify) => {
        return Promise.all(
          companyUsers.map((companyUser) => {
            return generateNotification({
              company_notification: companyNotify,
              company_user: companyUser,
              contacts: contactsByCompanyUserId,
              orders: ordersByContactId,
            })
          }),
        )
      })
      .flat(),
  )

  // Process Team notifications separately with proper teammate data
  const teamData = await Promise.all(
    teamNotifications
      .map((companyNotify) => {
        return Promise.all(
          companyUsers.map((companyUser) => {
            return generateTeamNotification({
              company_notification: companyNotify,
              company_user: companyUser,
              orders: ordersByContactId,
              userMap,
            })
          }),
        )
      })
      .flat(),
  )

  // Process Customer notifications separately with proper contact data
  const customerData = await Promise.all(
    customerNotifications
      .map((companyNotify) => {
        return Promise.all(
          companyUsers.map((companyUser) => {
            return generateCustomerNotification({
              company_notification: companyNotify,
              company_user: companyUser,
              contacts: contactsByCompanyUserId,
              orders: ordersByContactId,
            })
          }),
        )
      })
      .flat(),
  )

  const filteredData = [
    ...data
      .flat()
      .filter((item): item is NonNullable<typeof item> => item !== null),
    ...teamData
      .flat()
      .filter((item): item is NonNullable<typeof item> => item !== null),
    ...customerData
      .flat()
      .filter((item): item is NonNullable<typeof item> => item !== null),
  ]

  await prisma.companyUserNotification.createMany({
    data: filteredData,
  })

  return true
}
