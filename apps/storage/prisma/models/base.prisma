model Contact {
  id          BigInt @id @default(autoincrement())
  public_uuid String @unique @default(uuid())

  email String

  first_name String
  last_name  String

  birthday DateTime?

  phone_country_code String
  phone_number       String

  /// [DBContactTypeValueObject]
  type          String  @default("Contact")
  referral_link String?

  contact_addresses      ContactAddress[]
  contact_notes          ContactNote[]
  orders                 Order[]
  subscriptions          Subscription[]
  credits                Credit?
  contact_content_tokens ContactContentToken[]
  outgoing_messages      OutgoingMessage[]

  company_user_id BigInt
  company_user    CompanyUser @relation(fields: [company_user_id], references: [id])

  last_order_date DateTime?

  created_at  DateTime  @default(now())
  updated_at  DateTime  @updatedAt
  archived_at DateTime?

  shopify_buyer ShopifyBuyer?

  @@map("contacts")
}

model Sku {
  id          BigInt @id @default(autoincrement())
  public_uuid String @unique @default(uuid())

  name                String
  code                String
  image_url           String?
  current_price_cents Int

  // Shopify product mapping
  shopify_product_id String?
  shopify_variant_id String?
  auto_created       Boolean @default(false)

  order_items         OrderItem[]
  subscription_skus   SubscriptionSku[]
  shopify_order_items ShopifyOrderItem[]

  company_id BigInt
  company    Company @relation(fields: [company_id], references: [id])

  created_at  DateTime  @default(now())
  updated_at  DateTime  @updatedAt
  archived_at DateTime?

  @@index([shopify_product_id, shopify_variant_id])
  @@map("skus")
}

model Order {
  id          BigInt @id @default(autoincrement())
  public_uuid String @unique @default(uuid())

  /// [DBOrderStatusValueObject]
  status String
  /// [DBOrderTypeValueObject]
  type   String

  order_number        String?
  price_currency      String
  total_cost_cents    Int
  shipping_cost_cents Int
  tracking_link       String

  items OrderItem[]

  shipping_address_id BigInt
  shipping_address    ContactAddress @relation("ShippingAddress", fields: [shipping_address_id], references: [id])
  billing_address_id  BigInt
  billing_address     ContactAddress @relation("BillingAddress", fields: [billing_address_id], references: [id])
  contact_id          BigInt
  contact             Contact        @relation(fields: [contact_id], references: [id])
  company_user_id     BigInt
  company_user        CompanyUser    @relation(fields: [company_user_id], references: [id])
  subscription_id     BigInt?
  subscription        Subscription?  @relation(fields: [subscription_id], references: [id])

  upcoming_at DateTime?

  created_at  DateTime  @default(now())
  updated_at  DateTime  @updatedAt
  archived_at DateTime?

  shopify_order ShopifyOrder?

  @@map("orders")
}

model OrderItem {
  id          BigInt @id @default(autoincrement())
  public_uuid String @unique @default(uuid())

  price_cents Int

  sku_id   BigInt
  sku      Sku    @relation(fields: [sku_id], references: [id])
  order_id BigInt
  order    Order  @relation(fields: [order_id], references: [id])

  created_at  DateTime  @default(now())
  archived_at DateTime?

  @@map("order_items")
}

model Subscription {
  id          BigInt @id @default(autoincrement())
  public_uuid String @unique @default(uuid())

  periodicity Int
  start_date  DateTime
  end_date    DateTime?
  /// [DBSubscriptionStatusValueObject]
  status      String    @default("Active")

  contact_id BigInt
  contact    Contact @relation(fields: [contact_id], references: [id])

  orders            Order[]
  subscription_skus SubscriptionSku[]

  created_at  DateTime  @default(now())
  updated_at  DateTime  @updatedAt
  archived_at DateTime?

  @@map("subscriptions")
}

model SubscriptionSku {
  id          BigInt @id @default(autoincrement())
  public_uuid String @unique @default(uuid())

  subscription_id BigInt
  subscription    Subscription @relation(fields: [subscription_id], references: [id])
  sku_id          BigInt
  sku             Sku          @relation(fields: [sku_id], references: [id])

  created_at  DateTime  @default(now())
  updated_at  DateTime  @updatedAt
  archived_at DateTime?

  @@map("subscription_skus")
}

model Credit {
  id          BigInt @id @default(autoincrement())
  public_uuid String @unique @default(uuid())

  total_amount_cents Int    @default(0)
  /// [DBCreditTypeValueObject]
  type               String
  currency           String

  contact_id BigInt  @unique()
  contact    Contact @relation(fields: [contact_id], references: [id])

  entries CreditEntry[]

  created_at  DateTime  @default(now())
  updated_at  DateTime  @updatedAt
  archived_at DateTime?

  @@map("credit")
}

model CreditEntry {
  id          BigInt @id @default(autoincrement())
  public_uuid String @unique @default(uuid())

  /// [DBCreditEntryTypeValueObject]
  type         String
  amount_cents Int
  expires_at   DateTime?

  credit_id BigInt
  credit    Credit @relation(fields: [credit_id], references: [id])

  created_at  DateTime  @default(now())
  updated_at  DateTime  @updatedAt
  archived_at DateTime?

  @@map("credit_entries")
}

model OutgoingMessage {
  id          BigInt @id @default(autoincrement())
  public_uuid String @unique @default(uuid())

  message String

  created_by_company_user_id BigInt
  created_by_company_user    CompanyUser @relation(fields: [created_by_company_user_id], references: [id])

  to_contact_id BigInt
  to_contact    Contact @relation(fields: [to_contact_id], references: [id])

  attached_content_id BigInt?
  attached_content    Content? @relation(fields: [attached_content_id], references: [id])

  created_at  DateTime  @default(now())
  archived_at DateTime?

  @@map("outgoing_messages")
}
