model User {
  id          BigInt @id @default(autoincrement())
  public_uuid String @unique @default(uuid())

  email              String @unique
  phone_country_code String @default("")
  phone_number       String @default("")
  password_hash      String

  is_root_admin <PERSON> @default(false)

  first_name String
  last_name  String
  avatar_url String?

  /// [DBOtpUserSettingsValueObject]
  otp Json?

  magic_token String?

  birthday DateTime?

  company_users         CompanyUser[]
  company_notifications CompanyNotification[]

  created_at  DateTime  @default(now())
  updated_at  DateTime  @updatedAt
  archived_at DateTime?

  @@map("users")
}

model CompanyUserDevice {
  id          BigInt @id @default(autoincrement())
  public_uuid String @unique @default(uuid())

  company_user_id BigInt
  company_user    CompanyUser @relation(fields: [company_user_id], references: [id])

  /// [DBDevicePlatformValueObject]
  device_platform String

  push_token String?

  created_at  DateTime  @default(now())
  updated_at  DateTime  @updatedAt
  archived_at DateTime?

  @@map("company_user_device")
}
