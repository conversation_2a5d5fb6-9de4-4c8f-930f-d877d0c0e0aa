model AssistantMessageHistory {
  id          BigInt @id @default(autoincrement())
  public_uuid String @unique @default(uuid())

  company_user_id BigInt
  company_user    CompanyUser @relation(fields: [company_user_id], references: [id])

  conversation_uuid String

  message String

  /// [DBAssistantMessageHistoryItemTypeValueObject]
  type String

  created_at  DateTime  @default(now())
  archived_at DateTime?

  @@map("assistant_message_history")
}
