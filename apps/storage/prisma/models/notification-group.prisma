model CompanyNotification {
  id          BigInt @id @default(autoincrement())
  public_uuid String @unique @default(uuid())

  name String @default("")

  company_id BigInt
  company    Company @relation(fields: [company_id], references: [id])

  /// [DBNotificationSubjectValueObject]
  subject String

  /// [DBNotificationAllSubtypeValueObject]
  subtype String

  /// [DBNotificationInitialContentValueObject]
  initial_content Json @default("{}")

  /// [DBNotificationTimingValueObject[]]
  timing Json @default("[{\"type\": \"immediate\"}]")

  is_enabled Boolean

  /// [DBReceiverTypeValueObject]
  receiver_type String
  /// [DBReceiverFileValueObject]
  receiver_file Json   @default("{}")

  company_user_notification_receivers CompanyUserNotificationReceiver[]
  company_user_notifications          CompanyUserNotification[]
  company_notification_jobs           CompanyNotificationJob[]

  user_settings_notifications SettingsNotifications[]

  created_by_id BigInt?
  created_by    User?   @relation(fields: [created_by_id], references: [id])

  created_at  DateTime  @default(now())
  updated_at  DateTime  @updatedAt
  archived_at DateTime?

  @@map("company_notifications")
}

model CompanyNotificationJob {
  job_uuid String @unique @default(uuid())

  company_notification_id BigInt
  company_notification    CompanyNotification @relation(fields: [company_notification_id], references: [id])

  @@map("company_notification_jobs")
}

// when users selected in admin panel directly, without any emails or something else. only DIRECT selection of users
model CompanyUserNotificationReceiver {
  id          BigInt @id @default(autoincrement())
  public_uuid String @unique @default(uuid())

  company_user_id BigInt
  company_user    CompanyUser @relation(fields: [company_user_id], references: [id])

  company_notification_id BigInt
  company_notification    CompanyNotification @relation(fields: [company_notification_id], references: [id])

  created_at  DateTime  @default(now())
  archived_at DateTime?

  @@map("company_user_notification_receiver")
}

model CompanyUserNotification {
  id          BigInt @id @default(autoincrement())
  public_uuid String @unique @default(uuid())

  is_read      Boolean   @default(false)
  scheduled_on DateTime?
  sent_at      DateTime?

  /// [DBCompanyUserNotificationContentValueObject]
  content Json @default("{}") // old settings field

  /// [DBNotificationAllMetaTypes]
  meta Json @default("{}")

  company_user_id BigInt
  company_user    CompanyUser @relation(fields: [company_user_id], references: [id])

  company_notification_id BigInt
  company_notification    CompanyNotification @relation(fields: [company_notification_id], references: [id])

  created_at  DateTime  @default(now())
  updated_at  DateTime  @updatedAt
  archived_at DateTime?

  @@map("company_user_notifications")
}
