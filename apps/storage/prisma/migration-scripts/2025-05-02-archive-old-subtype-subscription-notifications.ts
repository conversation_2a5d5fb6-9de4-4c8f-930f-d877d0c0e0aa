import type { PrismaClient } from '@prisma/client'

export async function archiveOldSubtypeSubscriptionNotifications20250502(
  prisma: PrismaClient,
): Promise<void> {
  // find all company notifications created before 2025-05-02 with subject Subscription
  // archive old company user notifications with subscription subject, because now we generate right metadata, and with the old one we will have error on the app side due to lack of subscription_uuid absence in the metadata
  const updatedCompanyUserNotifications =
    await prisma.companyUserNotification.updateMany({
      where: {
        archived_at: null,
        created_at: {
          lte: new Date('2025-05-02'),
        },
        company_notification: {
          subject: 'Subscription',
          created_at: {
            lte: new Date('2025-05-02'),
          },
        },
      },
      data: {
        archived_at: new Date(),
      },
    })

  console.log(
    updatedCompanyUserNotifications.count === 0
      ? 'No company user notifications to archive'
      : `Archived ${updatedCompanyUserNotifications.count} company user notifications`,
  )
}
