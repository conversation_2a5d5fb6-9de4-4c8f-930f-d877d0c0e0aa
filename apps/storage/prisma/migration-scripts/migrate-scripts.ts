import { env } from '@dotenv-run/core'

import { getPrismaClientInstance } from '~/get-prisma-client-instance'

import type { PrismaClient } from '@prisma/client'

// import { updateNotifications20250319 } from './2025-03-19-update-notifications'
// import { updateNotificationStructure20250401 } from './2025-04-01-notification-structure'
// import { updateSubscriptionNotifications20250429 } from './2025-04-29-update-subscription-notifications'
// import { archiveOldSubtypeSubscriptionNotifications20250502 } from './2025-05-02-archive-old-subtype-subscription-notifications'
// import { addCustomerDiscountExpirationNotification } from './2025-05-09-add-customer-discount-expiration-company-notification'

const prisma = getPrismaClientInstance()

const scripts: ((prisma: PrismaClient) => Promise<void>)[] = [
  // updateNotifications20250319,
  // updateNotificationStructure20250401,
  // updateSubscriptionNotifications20250429,
  // archiveOldSubtypeSubscriptionNotifications20250502,
  // addCustomerDiscountExpirationNotification,
]

env({
  root: '../../..',
  verbose: true,
})

console.log('\n')
console.info('--- Start migration scripts...')
console.time('Migration')

async function main(): Promise<void> {
  // Run all scripts sequentially
  console.log('total migration scripts: ', scripts.length)

  for (const scriptFn of scripts) {
    const scriptId = scriptFn.name
    try {
      console.log(`Running script: ${scriptId}`)
      await scriptFn(prisma)
      console.log(`Completed script: ${scriptId}`)
    } catch (error) {
      console.error(`Failed to run script ${scriptId}:`, error)
      throw error
    }
  }
}

main()
  .then(() => {
    console.info('--- Migration has finished')
  })
  .catch((e: unknown) => {
    console.error('--- Migration has errored\n', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma?.$disconnect()
    console.timeEnd('Migration')
    console.log('\n')
  })
