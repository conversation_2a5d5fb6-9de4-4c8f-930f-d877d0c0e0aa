import { getPrismaClientInstance } from '~/get-prisma-client-instance'

const prisma = getPrismaClientInstance()

async function main() {
  await prisma.$transaction(async (tx) => {
    const companyUsers = await prisma.companyUser.findMany({
      select: {
        id: true,
      },
    })

    const allData = companyUsers.map(({ id }) => ({
      company_user_id: id,
    }))

    await Promise.all([
      prisma.settingsNotifications.createMany({
        // @ts-expect-error
        data: allData,
      }),
      prisma.settingsCommon.createMany({
        data: allData,
      }),
    ])
  })
}

main()
  .catch(async (e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
