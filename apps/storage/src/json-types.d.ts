import type { DBCompanyUserActivityStatusValueObject as DBCompanyUserActivityStatusValueObject_ } from '~/modules/company/core/value-objects/db-company-user-activity-status.value-object'
import type { DBCompanyUserRankValueObject as DBCompanyUserRankValueObject_ } from '~/modules/company/core/value-objects/db-company-user-rank.value-object'
import type { DBDevicePlatformValueObject as DBDevicePlatformValueObject_ } from '~/modules/company-user-device/core/db-device-platform-value.object'
import type { DBContactTypeValueObject as ContactTypeValueObject_ } from '~/modules/contact'
import type { DBContentDomainValueObject as DBContentDomainValueObject_ } from '~/modules/content/core/value-objects/db-content-domain.value-object'
import type { DBContentLanguageValueObject as DBContentLanguageValueObject_ } from '~/modules/content/core/value-objects/db-content-language.value-object'
import type { DBContentStatusDataValueObject as DBContentStatusDataValueObject_ } from '~/modules/content/core/value-objects/db-content-status-data.value-object'
import type {
  DBCreditEntryTypeValueObject as CreditEntryTypeValueObject_,
  DBCreditTypeValueObject as CreditTypeValueObject_,
} from '~/modules/credit'
import type { DBInviteStatusValueObject as DBInviteStatusValueObject_ } from '~/modules/invites'
import type {
  DBCompanyUserNotificationContentValueObject as DBNotificationSettingsValueObject_,
  DBNotificationAllMetaTypes as DBNotificationAllMetaTypes_,
  DBNotificationAllSubtypeValueObject as DBNotificationAllSubtypeValueObject_,
  DBNotificationSubjectValueObject as NotificationSubjectValueObject_,
} from '~/modules/notification'
import type { DBNotificationInitialContentValueObject as DBNotificationInitialContentValueObject_ } from '~/modules/notification/core/value-objects/db-notification-initial-content.value-object'
import type { DBNotificationTimingValueObject as DBNotificationTimingValueObject_ } from '~/modules/notification/core/value-objects/db-notification-timing.value-object'
import type { DBReceiverFileValueObject as DBReceiverFileValueObject_ } from '~/modules/notification/core/value-objects/db-receiver-file.value-object'
import type { DBReceiverTypeValueObject as DBReceiverTypeValueObject_ } from '~/modules/notification/core/value-objects/db-receiver-type.value-object'
import type { DBJobStateValueObject as DBJobStateValueObject_ } from '~/modules/notification-jobs/core/db-job-state.value-object'
import type {
  DBOrderStatusValueObject as OrderStatusValueObject_,
  DBOrderTypeValueObject as OrderTypeValueObject_,
} from '~/modules/order'
import type { DBScheduleValueObject as ScheduleValueObject_ } from '~/modules/schedule'
import type { DBAccountTypeValueObject as DBAccountTypeValueObject_ } from '~/modules/user/core/value-objects/db-account-type.value-object'
import type { DBOtpUserSettingsValueObject as DBOtpUserSettingsValueObject_ } from '~/modules/user/core/value-objects/db-otp-user-settings.value-object'
import type { DBUserRoleValueObject as DBUserRoleValueObject_ } from '~/modules/user/core/value-objects/db-user-role.value-object'
import type { DBContentTypeValueObject as ContentTypeValueObject_ } from './modules/content/core/value-objects/db-content-type.value-object'

declare global {
  namespace PrismaJson {
    type DBContactTypeValueObject = ContactTypeValueObject_
    type DBCreditEntryTypeValueObject = CreditEntryTypeValueObject_
    type DBCreditTypeValueObject = CreditTypeValueObject_
    type DBNotificationSubjectValueObject = NotificationSubjectValueObject_
    type DBOrderStatusValueObject = OrderStatusValueObject_
    type DBOrderTypeValueObject = OrderTypeValueObject_
    type DBOtpUserSettingsValueObject = DBOtpUserSettingsValueObject_
    type DBUserRoleValueObject = DBUserRoleValueObject_
    type DBCompanyUserNotificationContentValueObject =
      DBNotificationSettingsValueObject_
    type DBAccountTypeValueObject = DBAccountTypeValueObject_
    type DBInviteStatusValueObject = DBInviteStatusValueObject_
    type DBNotificationInitialContentValueObject =
      DBNotificationInitialContentValueObject_
    type DBReceiverTypeValueObject = DBReceiverTypeValueObject_
    type DBReceiverFileValueObject = DBReceiverFileValueObject_
    type DBNotificationAllSubtypeValueObject =
      DBNotificationAllSubtypeValueObject_
    type DBNotificationAllMetaTypes = DBNotificationAllMetaTypes_
    type DBNotificationTimingValueObject = DBNotificationTimingValueObject_
    type DBDevicePlatformValueObject = DBDevicePlatformValueObject_
    type DBContentTypeValueObject = ContentTypeValueObject_
    type DBContentLanguageValueObject = DBContentLanguageValueObject_
    type DBContentStatusDataValueObject = DBContentStatusDataValueObject_
    type DBScheduleValueObject = ScheduleValueObject_
    type DBCompanyUserRankValueObject = DBCompanyUserRankValueObject_
    type DBCompanyUserActivityStatusValueObject =
      DBCompanyUserActivityStatusValueObject_
    type DBContentDomainValueObject = DBContentDomainValueObject_
    type DBJobStateValueObject = DBJobStateValueObject_
  }
}

export {}
