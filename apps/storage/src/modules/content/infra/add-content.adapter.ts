import { contentMapper } from '~/modules/content/core/content.mapper'
import { buildScheduleValue } from '~/modules/content/infra/build-schedule-value'

import type { PrismaClient } from '@prisma/client'
import type { IncomingContentValueObject } from '~/modules/content/core/value-objects/incoming-content.value-object'
import type { DBContentValueObject } from '../core/value-objects/db-content.value-object'

export class AddContentAdapter {
  constructor(private readonly prisma: PrismaClient) {}

  async addContent({
    companyUserUuid,
    content,
    companyUuid,
  }: {
    content: IncomingContentValueObject
    companyUserUuid?: string
    companyUuid: string
  }): Promise<DBContentValueObject | undefined> {
    const file = content.file
    const scheduleData = buildScheduleValue(content.schedule)
    const expirationData = buildScheduleValue(content.expiration)
    const result = await this.prisma.content.create({
      data: {
        created_by_company_user: companyUserUuid
          ? {
              connect: {
                public_uuid: companyUserUuid,
              },
            }
          : undefined,
        company: {
          connect: {
            public_uuid: companyUuid,
          },
        },
        file_content: file
          ? {
              create: {
                file: {
                  create: {
                    bucket_name: file.bucketName,
                    file_name: file.fileName,
                    mime_type: file.mimeType,
                    public_url: file.publicUrl,
                    file_size: file.fileSize,
                    thumbnail_url: file.thumbnailUrl,
                  },
                },
              },
            }
          : undefined,

        domain: content.domain,
        lang: content.lang,
        notification_subtype: content.notificationSubtype,
        schedule: scheduleData?.value,
        schedule_timestamp: scheduleData?.timestamp,
        expiration: expirationData?.value,
        expiration_timestamp: expirationData?.timestamp,
        is_default: content.isDefault,
        status: content.status,
        content_type: content.contentType,
        title: content.title,
        text: content.text,
        links: content.links,
        keywords: content.keywords,
      },
      include: {
        file_content: {
          include: {
            file: true,
          },
        },
      },
    })

    if (!result) return

    return contentMapper(result)
  }
}
