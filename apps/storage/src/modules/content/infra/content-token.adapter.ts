import { contentMapper } from '../core/content.mapper'

import type { Company, PrismaClient, User } from '@prisma/client'
import type { DBContentValueObject } from '~/modules/content'

export class ContentTokenAdapter {
  constructor(private readonly prisma: PrismaClient) {}

  async incrementTokenViewCount({ token }: { token: string }): Promise<
    | {
        content: DBContentValueObject
        company: Company
        user: User
      }
    | undefined
  > {
    const result = await this.prisma.userContentToken.update({
      where: {
        token,
        archived_at: null,
      },
      data: {
        view_count: {
          increment: 1,
        },
      },
      include: {
        company_user: {
          include: {
            company: true,
            user: true,
          },
        },
        content: {
          include: {
            file_content: {
              include: {
                file: true,
              },
            },
          },
        },
      },
    })

    if (!result) return undefined

    return {
      content: contentMapper(result.content),
      company: result.company_user.company,
      user: result.company_user.user,
    }
  }
}
