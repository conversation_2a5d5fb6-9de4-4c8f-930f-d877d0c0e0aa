import { resultErr, resultOk } from '@repo/result'

import { prisma } from '~/prisma.connection'

import { contactNoteErrors } from '../core'
import { GetContactNotesAdapter } from '../infra'

import type { Result } from '@repo/result'
import type {
  ContactNoteErrorsValueObject,
  ContactNoteWithRelationsValueObject,
} from '../core'

export class GetContactNotesUseCase {
  constructor(private readonly adapter = new GetContactNotesAdapter(prisma)) {}

  async getNotesByContactUuid({
    contactUuid,
  }: {
    contactUuid: string
  }): Promise<
    Result<ContactNoteWithRelationsValueObject[], ContactNoteErrorsValueObject>
  > {
    try {
      const notes = await this.adapter.getNotesByContactUuid({
        contactUuid,
      })

      if (!notes)
        return resultErr(contactNoteErrors('GetNotesByContactUuidNotExist'))

      return resultOk(notes)
    } catch (e: unknown) {
      const error = contactNoteErrors('GetNotesByContactUuidUnexpected', e)
      return resultErr(error)
    }
  }
}
