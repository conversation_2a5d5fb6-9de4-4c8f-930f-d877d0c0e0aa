import type { DBContactTypeValueObject } from './db-contact-type-value.object'
import type { OrderTypeValueObject } from './order-type.value-object'

type RangeDate = {
  gte?: Date
  lte?: Date
}

export type FilterContactsValueObject = {
  searchValue?: string
  orderBy?: OrderTypeValueObject
  hasActiveSubscriptions?: boolean
  type?: DBContactTypeValueObject
  birthday?: RangeDate
  orders?: RangeDate
  lastOrderDate?: RangeDate
  upcomingOrders?: RangeDate
}
