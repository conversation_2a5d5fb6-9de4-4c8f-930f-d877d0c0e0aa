import type { Prisma } from '@prisma/client'

export function getOrdersFilter(orders?: {
  gte?: Date
  lte?: Date
}): Prisma.ContactWhereInput['orders'] | undefined {
  if (!orders?.gte || !orders?.lte) return

  return {
    some: {
      archived_at: null,
      created_at: {
        gte: orders.gte,
        lte: orders.lte,
      },
    },
  }
}

export function getUpcomingOrdersFilter(upcomingOrders?: {
  gte?: Date
  lte?: Date
}): Prisma.ContactWhereInput['orders'] | undefined {
  if (!upcomingOrders?.gte || !upcomingOrders?.lte) return

  return {
    some: {
      upcoming_at: {
        gte: upcomingOrders.gte,
        lte: upcomingOrders.lte,
      },
    },
  }
}
