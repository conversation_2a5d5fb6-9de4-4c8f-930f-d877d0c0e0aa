import { ContactMapper } from '../core/mappers/contact.mapper'

import type { PrismaClient } from '@prisma/client'
import type { ContactWithRelationsValueObject } from '../core'

export class UpsertContactAdapter {
  constructor(private readonly prisma: PrismaClient) {}

  async upsertContact({
    public_uuid,
    address,
    birthday,
    email,
    first_name,
    last_name,
    phone_country_code,
    phone_number,
    companyUserUuid,
  }: {
    public_uuid?: string
    address?: {
      public_uuid?: string
      city: string
      country: string
      country_code: string
      mail_stop?: string | null
      zip_code: string
      state_code?: string | null
      street_1: string
      street_2?: string | null
    }
    birthday?: Date | null
    email?: string
    first_name: string
    last_name: string
    phone_country_code?: string
    phone_number?: string
    companyUserUuid: string
  }): Promise<ContactWithRelationsValueObject | undefined> {
    if (public_uuid) {
      const contact = await this.prisma.contact.findUnique({
        where: {
          public_uuid,
          archived_at: null,
          company_user: {
            public_uuid: companyUserUuid,
            archived_at: null,
          },
        },
        include: {
          contact_addresses: {
            include: {
              address: true,
            },
          },
        },
      })

      if (!contact) return undefined

      const contactAddress = await this.prisma.contactAddress.findFirst({
        where: {
          contact_id: contact.id,
        },
      })

      const result = await this.prisma.contact.update({
        where: {
          public_uuid,
          archived_at: null,
          company_user: {
            public_uuid: companyUserUuid,
            archived_at: null,
          },
        },
        data: {
          birthday,
          email,
          first_name,
          last_name,
          phone_country_code,
          phone_number,
          contact_addresses:
            address?.public_uuid && contactAddress
              ? {
                  update: {
                    where: { id: contactAddress.id },
                    data: {
                      address: {
                        update: {
                          city: address.city,
                          country: address.country,
                          country_code: address.country_code,
                          mail_stop: address.mail_stop,
                          zip_code: address.zip_code,
                          state_code: address.state_code,
                          street_1: address.street_1,
                          street_2: address.street_2,
                        },
                      },
                    },
                  },
                }
              : undefined,
        },
        include: {
          contact_addresses: {
            include: {
              address: true,
            },
          },
          contact_notes: {
            include: {
              note: true,
            },
          },
          orders: true,
        },
      })

      return result ? ContactMapper.toUseCaseValueObject(result) : undefined
    }

    const companyUser = await this.prisma.companyUser.findFirst({
      where: {
        public_uuid: companyUserUuid,
        archived_at: null,
      },
    })

    if (!companyUser) return

    const result = await this.prisma.contact.create({
      data: {
        birthday,
        email: email || '',
        first_name,
        last_name,
        phone_country_code: phone_country_code || '',
        phone_number: phone_number || '',
        company_user_id: companyUser.id,
        contact_addresses: address
          ? {
              create: {
                address: {
                  create: {
                    city: address.city,
                    country: address.country,
                    country_code: address.country_code,
                    mail_stop: address.mail_stop,
                    zip_code: address.zip_code,
                    state_code: address.state_code,
                    street_1: address.street_1,
                    street_2: address.street_2,
                  },
                },
              },
            }
          : undefined,
      },
      include: {
        contact_addresses: {
          include: {
            address: true,
          },
        },
        contact_notes: {
          include: {
            note: true,
          },
        },
        orders: true,
      },
    })

    return ContactMapper.toUseCaseValueObject(result)
  }
}
