import type { Prisma } from '@prisma/client'

export function getNameFilter(
  searchValue?: string,
): Prisma.ContactWhereInput[] | undefined {
  if (!searchValue) return

  return [
    {
      first_name: {
        contains: searchValue,
        mode: 'insensitive',
      },
    },
    {
      last_name: {
        contains: searchValue,
        mode: 'insensitive',
      },
    },
    {
      email: {
        contains: 'contactEmailOrName',
        mode: 'insensitive',
      },
    },
    {
      contact_addresses: {
        some: {
          address: {
            city: {
              contains: searchValue,
              mode: 'insensitive',
            },
          },
        },
      },
    },
  ]
}
