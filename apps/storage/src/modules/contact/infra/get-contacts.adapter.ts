import { DEFAULT_TAKE_FOR_SKIP } from '~/modules/pagination/interface/default-take'
import { getPaginationResult } from '~/modules/pagination/interface/get-pagination-result'

import { ContactMapper } from '../core/mappers/contact.mapper'

import { getNameFilter } from './get-name-filter'
import { getOrderBy } from './get-order-by'
import { getOrdersFilter, getUpcomingOrdersFilter } from './get-orders-filter'

import type { PrismaClient } from '@prisma/client'
import type { DBPaginationCursorValueObject } from '~/modules/pagination/core'
import type {
  ContactsListValueObject,
  ContactWithRelationsValueObject,
} from '../core'
import type { FilterContactsValueObject } from '../core/value-objects/filter-contacts.value-object'

export class GetContactsAdapter {
  constructor(private readonly prisma: PrismaClient) {}

  async getContactsByCompanyUserUuid({
    filters,
    pagination,
    companyUserUuid,
  }: {
    filters: FilterContactsValueObject
    pagination?: DBPaginationCursorValueObject
    companyUserUuid: string
  }): Promise<ContactsListValueObject | undefined> {
    const take = pagination?.take ? pagination.take : DEFAULT_TAKE_FOR_SKIP
    const {
      searchValue,
      orderBy,
      lastOrderDate,
      birthday,
      hasActiveSubscriptions,
      type,
      orders,
      upcomingOrders,
    } = filters

    const nameFilter = getNameFilter(searchValue)
    const ordersFilter = orders
      ? getOrdersFilter(orders)
      : getUpcomingOrdersFilter(upcomingOrders)

    const contacts = await this.prisma.contact.findMany({
      where: {
        archived_at: null,
        company_user: {
          public_uuid: companyUserUuid,
          archived_at: null,
        },
        type,
        birthday: birthday ?? undefined,
        last_order_date: lastOrderDate ?? undefined,
        orders: ordersFilter,
        OR: nameFilter,
        subscriptions: hasActiveSubscriptions
          ? {
              some: {
                status: 'Active',
                archived_at: null,
              },
            }
          : undefined,
      },
      orderBy: getOrderBy(orderBy),
      take: take + 1,
      cursor: pagination?.cursor
        ? {
            public_uuid: pagination.cursor,
          }
        : undefined,
    })

    if (!contacts) return

    const res = getPaginationResult({
      take,
      list: contacts,
    })

    return {
      list: res.list.map((contact) =>
        ContactMapper.toUseCaseValueObject(contact),
      ),
      pagination: res.pagination,
    }
  }

  async getContactByPublicUuid({
    publicUuid,
    companyUserUuid,
  }: {
    publicUuid: string
    companyUserUuid: string
  }): Promise<ContactWithRelationsValueObject | undefined> {
    const contact = await this.prisma.contact.findFirst({
      where: {
        public_uuid: publicUuid,
        archived_at: null,
        company_user: {
          public_uuid: companyUserUuid,
          archived_at: null,
        },
      },
      include: {
        contact_addresses: {
          include: {
            address: true,
          },
          orderBy: {
            id: 'asc',
          },
        },
        contact_notes: {
          include: {
            note: true,
          },
          where: {
            contact: {
              public_uuid: publicUuid,
              archived_at: null,
              company_user: {
                public_uuid: companyUserUuid,
                archived_at: null,
              },
            },
          },
          orderBy: {
            created_at: 'desc',
          },
        },
      },
    })

    if (!contact) return

    const [upcomingOrders, orders] = await Promise.all([
      this.prisma.order.findMany({
        where: {
          contact_id: contact.id,
          archived_at: null,
          upcoming_at: {
            not: null,
          },
          contact: {
            archived_at: null,
            company_user: {
              public_uuid: companyUserUuid,
              archived_at: null,
            },
          },
        },
        orderBy: {
          upcoming_at: 'desc',
        },
        take: 10,
      }),
      this.prisma.order.findMany({
        where: {
          contact_id: contact.id,
          archived_at: null,
          upcoming_at: null,
          contact: {
            archived_at: null,
            company_user: {
              public_uuid: companyUserUuid,
              archived_at: null,
            },
          },
        },
        orderBy: {
          created_at: 'desc',
        },
        take: 10,
      }),
    ])

    return ContactMapper.toUseCaseValueObject(contact, upcomingOrders, orders)
  }
}
