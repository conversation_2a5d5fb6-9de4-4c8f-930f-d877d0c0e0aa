import { resultErr, resultOk } from '@repo/result'

import { prisma } from '~/prisma.connection'

import { shopifyCustomerErrors } from '../core/errors/shopify-customer.errors'
import { UpsertShopifyBuyerByParterAdapter } from '../infra/upsert-shopify-buyer-by-parter.adapter'

export class UpsertShopifyBuyerByPartnerUseCase {
  constructor(
    private readonly upsertShopifyBuyerByParterAdapter = new UpsertShopifyBuyerByParterAdapter(
      prisma,
    ),
  ) {}

  async upsert({
    shopifyId,
    partnerShopifyId,
    email,
    firstName,
    lastName,
    phoneCountryCode,
    phoneNumber,
  }: {
    shopifyId: string
    partnerShopifyId: string
    email: string
    firstName: string
    lastName: string
    phoneCountryCode: string
    phoneNumber: string
  }) {
    try {
      const shopifyPartnerResult =
        await this.upsertShopifyBuyerByParterAdapter.upsert({
          shopifyId,
          partnerShopifyId,
          email,
          firstName,
          lastName,
          phoneCountryCode,
          phoneNumber,
        })
      if (!shopifyPartnerResult)
        return resultErr(shopifyCustomerErrors('UpsertShopifyBuyerByPartner'))

      return resultOk({
        contact: shopifyPartnerResult,
      })
    } catch (e: unknown) {
      const error = shopifyCustomerErrors('Unexpected', e)
      return resultErr(error)
    }
  }
}
