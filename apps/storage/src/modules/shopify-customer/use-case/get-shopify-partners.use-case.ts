import { resultErr, resultOk } from '@repo/result'

import { prisma } from '~/prisma.connection'

import { shopifyCustomerErrors } from '../core/errors/shopify-customer.errors'
import { GetShopifyPartnersAdapter } from '../infra/get-shopify-partners.adapter'

export class GetShopifyPartnersUseCase {
  constructor(
    private readonly getShopifyPartnersAdapter = new GetShopifyPartnersAdapter(
      prisma,
    ),
  ) {}

  async get({
    query,
    shopifyCompanyId,
  }: {
    query: string
    shopifyCompanyId: string
  }) {
    try {
      const shopifyCustomersResult = await this.getShopifyPartnersAdapter.get({
        query,
        shopifyCompanyId,
      })
      if (!shopifyCustomersResult)
        return resultErr(shopifyCustomerErrors('GetShopifyPartners'))

      return resultOk({
        shopifyCustomers: shopifyCustomersResult,
      })
    } catch (e: unknown) {
      const error = shopifyCustomerErrors('Unexpected', e)
      return resultErr(error)
    }
  }
}
