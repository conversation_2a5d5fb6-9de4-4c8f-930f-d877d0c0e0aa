import { resultErr, resultOk } from '@repo/result'

import { prisma } from '~/prisma.connection'

import { teammateNoteErrors } from '../core/errors/teammate-note.errors'
import { DeleteTeammateNoteAdapter } from '../infra/delete-teammate-note.adapter'

import type { Result } from '@repo/result'
import type { TeammateNoteWithRelationsValueObject } from '../core'
import type { TeammateNoteErrorsValueObject } from '../core/errors/teammate-note.errors'

export class DeleteTeammateNoteUseCase {
  constructor(
    private readonly adapter = new DeleteTeammateNoteAdapter(prisma),
  ) {}

  async deleteNote({
    uuid,
    companyUserUuid,
  }: {
    uuid: string
    companyUserUuid: string
  }): Promise<
    Result<TeammateNoteWithRelationsValueObject, TeammateNoteErrorsValueObject>
  > {
    try {
      const note = await this.adapter.deleteNote({
        uuid,
        companyUserUuid,
      })

      if (!note)
        return resultErr(teammateNoteErrors('DeleteTeammateNoteNotExist'))

      return resultOk(note)
    } catch (e: unknown) {
      const error = teammateNoteErrors('DeleteTeammateNoteUnexpected', e)
      return resultErr(error)
    }
  }
}
