import { endOfDay } from '@repo/date'
import { resultErr, resultOk } from '@repo/result'

import { prisma } from '~/prisma.connection'

import { orderErrors } from '../core'
import { GetOrdersAdapter } from '../infra/get-orders.adapter'

import type { Result } from '@repo/result'
import type { DBPaginationCursorValueObject } from '~/modules/pagination/core'
import type { OrderErrorsValueObject, OrdersListValueObject } from '../core'

export class GetOrderListUseCase {
  constructor(private readonly adapter = new GetOrdersAdapter(prisma)) {}

  async getOrderList({
    companyUserUuid,
    searchField,
    pagination,
  }: {
    companyUserUuid: string
    searchField?: string
    pagination?: DBPaginationCursorValueObject
  }): Promise<Result<OrdersListValueObject, OrderErrorsValueObject>> {
    try {
      const result = await this.adapter.getOrdersByCompanyUserUuid({
        companyUserUuid,
        filter: {
          OR: [
            { upcoming_at: { lte: endOfDay(new Date()) } },
            { upcoming_at: null },
          ],
          contact: searchField
            ? {
                OR: [
                  {
                    first_name: {
                      contains: searchField,
                      mode: 'insensitive',
                    },
                  },
                  {
                    last_name: {
                      contains: searchField,
                      mode: 'insensitive',
                    },
                  },
                  {
                    email: { contains: searchField, mode: 'insensitive' },
                  },
                ],
              }
            : undefined,
        },
        pagination,
        orderBy: {
          created_at: 'desc',
        },
      })
      if (!result)
        return resultErr(orderErrors('GetOrdersByUserPublicUuidNotExist'))

      return resultOk(result)
    } catch (e: unknown) {
      const error = orderErrors('GetOrdersUnexpected', e)
      return resultErr(error)
    }
  }
}
