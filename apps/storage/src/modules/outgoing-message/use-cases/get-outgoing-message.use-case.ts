import { resultErr, resultOk } from '@repo/result'

import { prisma } from '~/prisma.connection'

import { outgoingMessageErrors } from '../core/errors/outgoing-message.errors'
import { GetOutgoingMessageAdapter } from '../infra/get-outgoing-message.adapter'

import type { Result } from '@repo/result'
import type { OutgoingMessageErrorsValueObject } from '../core/errors/outgoing-message.errors'
import type { DBOutgoingMessageValueObject } from '../core/value-objects/db-outgoing-message.value-object'

export class GetOutgoingMessageUseCase {
  constructor(
    private readonly getOutgoingMessageAdapter = new GetOutgoingMessageAdapter(
      prisma,
    ),
  ) {}

  async getOutgoingMessage({
    uuid,
  }: {
    uuid: string
  }): Promise<
    Result<DBOutgoingMessageValueObject, OutgoingMessageErrorsValueObject>
  > {
    try {
      const outgoingMessage =
        await this.getOutgoingMessageAdapter.getOutgoingMessage({ uuid })

      if (!outgoingMessage) return resultErr(outgoingMessageErrors('NotExist'))

      return resultOk(outgoingMessage)
    } catch (e) {
      return resultErr(outgoingMessageErrors('Unexpected', e))
    }
  }
}
