import { resultErr, resultOk } from '@repo/result'

import { prisma } from '~/prisma.connection'

import { outgoingMessageErrors } from '../core/errors/outgoing-message.errors'
import { AddOutgoingMessageAdapter } from '../infra/add-outgoing-message.adapter'

import type { Result } from '@repo/result'
import type { OutgoingMessageErrorsValueObject } from '../core/errors/outgoing-message.errors'
import type { DBOutgoingMessageValueObject } from '../core/value-objects/db-outgoing-message.value-object'

export class AddOutgoingMessageUseCase {
  constructor(
    private readonly addOutgoingMessageAdapter = new AddOutgoingMessageAdapter(
      prisma,
    ),
  ) {}

  async addOutgoingMessage({
    companyUserUuid,
    contentUuid,
    toContactUuid,
    message,
  }: {
    companyUserUuid: string
    contentUuid?: string
    toContactUuid: string
    message: string
  }): Promise<
    Result<DBOutgoingMessageValueObject, OutgoingMessageErrorsValueObject>
  > {
    try {
      const result = await this.addOutgoingMessageAdapter.addOutgoingMessage({
        companyUserUuid,
        contentUuid,
        toContactUuid,
        message,
      })

      if (!result) {
        return resultErr(outgoingMessageErrors('CreateFailed'))
      }

      return resultOk(result)
    } catch (e) {
      return resultErr(outgoingMessageErrors('Unexpected', e))
    }
  }
}
