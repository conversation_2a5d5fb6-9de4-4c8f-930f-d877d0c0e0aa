import type { Prisma } from '@prisma/client'
import type { DBOutgoingMessageValueObject } from '../value-objects/db-outgoing-message.value-object'

export function outgoingMessageMapper(
  outgoingMessage: Prisma.OutgoingMessageGetPayload<{
    include: {
      created_by_company_user: true
      to_contact: true
      attached_content: true
    }
  }>,
): DBOutgoingMessageValueObject {
  return {
    outgoingMessage,
    createdByCompanyUser: outgoingMessage.created_by_company_user,
    toContact: outgoingMessage.to_contact,
    attachedContent: outgoingMessage.attached_content ?? undefined,
  }
}
