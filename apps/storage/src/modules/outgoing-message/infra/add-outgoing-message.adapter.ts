import { outgoingMessageMapper } from '../core/mappers/outgoing-message.mapper'

import type { PrismaClient } from '@prisma/client'
import type { DBOutgoingMessageValueObject } from '../core/value-objects/db-outgoing-message.value-object'

export class AddOutgoingMessageAdapter {
  constructor(private readonly prisma: PrismaClient) {}

  async addOutgoingMessage({
    companyUserUuid,
    contentUuid,
    toContactUuid,
    message,
  }: {
    companyUserUuid: string
    contentUuid?: string
    toContactUuid: string
    message: string
  }): Promise<DBOutgoingMessageValueObject | undefined> {
    const addOutgoingMessage = await this.prisma.outgoingMessage.create({
      data: {
        created_by_company_user: {
          connect: {
            public_uuid: companyUserUuid,
          },
        },
        to_contact: {
          connect: {
            public_uuid: toContactUuid,
          },
        },
        attached_content: contentUuid
          ? {
              connect: {
                public_uuid: contentUuid,
              },
            }
          : undefined,
        message,
      },
      include: {
        created_by_company_user: true,
        to_contact: true,
        attached_content: true,
      },
    })

    if (!addOutgoingMessage) return

    return outgoingMessageMapper(addOutgoingMessage)
  }
}
