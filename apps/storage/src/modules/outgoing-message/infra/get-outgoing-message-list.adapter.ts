import { Prisma } from '@prisma/client'

import { DEFAULT_TAKE_FOR_SKIP } from '~/modules/pagination/interface/default-take'
import { getPaginationResult } from '~/modules/pagination/interface/get-pagination-result'

import { outgoingMessageMapper } from '../core/mappers/outgoing-message.mapper'

import type { PrismaClient } from '@prisma/client'
import type { DBPaginationCursorValueObject } from '~/modules/pagination/core'
import type { OutgoingMessageListValueObject } from '../core/value-objects/outgoing-message-list.value-object'

export class GetOutgoingMessageListAdapter {
  constructor(private readonly prisma: PrismaClient) {}

  async getOutgoingMessageList({
    companyUserUuid,
    contactUuid,
    pagination,
    searchTerm,
  }: {
    companyUserUuid: string
    contactUuid?: string
    pagination?: DBPaginationCursorValueObject
    searchTerm?: string
  }): Promise<OutgoingMessageListValueObject | undefined> {
    const take = pagination?.take ? pagination.take : DEFAULT_TAKE_FOR_SKIP

    const messages = await this.prisma.outgoingMessage.findMany({
      where: {
        created_by_company_user: {
          public_uuid: companyUserUuid,
        },
        to_contact: contactUuid
          ? {
              public_uuid: contactUuid,
            }
          : undefined,
        archived_at: null,
        OR: this.buildSearchConditions(searchTerm),
      },
      orderBy: {
        created_at: 'desc',
      },
      take: take + 1, // Take one more to determine if there are more results
      cursor: pagination?.cursor
        ? {
            public_uuid: pagination.cursor,
          }
        : undefined,
      include: {
        created_by_company_user: true,
        to_contact: true,
        attached_content: true,
      },
    })

    if (!messages) return

    const result = getPaginationResult({
      take,
      list: messages,
    })

    return {
      list: result.list.map((message) => outgoingMessageMapper(message)),
      pagination: result.pagination,
    }
  }

  /**
   * Build search conditions for outgoing messages based on search term
   * @param searchTerm - The term to search for in messages and contact names
   * @returns Prisma where conditions for the search
   */
  private buildSearchConditions(
    searchTerm?: string,
  ): Prisma.OutgoingMessageWhereInput[] | undefined {
    if (!searchTerm) return

    return [
      {
        message: {
          contains: searchTerm,
          mode: Prisma.QueryMode.insensitive,
        },
      },
      {
        to_contact: {
          OR: this.buildContactNameSearchConditions(searchTerm),
        },
      },
    ]
  }

  private buildContactNameSearchConditions(
    searchTerm: string,
  ): Prisma.ContactWhereInput[] {
    const conditions = [
      {
        first_name: {
          contains: searchTerm,
          mode: Prisma.QueryMode.insensitive,
        },
      },
      {
        last_name: {
          contains: searchTerm,
          mode: Prisma.QueryMode.insensitive,
        },
      },
    ]

    if (!searchTerm.includes(' ')) {
      return conditions
    }

    const [firstName, ...lastNameParts] = searchTerm.split(' ')
    const lastName = lastNameParts.join(' ')

    conditions.push(
      {
        first_name: {
          contains: firstName,
          mode: Prisma.QueryMode.insensitive,
        },
      },
      {
        last_name: {
          contains: lastName,
          mode: Prisma.QueryMode.insensitive,
        },
      },
    )

    return conditions
  }
}
