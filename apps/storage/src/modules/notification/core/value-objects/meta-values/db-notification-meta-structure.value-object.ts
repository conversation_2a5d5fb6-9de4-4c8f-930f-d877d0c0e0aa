import type { DBCompanyUserActivityStatusValueObject } from '~/modules/company'
import type { ContactBasicMetaValueObject } from './contact-basic-meta.value-object'
import type { TeammateBasicMetaValueObject } from './teammmate-basic-meta.value-object'

type OrderMeta = ContactBasicMetaValueObject<{
  order_uuid: string
}>

type TeammateMeta = TeammateBasicMetaValueObject<{
  order_uuid: string
  recruit_uuid: string
}>

// Subscription related types
type SubscriptionMeta = ContactBasicMetaValueObject<{
  subscription_uuid: string
}>

type DiscountExpirationMeta = ContactBasicMetaValueObject<{
  expiration_timestamp: number
}>

export type DBNotificationMetaStructureValueObject = {
  Birthday: {
    CustomerBirthday: ContactBasicMetaValueObject
    GroupBirthday: {}
  }
  Order: {
    NewOrder: OrderMeta
    OrderFollowup2w: OrderMeta
    OrderFollowup2mon: OrderMeta
    OrderFollowup6mon: OrderMeta
    CorpOrderAssigned: OrderMeta
  }
  Subscription: {
    SubscriptionReminder: SubscriptionMeta
    SubscriptionCustomerSkipped: SubscriptionMeta
    SubscriptionCustomerPaused: SubscriptionMeta
    SubscriptionCustomerCancels: SubscriptionMeta
    SubscriptionCustomerPaymentFailed: SubscriptionMeta
  }
  Customer: {
    CustomerDiscountExpiration: DiscountExpirationMeta
  }
  Team: {
    TeamNewRecruit: TeammateMeta
    TeamNewRecruitDownline: TeammateMeta
    TeamStatusChange: TeammateBasicMetaValueObject<{
      new_status: DBCompanyUserActivityStatusValueObject
    }>
    TeamAnniversaries: { timestamp: number }
    TeamBirthdays: { timestamp: number }
  }
  HomeOffice: {
    TBC: {}
  }
}
