import { afterEach, beforeEach, describe, expect, it } from '@repo/unit-test'
import { vi } from 'vitest'

import { getScheduleSeconds } from './get-schedule-seconds'

describe('getScheduleSeconds', () => {
  const mockDate = new Date('2024-02-15T10:00:00.000Z')

  beforeEach(() => {
    vi.useFakeTimers()
    vi.setSystemTime(mockDate)
    process.env.TZ = 'UTC' // Set timezone for consistent testing
  })

  afterEach(() => {
    vi.useRealTimers()
    process.env.TZ = undefined // Reset timezone
  })

  it('should return 0 for past schedule', () => {
    const pastSchedule = {
      day: { y: 2024, m: 1, d: 14 },
      time: { h: 10, m: 0 },
      tz: 'UTC',
    }

    const result = getScheduleSeconds(pastSchedule)
    expect(result).toBe(-86400) // One day in the past
  })

  it('should return correct seconds for future schedule', () => {
    const futureSchedule = {
      day: { y: 2024, m: 1, d: 15 },
      time: { h: 11, m: 0 },
      tz: 'UTC',
    }

    const result = getScheduleSeconds(futureSchedule)
    expect(result).toBe(3600) // 1 hour in future
  })

  describe('timezone handling', () => {
    it('should handle different timezones', () => {
      const schedule = {
        day: { y: 2024, m: 1, d: 15 },
        time: { h: 11, m: 0 },
        tz: 'America/New_York',
      }

      const result = getScheduleSeconds(schedule)
      expect(result).toBe(-14400) // 4 hours difference due to timezone in winter (non-DST period)
    })

    it('should handle daylight saving time', () => {
      const dstSchedule = {
        day: { y: 2024, m: 2, d: 10 }, // March 10, 2024 (DST start in US)
        time: { h: 2, m: 30 },
        tz: 'America/New_York',
      }

      const result = getScheduleSeconds(dstSchedule)
      // Calculate expected seconds between mockDate and DST transition
      expect(result).toBeGreaterThan(0)
    })
  })

  it('should handle incomplete schedule', () => {
    const incompleteSchedule = {
      tz: 'UTC',
    }

    const result = getScheduleSeconds(incompleteSchedule)
    expect(result).toBe(0)
  })
})
