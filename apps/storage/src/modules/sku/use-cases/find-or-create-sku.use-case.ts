import { type Result, resultErr, resultOk } from '@repo/result'

import { prisma } from '~/prisma.connection'

import { SkuCreationError } from '../core'
import { FindOrCreateSkuAdapter } from '../infra/find-or-create-sku.adapter'

import type { AutoSkuInputValueObject, ExistingSkuValueObject } from '../core'

export class FindOrCreateSkuUseCase {
  private adapter: FindOrCreateSkuAdapter

  constructor() {
    this.adapter = new FindOrCreateSkuAdapter(prisma)
  }

  async execute(
    input: AutoSkuInputValueObject,
  ): Promise<Result<ExistingSkuValueObject, SkuCreationError>> {
    try {
      // First, try to find existing SKU by Shopify product mapping
      const existingSku = await this.adapter.findByShopifyProduct(
        input.shopifyProductId,
        input.shopifyVariantId,
      )

      if (existingSku) {
        // Update price if different (Shopify prices might change)
        if (existingSku.currentPriceCents !== input.priceCents) {
          await this.adapter.updateSkuPrice(existingSku.id, input.priceCents)
          existingSku.currentPriceCents = input.priceCents
        }

        // Update image if provided and SKU doesn't have one
        if (input.imageUrl && !existingSku.imageUrl) {
          await this.adapter.updateSkuImage(existingSku.id, input.imageUrl)
        }

        return resultOk(existingSku)
      }

      // Create new auto-generated SKU
      const newSku = await this.adapter.createAutoSku(input)
      return resultOk(newSku)
    } catch (error) {
      const message =
        error instanceof Error ? error.message : 'Unknown error creating SKU'
      return resultErr(
        new SkuCreationError(`Failed to find or create SKU: ${message}`),
      )
    }
  }
}
