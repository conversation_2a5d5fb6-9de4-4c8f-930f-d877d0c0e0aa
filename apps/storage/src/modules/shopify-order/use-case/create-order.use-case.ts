import { resultErr, resultOk } from '@repo/result'

import {
  type DBOrderStatusValueObject,
  type DBOrderTypeValueObject,
} from '~/modules/order/core'
import { prisma } from '~/prisma.connection'

import { shopifyOrderErrors } from '../core/errors/shopify-order.errors'
import { CreateShopifyOrderAdapter } from '../infra/create-shopify-order.adapter'

const logger = console

type AddressInput = {
  street1: string
  street2?: string
  city: string
  country: string
  countryCode: string
  zipCode: string
}

type OrderItemInput = {
  priceCents: number
  skuId: string
}

export class CreateShopifyOrderUseCase {
  constructor(
    private readonly adapter = new CreateShopifyOrderAdapter(prisma),
  ) {}

  async createOrder({
    shopifyId,
    orderNumber,
    billingAddress,
    companyUserUuid,
    contactUuid,
    currency,
    shippingAddress,
    shippingCostCents,
    status,
    totalCostCents,
    trackingLink,
    type,
    items,
  }: {
    shopifyId: string
    orderNumber?: string
    companyUserUuid?: string
    status: DBOrderStatusValueObject
    type: DBOrderTypeValueObject
    currency: string
    shippingCostCents: number
    totalCostCents: number
    trackingLink: string
    shippingAddress: AddressInput
    billingAddress: AddressInput
    contactUuid?: string
    items: OrderItemInput[]
  }) {
    try {
      const order = await this.adapter.createOrder({
        orderNumber,
        shopifyId,
        billingAddress,
        companyUserUuid,
        contactUuid,
        currency,
        shippingAddress,
        shippingCostCents,
        status,
        totalCostCents,
        trackingLink,
        type,
        items,
      })

      if (!order) return resultErr(shopifyOrderErrors('CreateShopifyOrder'))

      return resultOk(order)
    } catch (e: unknown) {
      const error = shopifyOrderErrors('Unexpected', e)
      logger.error(error.key, error)
      return resultErr(error)
    }
  }
}
