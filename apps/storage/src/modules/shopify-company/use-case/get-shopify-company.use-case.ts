import { resultErr, resultOk } from '@repo/result'

import { prisma } from '~/prisma.connection'

import { shopifyCompanyErrors } from '../core/errors/shopify-company.errors'
import { GetShopifyCompanyAdapter } from '../infra/get-shopify-company.adapter'

export class GetShopifyCompanyUseCase {
  constructor(
    private readonly getShopifyCompanyAdapter = new GetShopifyCompanyAdapter(
      prisma,
    ),
  ) {}

  async get({ shopifyCompanyId }: { shopifyCompanyId: string }) {
    try {
      const shopifyCompanyResult = await this.getShopifyCompanyAdapter.get({
        shopifyCompanyId,
      })
      if (!shopifyCompanyResult)
        return resultErr(shopifyCompanyErrors('GetShopifyCompany'))

      return resultOk({
        shopifyCompany: shopifyCompanyResult,
      })
    } catch (e: unknown) {
      const error = shopifyCompanyErrors('Unexpected', e)
      return resultErr(error)
    }
  }
}
