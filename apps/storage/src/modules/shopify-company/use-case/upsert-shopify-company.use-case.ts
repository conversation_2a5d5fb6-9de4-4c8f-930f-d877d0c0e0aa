import { resultErr, resultOk } from '@repo/result'

import { prisma } from '~/prisma.connection'

import { shopifyCompanyErrors } from '../core/errors/shopify-company.errors'
import { UpsertShopifyCompanyAdapter } from '../infra/upsert-shopify-company.adater'

export class UpsertShopifyCompanyUseCase {
  constructor(
    private readonly upsertShopifyCompanyAdapter = new UpsertShopifyCompanyAdapter(
      prisma,
    ),
  ) {}

  async upsert({
    shopifyCompanyId,
    name,
  }: {
    shopifyCompanyId: string
    name: string
  }) {
    // get company by shopifyCompanyId
    // if exist - return
    // if !exist - AddCompanyUseCase + FillCompanyNotificationUseCase + upsertShopifyCompanyAdapter(shopifyCompanyId, createdCompany.id)
    try {
      const shopifyCompanyResult =
        await this.upsertShopifyCompanyAdapter.upsert({
          shopifyCompanyId,
          name,
        })
      if (!shopifyCompanyResult)
        return resultErr(shopifyCompanyErrors('CreateShopifyCompany'))

      return resultOk({
        shopifyCompany: shopifyCompanyResult,
      })
    } catch (e: unknown) {
      const error = shopifyCompanyErrors('Unexpected', e)
      return resultErr(error)
    }
  }
}
