import type { PrismaClient } from '@prisma/client'

export class UpsertShopifyCompanyAdapter {
  constructor(private readonly prisma: PrismaClient) {}

  async upsert({
    shopifyCompanyId,
    name,
  }: {
    shopifyCompanyId: string
    name: string
  }) {
    const res = await this.prisma.shopifyCompany.upsert({
      where: {
        shopify_company_id: shopifyCompanyId,
      },
      update: {},
      create: {
        shopify_company_id: shopifyCompanyId,
        company: {
          create: {
            name,
          },
        },
      },
    })

    if (!res) return

    return res
  }
}
