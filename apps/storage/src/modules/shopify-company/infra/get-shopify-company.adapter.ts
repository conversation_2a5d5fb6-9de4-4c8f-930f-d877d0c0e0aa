import type { PrismaClient } from '@prisma/client'

export class GetShopifyCompanyAdapter {
  constructor(private readonly prisma: PrismaClient) {}

  async get({ shopifyCompanyId }: { shopifyCompanyId: string }) {
    const res = await this.prisma.shopifyCompany.findFirst({
      where: {
        shopify_company_id: shopifyCompanyId,
      },
      include: {
        company: true,
      },
    })

    if (!res) return

    return res
  }
}
