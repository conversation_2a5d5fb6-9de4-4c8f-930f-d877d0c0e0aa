import { resultErr, resultOk } from '@repo/result'

import { userManagementErrors } from '~/modules/user-management/core/user-management.errors'
import { prisma } from '~/prisma.connection'

import { EditCompanyUserPersonalLinkAdapter } from '../infra/edit-company-user-personal-link.adapter'

export class EditCompanyUserPersonalLinkUseCase {
  constructor(
    private readonly adapter = new EditCompanyUserPersonalLinkAdapter(prisma),
  ) {}

  async edit({
    companyUserUuid,
    personalLink,
  }: {
    companyUserUuid: string
    personalLink: string | null
  }) {
    try {
      const result = await this.adapter.edit({
        companyUserUuid,
        personalLink,
      })

      if (!result) return resultErr(userManagementErrors('EditCompanyUser'))

      return resultOk({
        personalLink: result.personal_link,
        uuid: result.public_uuid,
        firstName: result.user.first_name,
        lastName: result.user.last_name,
        email: result.user.email,
      })
    } catch (e: unknown) {
      const error = userManagementErrors('Unexpected', e)
      return resultErr(error)
    }
  }
}
