import type { Prisma } from '@prisma/client'
import type { AnniversaryTeammateEntity } from '../entities/anniversary-teammate.entity'
import type { AnniversaryTeammateListValueObject } from '../value-object/anniversary-teammate-list.value-object'

export class AnniversaryTeammatesMapper {
  static toTeammate({
    teammate,
    anniversaryDate,
  }: {
    teammate: Prisma.CompanyUserGetPayload<{
      include: { user: true }
    }>
    anniversaryDate: Date
  }): AnniversaryTeammateEntity {
    return {
      uuid: teammate.public_uuid,
      email: teammate.user.email,
      phoneNumber: teammate.user.phone_number,
      phoneCountryCode: teammate.user.phone_country_code,
      firstName: teammate.user.first_name,
      lastName: teammate.user.last_name,
      anniversaryCount: teammate.start_date
        ? new Date(anniversaryDate).getFullYear() -
          new Date(teammate.start_date).getFullYear()
        : 0,
    }
  }

  static toAnniversaryTeammateListValueObject({
    teammates,
    anniversaryDate,
  }: {
    teammates: Prisma.CompanyUserGetPayload<{
      include: {
        user: true
        hierarchy_ancestors: {
          include: {
            ancestor: true
          }
        }
        company: true
      }
    }>[]
    anniversaryDate: Date
  }): AnniversaryTeammateListValueObject[] {
    const teammatesByAncestor = new Map<
      string,
      {
        teammates: AnniversaryTeammateEntity[]
        companyUuid: string
      }
    >()

    for (const teammate of teammates) {
      const companyUuid = teammate.company.public_uuid

      teammate.hierarchy_ancestors.forEach(({ ancestor }) => {
        const ancestorId = String(ancestor.id)
        if (!teammatesByAncestor.has(ancestorId)) {
          teammatesByAncestor.set(ancestorId, {
            teammates: [],
            companyUuid,
          })
        }

        teammatesByAncestor.get(ancestorId)?.teammates.push(
          this.toTeammate({
            teammate,
            anniversaryDate,
          }),
        )
      })
    }

    return Array.from(teammatesByAncestor.entries()).map(
      ([ancestorId, { teammates, companyUuid }]) => ({
        ancestorId,
        companyUuid,
        teammates,
      }),
    )
  }
}
