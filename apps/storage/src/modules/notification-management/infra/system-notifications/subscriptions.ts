import type { CompanyNotificationFieldsValueObject } from '../../core/company-notification-fields.value-object'

export const subscriptions: CompanyNotificationFieldsValueObject[] = [
  {
    name: 'Subscription Reminder',
    subject: 'Subscription',
    subtype: 'SubscriptionReminder',
    initialContent: {
      title: 'Subscription Alert! ⏰',
      html: '{{Name}}’s next subscription order is next week. Check-in to confirm the order details and see if they want to add anything.',
      pushTitle: 'Subscription Alert! ⏰',
      pushDesc:
        '{{Name}}’s next subscription order is next week. Check-in to confirm the order details and see if they want to add anything.',
    },
    isEnabled: true,
    receiverType: 'All',
    timing: [{ type: 'before', prop: { unit: 'w', val: 1 } }],
  },
  {
    name: 'Subscription Skipped (Customer)',
    subject: 'Subscription',
    subtype: 'SubscriptionCustomerSkipped',
    initialContent: {
      title: 'Skipped Subscription',
      html: '{{Name}} has skipped their subscription for {{Product}} - this would be a good time to check in.',
      pushTitle: 'Skipped Subscription',
      pushDesc:
        '{{Name}} has skipped their subscription for {{Product}} - this would be a good time to check in.',
    },
    isEnabled: true,
    receiverType: 'All',
    timing: [{ type: 'immediate' }],
  },
  {
    name: 'Subscription Paused (Customer)',
    subject: 'Subscription',
    subtype: 'SubscriptionCustomerPaused',
    initialContent: {
      title: 'Paused Subscription',
      html: '{{Name}} has paused their subscription for {{Product}} - this would be a good time to check in.',
      pushTitle: 'Paused Subscription',
      pushDesc:
        '{{Name}} has paused their subscription for {{Product}} - this would be a good time to check in.',
    },
    isEnabled: true,
    receiverType: 'All',
    timing: [{ type: 'immediate' }],
  },
  {
    name: 'Subscription Cancelled (Customer)',
    subject: 'Subscription',
    subtype: 'SubscriptionCustomerCancels',
    initialContent: {
      title: 'Cancelled Subscription',
      html: 'Heads up! {{Name}} has canceled their subscription. A quick check-in could bring them back!',
      pushTitle: 'Cancelled Subscription',
      pushDesc:
        'Heads up! {{Name}} has canceled their subscription. A quick check-in could bring them back!',
    },
    isEnabled: true,
    receiverType: 'All',
    timing: [{ type: 'immediate' }],
  },
  {
    name: 'Subscription Payment Failed (Customer)',
    subject: 'Subscription',
    subtype: 'SubscriptionCustomerPaymentFailed',
    initialContent: {
      title: 'Subscription payment issue',
      html: '{{Name}} has a payment failure on their subscription. They might need help updating their info.',
      pushTitle: 'Subscription payment issue',
      pushDesc:
        '{{Name}} has a payment failure on their subscription. They might need help updating their info.',
    },
    isEnabled: true,
    receiverType: 'All',
    timing: [{ type: 'immediate' }],
  },
]
