name: Build for PROD

on:
  release:
    types: [published]

jobs:
  pre-build:
    name: Pre-build
    uses: ./.github/workflows/_section-pre-build.yml
    with:
      environment: Production
      migrate-db: true
    secrets: inherit

  show-release:
    name: Show release
    runs-on: ubuntu-22.04

    steps:
      - name: Show release
        shell: bash
        run: |
          echo "${{  github.ref_name }}"

  build-and-deploy-graphql-api:
    name: GraphqQL API
    needs: [pre-build, show-release]
    secrets: inherit
    with:
      docker-tag-hash: ${{ github.sha }}
      docker-tag-name: latest
      track-deployment: true
      workspace-project-name: graphql-api
    uses: ./.github/workflows/_section-build-and-deploy.yml

  build-and-deploy-shopify-app:
    name: Shopify App (Docker)
    needs: [pre-build, show-release]
    secrets: inherit
    with:
      docker-tag-hash: ${{ github.sha }}
      docker-tag-name: latest
      track-deployment: true
    uses: ./.github/workflows/_section-build-and-deploy-shopify.yml

#  deploy-admin-site:
#    name: Deploy Admin site
#    needs: [pre-build, show-release]
#    secrets: inherit
#    uses: ./.github/workflows/_section-deploy-admin.yml
#    with:
#      environment: Production

  deploy-shopify-app:
    name: Deploy Shopify App (Shopify Platform)
    needs: [pre-build, show-release]
    secrets: inherit
    with:
      environment: production
      config-file: shopify.app.prod.toml
      auto-release: false
      track-deployment: true
    uses: ./.github/workflows/_section-deploy-shopify-app.yml
