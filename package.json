{"name": "emma", "version": "1.0.0", "private": true, "workspaces": ["packages/*", "configs/*", "apps/*"], "scripts": {"build": "turbo run build --env-mode=loose", "build:graphql-api": "turbo run check-types build --filter=@emma/graphql-api... --color --env-mode=loose", "build:landing": "turbo run build --filter=@emma/landing... --color --env-mode=loose", "build:admin": "turbo run build --filter=@emma/admin... --color --env-mode=loose", "check-types": "turbo run check-types --color", "check-types:admin": "turbo run check-types --filter=@emma/admin... --color", "clean": "rimraf ./out && turbo run clean && rimraf ./.turbo", "clean:node_modules": "turbo run clean:node_modules && rimraf ./node_modules", "clean:reset:all": "git clean -fdx -e .", "dev:mcp": "turbo run dev --concurrency=25 --filter=@emma/emma-mcp... --color", "dev:graphql-api": "turbo run dev --concurrency=25 --filter=@emma/graphql-api... --filter=!@emma/emma-mcp --color", "dev:landing": "turbo run dev --filter=@emma/landing... --color", "dev:content-share": "turbo run dev --filter=@emma/content-share... --color", "dev:admin": "turbo run dev --concurrency=25 --filter=@emma/admin... --color", "dev:shopify-app": "turbo run dev --concurrency=25 --filter=@emma/shopify-app... --filter=@emma/shopify-theme... --color", "dev:setup": "turbo run dev:setup", "codegen": "turbo run codegen", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "lint": "turbo run lint", "lint-staged": "lint-staged", "test:unit": "NODE_ENV=test turbo run test:unit", "test:unit:admin": "turbo run test:unit  --filter=@emma/admin... --color", "prepare": "husky", "storage:migrate": "turbo run storage:migrate --filter=@emma/storage... --color --env-mode=loose", "storybook": "turbo run storybook"}, "dependencies": {"@dotenv-run/cli": "^1.3.6", "autoprefixer": "~10.4.21", "postcss": "^8.4.45", "tailwindcss": "^3.4.17", "tsx": "~4.19.3"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/ts-config": "workspace:*", "@repo/build-version": "workspace:*", "@types/bcrypt": "^5.0.2", "@types/node": "^20.16.2", "husky": "^9.1.7", "lint-staged": "^15.5.1", "prettier": "^3.5.3", "rimraf": "^6.0.1", "ts-node": "10.9.2", "tsup": "8.4.0", "turbo": "^2.5.1", "typescript": "^5.6.2"}, "packageManager": "pnpm@9.15.0", "engines": {"node": ">=20.0.0"}}