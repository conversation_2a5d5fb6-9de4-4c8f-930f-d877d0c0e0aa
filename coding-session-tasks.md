# Context-based Authentication Implementation Tasks

## 1. Update Authentication Middleware (JWT-only)

- [x] Remove AsyncLocalStorage dependency
- [x] Simplify auth middleware to only extract tokens from Authorization header
- [x] Update middleware to attach user info directly to request object
- [x] Remove all query parameter token extraction
- [x] Remove any global state for authentication

## 2. Implement Streamable HTTP Transport

- [x] Add `StreamableHTTPServerTransport` from MCP SDK
- [x] Configure transport without session management (stateless mode)
- [x] Replace existing SSE transport implementation
- [x] Create a unified endpoint for all MCP requests (GET, POST, DELETE)

## 3. Update Tool Implementations

- [x] Modify tool handlers to get auth info from `extra.request.user`
- [x] Remove all references to AsyncLocalStorage or getCurrentAuthContext()
- [x] Update error handling for authentication failures
- [x] Ensure tool handlers properly validate authentication

## 4. Clean Up Legacy Code

- [x] Remove SSE-specific endpoints and handlers
- [x] Remove toolCallAuthMiddleware and other custom auth middleware
- [x] Remove session token persistence logic
- [x] Clean up any unused imports and variables

## 5. Update Server Configuration

- [x] Update Express app setup with streamlined middleware
- [x] Configure the server as truly stateless
- [x] Set proper HTTP status codes for auth errors (401, 403)
- [x] Add appropriate CORS headers if needed

## 6. Update Client-Side Implementation

- [x] Update McpClientManager to use StreamableHTTPClientTransport
- [x] Ensure all tool calls include the Authorization header
- [x] Update client error handling for authentication failures
- [x] Add proper cleanup for client instances

## 7. Simplify With Standard SDK Features

- [x] Remove custom transport wrapper in favor of standard MCP SDK
- [x] Pass Authorization headers directly in each tool call
- [x] Fix connection issues by only calling connect() once
- [x] Update documentation to reflect simplified approach

## 8. Testing & Documentation

- [ ] Test authentication with valid JWT tokens
- [ ] Test authentication failures (expired/invalid token)
- [ ] Test unauthenticated requests
- [ ] Test tool authorization logic
- [ ] Update documentation to reflect the new authentication flow
