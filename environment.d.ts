declare global {
  namespace NodeJS {
    // eslint-disable-next-line @typescript-eslint/consistent-type-definitions
    interface ProcessEnv {
      JWT_SECRET_KEY: string
      EMAIL_SEND_API: string
      AUTH_ACCESS_SECRET_KEY: string
      AUTH_REFRESH_SECRET_KEY: string
      SHARE_CONTENT_URL: string
      MCP_MODEL_NAME: string
      REDIS_URL: string
      LOGTAIL_SOURCE_TOKEN: string
      SENTRY_DSN: string
      LOGGER_TRANSPORT: 'console' | 'logtail' | 'sentry'
      NODE_ENV: 'development' | 'production' | 'test'
    }
  }
}

// If this file has no import/export statements (i.e. is a script)
// convert it into a module by adding an empty export statement.
export {}
