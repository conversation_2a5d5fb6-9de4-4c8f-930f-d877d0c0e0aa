{"$schema": "https://turbo.build/schema.json", "globalDependencies": [".env", ".env.development", ".env.production"], "globalPassThroughEnv": ["FORM_WEBHOOK", "FEEDBACK_WEBHOOK", "VITE_EMMA_ADMIN_URL", "VITE_EMMA_ADMIN_ENDPOINT_API", "VITE_EMMA_LANDING_URL", "EMAIL_SEND_API", "JWT_SECRET_KEY", "AUTH_ACCESS_SECRET_KEY", "AUTH_REFRESH_SECRET_KEY", "SHARE_CONTENT_URL", "DIRECT_URL", "REDIS_URL", "MCP_MODEL_NAME", "DATABASE_URL", "LOGGER_TRANSPORT", "VITE_BASE_URL", "OPENAI_API_KEY", "EXPO_ACCESS_TOKEN", "OBJECT_STORAGE_ENDPOINT", "OBJECT_STORAGE_ACCESS_KEY", "OBJECT_STORAGE_SECRET_KEY", "OBJECT_STORAGE_REGION", "GA_TRACKING_ID", "MCP_HTTP_PORT", "ENABLE_STDIO", "NODE_ENV", "MCP_SERVER_URL", "MCP_STDIO_COMMAND", "MCP_STDIO_ARGS", "MCP_STDIO_CWD", "GA_TRACKING_ID", "SENTRY_DSN", "SENTRY_AUTH_TOKEN", "SENTRY_ORG", "SENTRY_PROJECT", "SHOPIFY_APP_CONFIG"], "tasks": {"build": {"dependsOn": ["^build"], "outputs": ["dist/**", "build/**", ".cache", ".next/**", "!.next/cache/**"]}, "clean": {"cache": false, "dependsOn": ["^clean"]}, "clean:node_modules": {"cache": false, "dependsOn": ["^clean:node_modules"]}, "check-types": {"cache": false}, "test:unit": {"cache": false, "outputs": [], "dependsOn": ["@repo/validator#build", "@repo/result#build", "@repo/date#build"]}, "@emma/graphql-api#check-types": {"dependsOn": ["@emma/storage#build", "@emma/s3#build", "@emma/jobs#build"], "cache": false}, "@emma/jobs#check-types": {"dependsOn": ["@emma/storage#build"], "cache": false}, "@emma/emma-mcp#check-types": {"dependsOn": ["@emma/storage#build"], "cache": false}, "@emma/emma-mcp#dev": {"dependsOn": ["@emma/emma-mcp#build"], "cache": false}, "@emma/storage#check-types": {"dependsOn": ["@emma/storage#build"], "cache": false}, "@emma/emma-mcp#build:external": {"cache": false}, "@emma/graphql-api#dev": {"dependsOn": ["@emma/storage#prisma:generate", "@emma/s3#build", "@emma/emma-mcp#build:external"], "cache": false}, "@emma/storage#prisma:generate": {"cache": false, "outputs": []}, "@emma/s3#dev:setup": {"cache": false, "outputs": []}, "@emma/shopify-app#check-types": {"dependsOn": ["@emma/storage#build", "@repo/result#build", "@repo/sender#build"], "cache": false}, "@emma/shopify-app#build": {"dependsOn": ["@emma/storage#build", "@repo/result#build", "@repo/sender#build"]}, "@emma/shopify-app#dev": {"dependsOn": ["@emma/storage#prisma:generate"], "cache": false, "env": ["SHOPIFY_APP_CONFIG"]}, "@emma/shopify-theme#dev": {"cache": false, "outputs": []}, "dev": {"cache": false, "persistent": true}, "lint": {"cache": false}, "storage:migrate": {"cache": false}, "codegen": {"cache": false}, "storybook": {"cache": false, "dependsOn": ["@repo/date#build"]}, "dev:setup": {"cache": false}}}