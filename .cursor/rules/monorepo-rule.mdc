---
description: 
globs: 
alwaysApply: true
---
# Role

- You are a helpful assistant for a TypeScript monorepo project named <PERSON>. 
- You help with coding questions, architecture decisions and best practices for this project.
- This monorepo uses GraphQL with Apollo Server, TypeGraphQL and Express. 
- It also uses Prisma for database interactions. 
- JSON Web Tokens handle authentication. 
- Docker is used for containerization. 
- ESLint and TypeScript enforce code quality.
- Ensure consistent use of ESLint and Typescript configuration when modifying existing or creating new apps.
-- This means leveraging the same tsconfig.json inheriting from base, as well as eslint configuration

## Tech stack:

- Typescript
- Node.js
- Pnpm as package manager
- GraphQL (with graphql-yoga for subscriptions)
- Prisma (ORM)
- Utilities: faker.js, dotenv, nodemon

## Monorepo structure

The monorepo structure includes multiple apps like emma-admin, emma-landing and graphql-api. It also has shared packages like date, errors and logger.

Entire monorepo is managed with pnpm. Here's a monorepo structure:

```
.
├── Dockerfile
├── README.md
├── apps
│   ├── emma-admin
│   ├── emma-landing
│   ├── graphql-api
│   ├── graphql-code
│   ├── s3
│   └── storage
├── configs
│   ├── eslint
│   ├── tailwind-config
│   └── ts
├── environment.d.ts
├── package.json
├── packages
│   ├── build-version
│   ├── date
│   ├── errors
│   ├── lang-i18n
│   ├── logger
│   ├── result
│   ├── service
│   ├── storybook
│   ├── ui-kit
│   ├── unit-test
│   └── validator
├── pnpm-lock.yaml
├── pnpm-workspace.yaml
├── supabase
│   ├── config.toml
│   └── seed.sql
├── turbo.json
└── vitest.workspace.ts
```

Use kebab-case for file names.

## Directory Hierarchy

- **apps/graphql-api**: Contains the GraphQL API server code.
- **src/modules**: Houses different modules like server, otp, company, etc., each containing resolvers, DTOs, and related logic.
- **src/server**: Contains server setup and middleware configurations.
- **apps/storage**: Contains business logic and database interaction code.
- **src/modules**: Organized by domain, such as company, contact, order, etc., each with use cases and infrastructure code.
- **prisma**: Contains Prisma schema and seed files for database setup.

## Coding guidelines

- Use @repo/result whenever required instead of writing custom error handling or for flow control.
- Do not make environment-specific functionality. For example don't provide fallback to default user id for development environment vs production.

