{"name": "@repo/unit-test", "version": "0.0.4", "private": true, "type": "module", "bin": {"repo-unit": "cli.cjs"}, "scripts": {"clean": "rimraf ./dist && rimraf .turbo", "clean:node_modules": "rimraf node_modules"}, "source": "./index.ts", "types": "./index.ts", "module": "./index", "dependencies": {"minimist": "^1.2.8", "vitest": "2.1.0", "vite-tsconfig-paths": "^5.0.1"}, "devDependencies": {"@dotenv-run/core": "^1.3.5", "@repo/eslint-config": "workspace:*", "@repo/ts-config": "workspace:*"}, "sideEffects": false}