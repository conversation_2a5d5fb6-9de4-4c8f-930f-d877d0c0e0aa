{"name": "@repo/date", "version": "0.0.2", "private": true, "sideEffects": false, "source": "src/index.ts", "types": "src/index.ts", "main": "dist/index.js", "module": "dist/index.mjs", "scripts": {"check-types": "tsc --noEmit", "clean": "rimraf dist ./.turbo", "clean:node_modules": "rimraf ./node_modules", "build": "tsup", "dev": "tsup --watch"}, "dependencies": {"date-fns": "4.1.0"}, "devDependencies": {"@repo/ts-config": "workspace:*", "@repo/eslint-config": "workspace:*"}}