import { type ChangeEvent, useCallback, useRef } from 'react'

import type { TimePickerValueType } from './time-picker.value-type'

type Props = {
  value?: TimePickerValueType
  onChange: (value: TimePickerValueType) => void
  isError?: boolean
  disabled?: boolean
}

const hours = Array(12).fill(null)
const minutes = Array(60).fill(null)

const selectClass =
  ' bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500 appearance-none [-webkit-appearance:none] [-moz-appearance:none]'

type DayLight = 'AM' | 'PM'

function getDefaultMin(val?: number): number {
  if (val !== undefined) return val
  const date = new Date()
  return date.getMinutes()
}

function getNextHour(dateTime: DayLight, val?: number): number {
  if (val === undefined) {
    const date = new Date()
    return date.getHours()
  }

  if (dateTime === 'AM') {
    if (val < 12) return val
    return Number(val) - 12
  }

  if (val >= 12) return val

  const nextHour = val + 12
  if (nextHour >= 24) return 0
  return nextHour
}

export function TimePickerDropdown({
  value,
  onChange,
  isError,
  disabled,
}: Props) {
  const dayTimeRef = useRef<DayLight>('AM')

  const handleChangeDayLight = useCallback(
    (e: ChangeEvent<HTMLSelectElement>) => {
      const val = e.target.value as DayLight
      dayTimeRef.current = val

      const nextHour = getNextHour(val, value?.h)

      onChange({
        h: nextHour,
        m: getDefaultMin(value?.m),
      })
    },
    [onChange, value],
  )

  const handleChangeHour = useCallback(
    (e: ChangeEvent<HTMLSelectElement>) => {
      const val = Number(e.target.value)

      const nextHour = getNextHour(dayTimeRef.current, val)

      onChange({
        h: nextHour,
        m: getDefaultMin(value?.m),
      })
    },
    [onChange, value],
  )

  const handleChangeMin = useCallback(
    (e: ChangeEvent<HTMLSelectElement>) => {
      const val = Number(e.target.value)
      onChange({
        h: value?.h === undefined ? 0 : value.h,
        m: val,
      })
    },
    [onChange, value],
  )
  const checkVal = value?.h && value.h > 12 ? value.h - 12 : value?.h

  const errorClass = isError ? 'border-red-600 bg-red-50' : ''
  const disabledClass = disabled ? 'opacity-50 cursor-not-allowed' : ''

  return (
    <div className="flex w-52 gap-2">
      <select
        className={`${selectClass} ${errorClass} ${disabledClass}`}
        onChange={handleChangeHour}
        value={checkVal}
        disabled={disabled}
      >
        {hours.map((_, key) => {
          const val = key
          const usedVal = val === 0 ? '12' : val
          return (
            <option key={key} value={val}>
              {usedVal}
            </option>
          )
        })}
      </select>
      <select
        className={`${selectClass} ${errorClass} ${disabledClass}`}
        onChange={handleChangeMin}
        value={value?.m}
        disabled={disabled}
      >
        {minutes.map((_, key) => {
          const val = key
          const usedVal = val < 10 ? `0${val}` : val
          return (
            <option key={key} value={key}>
              {usedVal}
            </option>
          )
        })}
      </select>
      <select
        className={`${selectClass} ${errorClass} ${disabledClass}`}
        onChange={handleChangeDayLight}
        value={(!!value?.h && value.h < 12) || checkVal === 0 ? 'AM' : 'PM'}
        disabled={disabled}
      >
        <option value="AM">AM</option>
        <option value="PM">PM</option>
      </select>
    </div>
  )
}
