import { Children, useMemo } from 'react'

import { useClickOutside } from '../hooks/use-click-outside'
import { Label } from '../label'

import { ContainerContext } from './container.context'
import { DropdownContainerOption } from './dropdown-container-option'
import { heightMap } from './height-map'
import { placementMap } from './placement-map'
import { useContainer } from './use-container'
import { widthMap } from './width-map'

import type { ReactNode } from 'react'
import type {
  ContainerHeightType,
  DropdownContainerType,
  Placement,
  WidthType,
} from './types'

type Props = {
  children?: ReactNode
  footerSlot?: ReactNode
  headerSlot?: ReactNode
  heightType?: ContainerHeightType
  label?: ReactNode
  onOutsideClick: () => void
  placement?: Placement
  placeSlot?: ReactNode
  type?: DropdownContainerType
  widthType?: WidthType
}

const borderMap: Partial<Record<DropdownContainerType, string>> = {
  landing:
    'bg-white border-2 border-black rounded-lg shadow-landing-burger-menu',
}

const layoutMap: Partial<Record<DropdownContainerType, string>> = {
  app: 'flex-1 overflow-y-auto text-left',
  landing: 'flex flex-col justify-between pt-10 pb-6',
}

function DropdownContainerBase({
  children,
  footerSlot,
  headerSlot,
  heightType = 'lg',
  label,
  onOutsideClick,
  placement = 'middle',
  placeSlot,
  type = 'app',
  widthType = 'none',
}: Props) {
  const borderClasses = borderMap[type] || ''
  const layoutClasses = layoutMap[type] || ''

  const { context } = useContainer({
    onClose: onOutsideClick,
  })

  const heightClass = heightMap[heightType]
  const widthClass = widthMap[widthType]
  const placementClass = placementMap[placement]

  const isChildrenExist = useMemo(() => {
    const arrayChildren = Children.toArray(children)
    return arrayChildren.length > 0
  }, [children])

  const { ref } = useClickOutside(onOutsideClick)

  return (
    <ContainerContext.Provider value={context}>
      <div className="relative" ref={ref}>
        <Label isTruncated title={label}>
          <div className="relative">{placeSlot}</div>
        </Label>
        {!!isChildrenExist && (
          <div
            className={`z-10 bg-white mt-2 rounded-lg absolute border border-gray-200 shadow flex flex-col ${heightClass} ${widthClass} ${placementClass}`}
          >
            {headerSlot}
            <ul className={`${layoutClasses} ${borderClasses}`}>{children}</ul>
            {footerSlot}
          </div>
        )}
      </div>
    </ContainerContext.Provider>
  )
}

export const DropdownContainer = Object.assign(DropdownContainerBase, {
  Option: DropdownContainerOption,
})
