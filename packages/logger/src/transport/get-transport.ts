const transport = process.env.LOGGER_TRANSPORT

export function getTransport() {
  switch (transport) {
    case 'logtail':
      // eslint-disable-next-line eslint-comments/no-restricted-disable
      // eslint-disable-next-line @typescript-eslint/no-require-imports, @typescript-eslint/no-var-requires
      return require('./logtail').createTransport()
    case 'sentry':
      // eslint-disable-next-line eslint-comments/no-restricted-disable
      // eslint-disable-next-line @typescript-eslint/no-require-imports, @typescript-eslint/no-var-requires
      return require('./sentry').createTransport()
    case 'console':
      // eslint-disable-next-line eslint-comments/no-restricted-disable
      // eslint-disable-next-line @typescript-eslint/no-var-requires,@typescript-eslint/no-require-imports
      return require('./console').createTransport()
  }
}
