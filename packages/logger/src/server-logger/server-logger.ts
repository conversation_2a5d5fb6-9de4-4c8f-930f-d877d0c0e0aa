import { Logger } from '../base-logger/logger'

import { getSharedStore } from './lcid-storage'

import type { LogParams } from '../core/log-params'

type Details = LogParams['details']

function getDetails(details?: Details): Record<string, unknown> {
  if (!details) return {}
  if (details instanceof Error)
    return { error: details.message, stack: details.stack }
  if (typeof details === 'object') return details
  return { details }
}

export class ServerLogger extends Logger {
  override getArgs(message: string, details?: Details): LogParams {
    const { cacheStore } = getSharedStore() || {}

    const usedLcid = this.lcid
    const prefix = this.section ? `${this.name} in ${this.section}` : this.name

    const cacheValues = cacheStore?.values

    cacheStore?.flush()

    const mergedDetails = {
      ...getDetails(details),
      ...cacheValues,
    }

    const mergedMessage = `[${prefix}] ${usedLcid} ${message}`.trim()

    if (Object.keys(mergedDetails).length === 0) {
      return {
        message: mergedMessage,
      }
    }

    return {
      message: mergedMessage,
      details: mergedDetails,
    }
  }

  get lcid(): string {
    const lcid = this.rawLcid

    const usedLcid = lcid ? `[_${lcid}]` : '-'
    return usedLcid
  }

  get rawLcid(): string | undefined {
    const { lcid } = getSharedStore() || {}
    return lcid
  }

  appendCallStep(fields: Record<string, unknown>): void {
    const existStore = getSharedStore()

    if (!existStore) return
    existStore.cacheStore.appendByKey(this.name, fields)
  }

  updateContext(fields: Record<string, unknown>): void {
    const existStore = getSharedStore()

    if (!existStore) return
    existStore.cacheStore.updateValue('ctx', fields)
  }

  get context(): Record<string, unknown> | undefined {
    const existStore = getSharedStore()
    return existStore?.cacheStore.values['ctx'] as Record<string, unknown>
  }
}
